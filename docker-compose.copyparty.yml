version: '3.8'

services:
  # Copyparty File Server - Standalone Configuration
  copyparty:
    image: ghcr.io/9001/copyparty:latest
    container_name: combat-mirror-copyparty
    restart: unless-stopped
    
    # Port mappings
    ports:
      - "3923:3923"  # HTTP/WebDAV
      - "3990:3990"  # FTP (optional)
      - "1033:1033"  # TFTP (optional)
    
    # Environment configuration
    environment:
      # Copyparty arguments
      - ARGS=-i :: -p 3923 --rw /srv/uploads,/srv/recordings,/srv/exports --cors --theme 2 --name "Combat Mirror File Server" --no-robots --ed --em --et --e2dsa --e2ts --s --th-poke --no-thumb-svg --j-part-sz 32 --j-threads 4
    
    # Volume mappings - persistent storage
    volumes:
      # Video storage directories
      - ./uploads:/srv/uploads
      - ./recordings:/srv/recordings
      - ./exports:/srv/exports
      
      # Copyparty database and metadata
      - ./copyparty-db:/srv/.hist
      
      # Configuration directory
      - ./copyparty-config:/srv/config
      
      # Thumbnails cache
      - ./copyparty-thumbs:/srv/.th
    
    # Health check
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3923/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    
    # Resource limits (optional - adjust based on your system)
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '0.5'
          memory: 512M
    
    # Network
    networks:
      - combat-mirror-network

# Network definition
networks:
  combat-mirror-network:
    driver: bridge
    name: combat-mirror-network

# Named volumes for better management (optional)
volumes:
  uploads:
    driver: local
  recordings:
    driver: local
  exports:
    driver: local
  copyparty-db:
    driver: local
# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

Combat Mirror System is a React-based research application for analyzing combat sports athlete performance using multi-camera systems and real-time pose estimation. The application captures and analyzes biomechanical metrics, generates AI-powered performance reports using Google Gemini, and manages video storage with offline-first capabilities.

## Common Development Commands

```bash
# Install dependencies (using npm, yarn, or bun)
npm install
# or
yarn install
# or
bun install

# Start development server
npm run dev         # Runs Vite dev server

# Build for production
npm run build       # Creates production build

# Preview production build
npm run preview     # Serves the production build locally
```

## Environment Setup

Before running the application, you must set up your environment:

1. Create a `.env.local` file in the root directory
2. Add your Gemini API key:
   ```
   GEMINI_API_KEY=your_gemini_api_key_here
   ```

## High-Level Architecture

### Technology Stack
- **Frontend Framework**: React 19 with TypeScript
- **Build Tool**: Vite
- **Styling**: Inline styles and CSS modules
- **AI Integration**: Google Gemini AI (gemini-2.5-flash-preview-04-17)
- **Local Storage**: IndexedDB for video persistence
- **State Management**: React hooks (useState, useEffect)

### Core Components Structure

1. **Main Application** (`App.tsx`)
   - Orchestrates the entire application state
   - Manages session lifecycle (Idle → Active → Analyzing → Finished)
   - Handles metrics simulation during active sessions
   - Coordinates video upload and storage

2. **Services Layer**
   - `geminiService.ts`: Interfaces with Gemini AI for generating performance analysis reports
   - `videoStorageService.ts`: Manages video storage with IndexedDB persistence and sync queue for cloud upload simulation

3. **Component Architecture**
   - `Dashboard`: Main control interface for session management and metrics display
   - `CameraGrid`: Multi-camera view display
   - `MetricsDisplay`: Real-time metrics visualization
   - `SessionControl`: Session type selection and control buttons
   - `VideoUpload` & `VideoList`: Video management interface
   - `ReportModal`: Display AI-generated analysis reports

### Key Data Types

- **SessionState**: Idle | Active | Analyzing | Finished
- **SessionType**: Baseline (5 mins) | Training (15 mins) | Recovery (5 mins)
- **Metrics**: Core biomechanical measurements including:
  - punchRate, punchCount, punchVelocity
  - headMovement, postureScore, gaitBalance
  - fatigue index

### Video Storage Architecture

The application implements an offline-first video storage system:
- Videos are stored locally in IndexedDB with metadata
- Automatic sync queue processes uploads to cloud storage
- Failed syncs are retried every 30 seconds
- Videos persist across browser sessions

### Environment Variable Handling

The Vite configuration (`vite.config.ts`) exposes the Gemini API key to the application:
- Maps `GEMINI_API_KEY` from `.env.local` to `process.env.API_KEY`
- Also available as `process.env.GEMINI_API_KEY`

### Path Aliases

TypeScript is configured with path aliases:
- `@/*` maps to the root directory
- Example: `import { SessionType } from '@/types'`
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Combat Mirror - Mobile Camera</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://unpkg.com/livekit-client@latest/dist/livekit-client.umd.js"></script>
    <style>
        body { 
            margin: 0; 
            padding: 0; 
            background: #000; 
            overflow: hidden;
        }
        #video-container {
            position: relative;
            width: 100vw;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        #local-video {
            width: 100%;
            height: 100%;
            object-fit: cover;
            transform: scaleX(-1); /* Mirror video */
        }
        #controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }
        .control-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 25px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .connect-btn {
            background: #10B981;
            color: white;
        }
        .disconnect-btn {
            background: #EF4444;
            color: white;
        }
        #status {
            position: absolute;
            top: 20px;
            left: 20px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 10px 15px;
            border-radius: 10px;
            font-size: 14px;
        }
        #position-indicator {
            position: absolute;
            top: 20px;
            right: 20px;
            background: rgba(16, 185, 129, 0.9);
            color: white;
            padding: 8px 12px;
            border-radius: 20px;
            font-weight: bold;
            font-size: 16px;
        }
    </style>
</head>
<body>
    <div id="video-container">
        <video id="local-video" autoplay muted playsinline></video>
        <div id="status">Initializing...</div>
        <div id="position-indicator"></div>
        <div id="controls">
            <button id="connect-btn" class="control-btn connect-btn">Connect</button>
            <button id="disconnect-btn" class="control-btn disconnect-btn" style="display: none;">Disconnect</button>
        </div>
    </div>

    <script>
        // Parse URL parameters
        const urlParams = new URLSearchParams(window.location.search);
        const roomName = urlParams.get('room');
        const token = urlParams.get('token');
        const position = urlParams.get('position') || 'UNKNOWN';

        // UI elements
        const localVideo = document.getElementById('local-video');
        const status = document.getElementById('status');
        const positionIndicator = document.getElementById('position-indicator');
        const connectBtn = document.getElementById('connect-btn');
        const disconnectBtn = document.getElementById('disconnect-btn');

        // Set position indicator
        positionIndicator.textContent = position + ' CAMERA';

        let room;
        let isConnected = false;

        // Update status
        function updateStatus(message, isError = false) {
            status.textContent = message;
            status.style.background = isError ? 'rgba(239, 68, 68, 0.9)' : 'rgba(0,0,0,0.7)';
        }

        // Get user media
        async function getUserMedia() {
            try {
                const stream = await navigator.mediaDevices.getUserMedia({
                    video: {
                        width: { ideal: 1280 },
                        height: { ideal: 720 },
                        facingMode: 'user' // Front camera
                    },
                    audio: false
                });
                localVideo.srcObject = stream;
                return stream;
            } catch (error) {
                updateStatus('Camera access denied', true);
                throw error;
            }
        }

        // Connect to LiveKit room
        async function connect() {
            if (!roomName || !token) {
                updateStatus('Missing room or token', true);
                return;
            }

            try {
                updateStatus('Getting camera access...');
                const stream = await getUserMedia();

                updateStatus('Connecting to room...');
                room = new LiveKit.Room();

                // Set up event handlers
                room.on(LiveKit.RoomEvent.Connected, () => {
                    updateStatus('Connected - Streaming live!');
                    isConnected = true;
                    connectBtn.style.display = 'none';
                    disconnectBtn.style.display = 'block';
                });

                room.on(LiveKit.RoomEvent.Disconnected, () => {
                    updateStatus('Disconnected');
                    isConnected = false;
                    connectBtn.style.display = 'block';
                    disconnectBtn.style.display = 'none';
                });

                room.on(LiveKit.RoomEvent.LocalTrackPublished, (publication) => {
                    updateStatus('Video track published');
                });

                // Determine LiveKit server URL
                const livekitUrl = roomName.includes('localhost') ? 
                    'ws://localhost:7880' : 
                    'wss://your-livekit-server.railway.app'; // Update this URL

                // Connect to room
                await room.connect(livekitUrl, token);

                // Publish video track
                const videoTrack = await LiveKit.createLocalVideoTrack({
                    resolution: LiveKit.VideoPresets.h720.resolution,
                });
                await room.localParticipant.publishTrack(videoTrack);

            } catch (error) {
                console.error('Connection failed:', error);
                updateStatus('Connection failed: ' + error.message, true);
            }
        }

        // Disconnect from room
        async function disconnect() {
            if (room) {
                await room.disconnect();
                room = null;
            }
            
            // Stop camera stream
            if (localVideo.srcObject) {
                localVideo.srcObject.getTracks().forEach(track => track.stop());
                localVideo.srcObject = null;
            }
            
            updateStatus('Disconnected');
            isConnected = false;
            connectBtn.style.display = 'block';
            disconnectBtn.style.display = 'none';
        }

        // Event listeners
        connectBtn.addEventListener('click', connect);
        disconnectBtn.addEventListener('click', disconnect);

        // Prevent screen from sleeping on mobile
        if ('wakeLock' in navigator) {
            navigator.wakeLock.request('screen').catch(console.warn);
        }

        // Handle page visibility changes
        document.addEventListener('visibilitychange', () => {
            if (document.hidden && isConnected) {
                updateStatus('App backgrounded - may disconnect');
            } else if (!document.hidden && isConnected) {
                updateStatus('Connected - Streaming live!');
            }
        });

        // Initialize
        updateStatus('Ready to connect to ' + position + ' camera position');
        
        // Auto-connect if parameters are present
        if (roomName && token) {
            // Add small delay to ensure UI is ready
            setTimeout(() => {
                connectBtn.click();
            }, 1000);
        } else {
            updateStatus('Missing room or token parameters', true);
        }
    </script>
</body>
</html>
#!/bin/bash

# Combat Mirror System - Production Deployment Script
set -e

echo "🚀 Combat Mirror System - Production Deployment"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Check for required tools
command -v railway >/dev/null 2>&1 || { echo -e "${RED}❌ Railway CLI is required but not installed.${NC}" >&2; exit 1; }
command -v npm >/dev/null 2>&1 || { echo -e "${RED}❌ npm is required but not installed.${NC}" >&2; exit 1; }

# Check for environment variables
if [ -z "$GEMINI_API_KEY" ]; then
    if [ -f .env.local ]; then
        export $(grep GEMINI_API_KEY .env.local | xargs)
    fi
    
    if [ -z "$GEMINI_API_KEY" ]; then
        echo -e "${RED}❌ GEMINI_API_KEY not found. Please set it in .env.local or as environment variable${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}✅ Environment variables loaded${NC}"

# Build the application
echo -e "${YELLOW}📦 Building application...${NC}"
npm run build

# Create railway.toml for deployment configuration
cat > railway.toml << EOF
[build]
builder = "nixpacks"
buildCommand = "npm install && npm run build"

[deploy]
startCommand = "npm run start:all"
healthcheckPath = "/api/health"
healthcheckTimeout = 300
restartPolicyType = "on-failure"
restartPolicyMaxRetries = 3

[environments.production]
NODE_ENV = "production"
EOF

echo -e "${GREEN}✅ Railway configuration created${NC}"

# Login to Railway (if not already logged in)
echo -e "${YELLOW}🔐 Checking Railway authentication...${NC}"
railway whoami || railway login

# Link to project (create new if doesn't exist)
echo -e "${YELLOW}🔗 Linking to Railway project...${NC}"
if [ ! -f .railway/config.json ]; then
    railway link
fi

# Set environment variables
echo -e "${YELLOW}⚙️  Setting production environment variables...${NC}"
railway variables set GEMINI_API_KEY="$GEMINI_API_KEY" --environment production
railway variables set NODE_ENV="production" --environment production
railway variables set VITE_API_URL="https://\${{RAILWAY_PUBLIC_DOMAIN}}" --environment production
railway variables set PORT="3000" --environment production

# Deploy to production
echo -e "${YELLOW}🚂 Deploying to Railway production...${NC}"
railway up --environment production

# Get deployment URL
echo -e "${GREEN}✅ Deployment complete!${NC}"
echo ""
echo "📱 Your application is being deployed to Railway."
echo "   Check the deployment status:"
echo "   railway logs --environment production"
echo ""
echo "🌐 Once deployed, access your app at:"
railway domain --environment production

# Verify deployment
echo ""
echo -e "${YELLOW}🔍 Waiting for deployment to be ready...${NC}"
sleep 10

# Check health endpoint
DOMAIN=$(railway domain --environment production | grep -o 'https://[^ ]*' | head -1)
if [ ! -z "$DOMAIN" ]; then
    echo -e "${YELLOW}🏥 Checking health endpoint: $DOMAIN/api/health${NC}"
    curl -s "$DOMAIN/api/health" | python3 -m json.tool || echo -e "${YELLOW}⚠️  Health check pending...${NC}"
fi

echo ""
echo -e "${GREEN}🎉 Production deployment script completed!${NC}"
echo "   Monitor logs: railway logs -f --environment production"
echo "   Open app: railway open --environment production"
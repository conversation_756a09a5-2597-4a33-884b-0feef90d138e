# Combat Mirror iOS Production Build Guide

## 📱 App Information
- **App Name**: Combat Mirror Camera
- **Bundle ID**: com.aliaslabs.combatmirrorsystem
- **Version**: 1.0.0
- **Platform**: React Native Expo
- **EAS Project ID**: b6df7343-053a-492b-869d-45a0d9a3e3dd

## 🚀 Quick Build Commands

```bash
# Navigate to iOS app directory
cd /Users/<USER>/Documents/CombatMirrorProjects/combatmirroranalysis/combat-mirror-mobile

# Install dependencies
npm install

# Install EAS CLI globally (if not already installed)
npm install -g eas-cli

# Login to Expo account
eas login

# Build for production
eas build --platform ios --profile production

# Submit to App Store (after build completes)
eas submit --platform ios --latest
```

## 📋 Prerequisites

1. **Apple Developer Account** ($99/year)
   - Enroll at: https://developer.apple.com/programs/
   - Create App ID for bundle identifier: com.aliaslabs.combatmirrorsystem
   - Create provisioning profiles

2. **Expo Account** (free)
   - Sign up at: https://expo.dev/
   - Already configured with project ID: b6df7343-053a-492b-869d-45a0d9a3e3dd

3. **Development Environment**
   - Node.js 18+ 
   - npm or yarn
   - EAS CLI: `npm install -g eas-cli`
   - (Optional) Xcode for local builds

## 🔧 Production Configuration

### 1. Environment Variables
Create `.env.production` in the iOS app directory:

```bash
# Production API endpoints
EXPO_PUBLIC_API_URL=https://combat-mirror-system.up.railway.app/api
EXPO_PUBLIC_LIVEKIT_URL=wss://combat-mirror-livekit.up.railway.app
EXPO_PUBLIC_GEMINI_API_KEY=your_production_gemini_key_here
```

### 2. Update app.json
Before each production build, update version and buildNumber:

```json
{
  "expo": {
    "version": "1.0.1",  // Increment for each release
    "ios": {
      "buildNumber": "2"  // Increment for each build
    }
  }
}
```

### 3. Configure eas.json
Update with your Apple credentials:

```json
{
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "1234567890",  // App Store Connect App ID
        "appleTeamId": "ABCDEF1234"  // Apple Team ID
      }
    }
  }
}
```

## 🏗️ Build Process

### Option 1: EAS Cloud Build (Recommended)
```bash
# Production build for App Store
eas build --platform ios --profile production

# Internal testing build
eas build --platform ios --profile preview

# Development build with dev client
eas build --platform ios --profile development
```

### Option 2: Local Build with Xcode
```bash
# Generate native iOS project
npx expo prebuild

# Install iOS dependencies
cd ios && pod install

# Open in Xcode
open ios/CombatMirrorCamera.xcworkspace

# In Xcode:
# 1. Select "Any iOS Device" as target
# 2. Product → Archive
# 3. Distribute App → App Store Connect
```

## 📲 Distribution Options

### TestFlight (Beta Testing)
1. Build with production profile
2. Submit to TestFlight:
   ```bash
   eas submit --platform ios --latest
   ```
3. Add internal/external testers in App Store Connect
4. Testers receive invitation via email

### App Store Release
1. Complete app information in App Store Connect
2. Upload screenshots for all device sizes
3. Write app description and keywords
4. Submit for review

## 🔍 Build Monitoring

### Check Build Status
```bash
# View all builds
eas build:list --platform ios

# View specific build details
eas build:view [build-id]
```

### Download Build Artifacts
1. Go to https://expo.dev/
2. Navigate to your project
3. Click on Builds
4. Download .ipa file

## 🛠️ Troubleshooting

### Common Issues

1. **Provisioning Profile Errors**
   - Ensure Apple Developer account is active
   - Check bundle ID matches in Apple Developer Portal
   - Run `eas credentials` to manage certificates

2. **Build Failures**
   - Check build logs: `eas build:view [build-id]`
   - Verify all dependencies are compatible
   - Clear cache: `npx expo start --clear`

3. **Submission Errors**
   - Verify App Store Connect app is created
   - Check all required app information is complete
   - Ensure app icons and screenshots are provided

### Useful Commands
```bash
# Manage credentials
eas credentials

# Clear build cache
eas build:cancel [build-id]

# View build configuration
eas build:inspect --platform ios --profile production
```

## 📊 Performance Optimization

### Production Build Optimizations
- Enable Hermes JavaScript engine (already configured)
- Use production Metro bundler settings
- Minimize bundle size with tree shaking
- Enable ProGuard/R8 for Android (if building for both platforms)

### App Size Reduction
- Review and remove unused dependencies
- Optimize image assets
- Use dynamic imports for large features
- Enable app thinning in Xcode

## 🔒 Security Checklist

- [ ] Remove all console.log statements
- [ ] Ensure API keys are in environment variables
- [ ] Enable certificate pinning for API calls
- [ ] Implement proper authentication flow
- [ ] Add privacy policy URL in app.json
- [ ] Configure proper data encryption

## 📝 Pre-submission Checklist

- [ ] Test on real devices (iPhone 12+ recommended)
- [ ] Verify camera permissions work correctly
- [ ] Test QR code scanning functionality
- [ ] Ensure LiveKit connection is stable
- [ ] Check all UI elements on different screen sizes
- [ ] Verify app works offline (graceful degradation)
- [ ] Test background/foreground transitions
- [ ] Validate deep linking works
- [ ] Review App Store guidelines compliance

## 🚀 Next Steps

1. **Run the build script**: `./ios-production-build.sh`
2. **Update production configuration** with your credentials
3. **Start the build**: `eas build --platform ios --profile production`
4. **Submit to TestFlight** for beta testing
5. **Gather feedback** and iterate
6. **Submit to App Store** when ready

## 📞 Support Resources

- Expo Documentation: https://docs.expo.dev/
- EAS Build: https://docs.expo.dev/build/introduction/
- Apple Developer: https://developer.apple.com/
- App Store Connect: https://appstoreconnect.apple.com/
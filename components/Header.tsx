
import React from 'react';

const Header: React.FC = () => {
  return (
    <header className="w-full px-4 py-2 bg-neutral-800 border-b border-neutral-700 shadow-lg flex-shrink-0">
      <div className="flex items-center gap-3">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" className="w-6 h-6 text-brand-primary">
            <path d="M4.5 4.5a3 3 0 0 0-3 3v9a3 3 0 0 0 3 3h8.25a3 3 0 0 0 3-3v-9a3 3 0 0 0-3-3H4.5ZM19.94 18.75l-2.69-2.69V7.94l2.69-2.69c.944-.945 2.56-.276 2.56 1.06v11.38c0 1.336-1.616 2.005-2.56 1.06Z" />
        </svg>
        <h1 className="text-lg sm:text-xl font-bold text-neutral-100 tracking-wider">
          Combat Mirror <span className="text-brand-primary font-light">System</span>
        </h1>
      </div>
    </header>
  );
};

export default Header;

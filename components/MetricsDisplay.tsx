
import React from 'react';
import { Metrics } from '../types';
import { CORE_VARIABLES } from '../constants';

interface MetricsDisplayProps {
  metrics: Metrics;
  isSessionActive: boolean;
}

const MetricCard = React.memo<{ title: string; value: string; unit: string; icon: React.ReactNode, isSessionActive: boolean }>(
  ({ title, value, unit, icon, isSessionActive }) => {
    return (
      <div className="bg-neutral-700 p-4 rounded-lg flex items-center gap-4 transition-all duration-300 border border-neutral-600 hover:border-brand-primary hover:bg-neutral-600">
        <div className={`p-3 rounded-full bg-neutral-800 text-brand-primary transition-colors duration-300 ${isSessionActive ? 'text-brand-primary' : 'text-neutral-500'}`}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-neutral-300">{title}</p>
          <p className="text-2xl font-bold text-neutral-100">
            {value} <span className="text-base font-normal text-neutral-400">{unit}</span>
          </p>
        </div>
      </div>
    );
  }
);

const MetricsDisplay: React.FC<MetricsDisplayProps> = ({ metrics, isSessionActive }) => {
  return (
    <div className="bg-neutral-800 p-6 rounded-lg shadow-xl border border-neutral-700 h-full">
      <h3 className="text-xl font-semibold mb-4 text-neutral-100">Real-Time Biomechanical Analysis</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        {CORE_VARIABLES.map(({ key, title, unit, Icon }) => (
          <MetricCard
            key={key}
            title={title}
            value={metrics[key as keyof Metrics].toFixed(key === 'punchCount' ? 0 : 1)}
            unit={unit}
            icon={<Icon className="w-6 h-6" />}
            isSessionActive={isSessionActive}
          />
        ))}
      </div>
    </div>
  );
};

export default React.memo(MetricsDisplay, (prevProps, nextProps) => {
  // Custom comparison function - only re-render if metrics or session state changed
  return (
    prevProps.isSessionActive === nextProps.isSessionActive &&
    JSON.stringify(prevProps.metrics) === JSON.stringify(nextProps.metrics)
  );
});

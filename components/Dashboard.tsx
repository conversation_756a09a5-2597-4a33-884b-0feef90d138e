
import React from 'react';
import { SessionState, SessionType, Metrics } from '../types';
import SessionControl from './SessionControl';
import CameraGrid from './CameraGrid';
import MetricsDisplay from './MetricsDisplay';

interface DashboardProps {
    participantId: string;
    setParticipantId: (id: string) => void;
    sessionType: SessionType;
    setSessionType: (type: SessionType) => void;
    sessionState: SessionState;
    handleStart: () => void;
    handleStop: () => void;
    metrics: Metrics;
    onCameraMetricsUpdate?: (cameraId: string, metrics: Metrics) => void;
    useLiveCamera?: boolean;
}

const Dashboard: React.FC<DashboardProps> = ({
    participantId,
    setParticipantId,
    sessionType,
    setSessionType,
    sessionState,
    handleStart,
    handleStop,
    metrics,
    onCameraMetricsUpdate,
    useLiveCamera = false
}) => {
    return (
        <main className="flex-1 flex flex-col overflow-hidden">
            <div className="flex-1 flex flex-col gap-4 p-4 overflow-auto">
                {/* Session Control - Compact */}
                <div className="w-full flex-shrink-0">
                    <SessionControl 
                        participantId={participantId}
                        setParticipantId={setParticipantId}
                        sessionType={sessionType}
                        setSessionType={setSessionType}
                        sessionState={sessionState}
                        onStart={handleStart}
                        onStop={handleStop}
                    />
                </div>
                
                {/* Camera Grid - Flex Grow */}
                <div className="w-full flex-1 min-h-0">
                    <CameraGrid 
                        isSessionActive={sessionState === SessionState.Active} 
                        onMetricsUpdate={onCameraMetricsUpdate}
                        useLiveCamera={useLiveCamera}
                    />
                </div>
                
                {/* Metrics Display - Compact */}
                <div className="w-full flex-shrink-0">
                    <MetricsDisplay metrics={metrics} isSessionActive={sessionState === SessionState.Active}/>
                </div>
            </div>
        </main>
    );
}

export default React.memo(Dashboard);

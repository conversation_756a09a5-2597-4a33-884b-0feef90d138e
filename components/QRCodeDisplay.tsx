import React, { useEffect, useState } from 'react';
import QRCode from 'qrcode';

interface QRCodeDisplayProps {
  url: string;
  position: string;
  size?: number;
}

const QRCodeDisplay: React.FC<QRCodeDisplayProps> = ({ url, position, size = 300 }) => {
  const [qrCodeUrl, setQrCodeUrl] = useState<string>('');

  useEffect(() => {
    const generateQR = async () => {
      try {
        const qrUrl = await QRCode.toDataURL(url, {
          width: size,
          margin: 2,
          color: {
            dark: '#000000',
            light: '#FFFFFF',
          },
        });
        setQrCodeUrl(qrUrl);
      } catch (err) {
        console.error('Failed to generate QR code:', err);
      }
    };

    generateQR();
  }, [url, size]);

  return (
    <div className="flex flex-col items-center justify-center h-full bg-neutral-800 rounded-lg p-4">
      <div className="bg-white p-6 rounded-xl shadow-xl">
        {qrCodeUrl ? (
          <img 
            src={qrCodeUrl} 
            alt={`QR Code for ${position}`} 
            className="w-full h-full max-w-[300px] max-h-[300px]" 
          />
        ) : (
          <div className="w-[300px] h-[300px] bg-neutral-200 animate-pulse rounded" />
        )}
      </div>
      <div className="mt-6 text-center">
        <p className="text-white text-lg font-semibold">
          Scan to Connect {position} Camera
        </p>
        <p className="text-neutral-400 text-sm mt-2">
          Use your iPhone camera app
        </p>
      </div>
    </div>
  );
};

export default QRCodeDisplay;
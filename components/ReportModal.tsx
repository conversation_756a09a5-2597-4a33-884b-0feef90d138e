
import React from 'react';
import { Metrics, SessionState } from '../types';

interface ReportModalProps {
  isOpen: boolean;
  onClose: () => void;
  report: string;
  sessionState: SessionState;
  metrics: Metrics;
}

const ReportModal: React.FC<ReportModalProps> = ({ isOpen, onClose, report, sessionState, metrics }) => {
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-70 flex items-center justify-center z-50 p-4 transition-opacity duration-300">
      <div className="bg-neutral-800 rounded-lg shadow-2xl w-full max-w-4xl max-h-[90vh] flex flex-col border border-neutral-600">
        <div className="p-4 border-b border-neutral-700 flex justify-between items-center">
          <h2 className="text-2xl font-bold text-brand-primary">Performance Analysis Report</h2>
          <button onClick={onClose} className="text-neutral-400 hover:text-white transition">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        <div className="p-6 overflow-y-auto">
          {sessionState === SessionState.Analyzing && (
            <div className="flex flex-col items-center justify-center h-96 text-center">
              <svg className="animate-spin h-12 w-12 text-brand-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <h3 className="text-xl font-semibold mt-4">Analyzing Session Data...</h3>
              <p className="text-neutral-300 mt-1">Gemini AI is generating your comprehensive report.</p>
            </div>
          )}
          {sessionState === SessionState.Finished && (
            <div className="prose prose-invert max-w-none prose-p:text-neutral-200 prose-headings:text-brand-secondary">
                <h4 className="text-lg font-semibold text-brand-secondary">Data Summary</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-2 text-sm mb-6 not-prose">
                    <p><strong>Punch Rate:</strong> {metrics.punchRate.toFixed(1)}/min</p>
                    <p><strong>Total Punches:</strong> {metrics.punchCount}</p>
                    <p><strong>Avg Velocity:</strong> {metrics.punchVelocity.toFixed(2)} m/s</p>
                    <p><strong>Head Movement:</strong> {metrics.headMovement.toFixed(2)} cm</p>
                    <p><strong>Posture Score:</strong> {metrics.postureScore.toFixed(0)}/100</p>
                    <p><strong>Fatigue Index:</strong> {metrics.fatigue.toFixed(1)}%</p>
                </div>

                <h4 className="text-lg font-semibold text-brand-secondary border-t border-neutral-700 pt-4">AI-Generated Insights</h4>
                <pre className="whitespace-pre-wrap font-sans text-neutral-200 bg-neutral-900 p-4 rounded-md">{report}</pre>
            </div>
          )}
        </div>
         <div className="p-4 border-t border-neutral-700 text-right">
            <button onClick={onClose} className="bg-brand-primary text-white font-bold py-2 px-6 rounded-md hover:bg-brand-secondary transition-colors">
                Close Report
            </button>
        </div>
      </div>
    </div>
  );
};

export default ReportModal;

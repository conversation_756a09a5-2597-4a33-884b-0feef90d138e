import React, { useRef, useEffect, useState, useMemo } from 'react';
import { mediaPipeService, PoseResults } from '../services/mediaPipeService';
import { videoAnalysisService } from '../services/videoAnalysisService';
import { Metrics } from '../types';
import { debounce } from '../utils/debounce';

interface VideoPoseAnalysisProps {
  isActive: boolean;
  onMetricsUpdate: (metrics: Metrics) => void;
  videoStream?: MediaStream;
}

const VideoPoseAnalysis: React.FC<VideoPoseAnalysisProps> = ({
  isActive,
  onMetricsUpdate,
  videoStream
}) => {
  const videoRef = useRef<HTMLVideoElement>(null);
  const canvasRef = useRef<HTMLCanvasElement>(null);
  const animationFrameRef = useRef<number>();
  const [isProcessing, setIsProcessing] = useState(false);
  const [fps, setFps] = useState(0);
  const lastFrameTimeRef = useRef(Date.now());
  const frameCountRef = useRef(0);

  // Debounce metrics updates to reduce re-renders (100ms = max 10 updates/sec)
  const debouncedMetricsUpdate = useMemo(
    () => debounce(onMetricsUpdate, 100),
    [onMetricsUpdate]
  );

  useEffect(() => {
    if (isActive && videoStream && videoRef.current) {
      videoRef.current.srcObject = videoStream;
      startPoseDetection();
    } else {
      stopPoseDetection();
    }

    return () => {
      stopPoseDetection();
    };
  }, [isActive, videoStream]);

  const startPoseDetection = async () => {
    if (!videoRef.current || !canvasRef.current) return;

    setIsProcessing(true);
    videoAnalysisService.startAnalysis();

    try {
      await mediaPipeService.processVideoElement(
        videoRef.current,
        onPoseResults
      );
    } catch (error) {
      console.error('Failed to start pose detection:', error);
      setIsProcessing(false);
    }
  };

  const stopPoseDetection = () => {
    mediaPipeService.stop();
    videoAnalysisService.reset();
    setIsProcessing(false);
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }
  };

  const onPoseResults = async (results: PoseResults) => {
    // Calculate FPS
    frameCountRef.current++;
    const currentTime = Date.now();
    const deltaTime = currentTime - lastFrameTimeRef.current;
    
    if (deltaTime >= 1000) {
      setFps(Math.round((frameCountRef.current * 1000) / deltaTime));
      frameCountRef.current = 0;
      lastFrameTimeRef.current = currentTime;
    }

    // Draw pose on canvas
    if (canvasRef.current && videoRef.current) {
      const canvasCtx = canvasRef.current.getContext('2d')!;
      mediaPipeService.drawPose(
        canvasCtx,
        results,
        canvasRef.current.width,
        canvasRef.current.height
      );
    }

    // Analyze frame and update metrics with debouncing
    const metrics = await videoAnalysisService.analyzeFrame(results);
    debouncedMetricsUpdate(metrics);
  };

  return (
    <div className="relative w-full h-full bg-black rounded-lg overflow-hidden">
      <video
        ref={videoRef}
        className="absolute inset-0 w-full h-full object-contain"
        autoPlay
        playsInline
        muted
      />
      <canvas
        ref={canvasRef}
        className="absolute inset-0 w-full h-full object-contain"
        width={1280}
        height={720}
      />
      
      {/* Overlay UI */}
      <div className="absolute top-4 left-4 bg-black/50 backdrop-blur-sm rounded-lg p-2">
        <div className="text-white text-sm font-mono space-y-1">
          <div>FPS: {fps}</div>
          <div>Status: {isProcessing ? 'Processing' : 'Idle'}</div>
        </div>
      </div>

      {!videoStream && (
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="text-neutral-400 text-center">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <p>No video stream available</p>
          </div>
        </div>
      )}
    </div>
  );
};

export default VideoPoseAnalysis;
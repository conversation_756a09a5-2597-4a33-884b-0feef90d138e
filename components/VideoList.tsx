import React from 'react';
import { VideoMetadata } from '../services/videoStorageService';

interface VideoListProps {
  videos: VideoMetadata[];
  onVideoSelect?: (video: VideoMetadata) => void;
  onVideoDelete?: (id: string) => void;
}

const VideoList: React.FC<VideoListProps> = ({ videos, onVideoSelect, onVideoDelete }) => {
  const getSyncStatusColor = (status: VideoMetadata['syncStatus']) => {
    switch (status) {
      case 'synced': return 'text-green-500';
      case 'syncing': return 'text-yellow-500';
      case 'failed': return 'text-red-500';
      default: return 'text-neutral-400';
    }
  };

  const getSyncStatusText = (status: VideoMetadata['syncStatus']) => {
    switch (status) {
      case 'synced': return '✓ Synced';
      case 'syncing': return '⟳ Syncing...';
      case 'failed': return '✗ Failed';
      default: return '○ Pending';
    }
  };

  const formatFileSize = (bytes: number): string => {
    const mb = bytes / (1024 * 1024);
    return `${mb.toFixed(2)} MB`;
  };

  const formatDate = (date: Date): string => {
    return new Date(date).toLocaleString();
  };

  if (videos.length === 0) {
    return (
      <div className="bg-neutral-800 rounded-lg p-6 border border-neutral-700 text-center text-neutral-400">
        No videos uploaded yet
      </div>
    );
  }

  return (
    <div className="bg-neutral-800 rounded-lg p-6 border border-neutral-700">
      <h3 className="text-lg font-semibold mb-4 text-neutral-100">Uploaded Videos</h3>
      <div className="space-y-3">
        {videos.map((video) => (
          <div
            key={video.id}
            className="bg-neutral-700 rounded-lg p-4 flex items-center justify-between hover:bg-neutral-650 transition-colors"
          >
            <div className="flex-1">
              <div className="flex items-center space-x-3">
                <div className="flex-1">
                  <h4 className="font-medium text-neutral-100">{video.filename}</h4>
                  <div className="text-sm text-neutral-400 mt-1">
                    <span>Participant: {video.participantId}</span>
                    <span className="mx-2">•</span>
                    <span>{video.sessionType}</span>
                    <span className="mx-2">•</span>
                    <span>{formatFileSize(video.size)}</span>
                  </div>
                  <div className="text-xs text-neutral-500 mt-1">
                    {formatDate(video.uploadedAt)}
                  </div>
                </div>
                <div className={`text-sm font-medium ${getSyncStatusColor(video.syncStatus)}`}>
                  {getSyncStatusText(video.syncStatus)}
                </div>
              </div>
            </div>
            <div className="flex items-center space-x-2 ml-4">
              {onVideoSelect && (
                <button
                  onClick={() => onVideoSelect(video)}
                  className="px-3 py-1.5 text-sm bg-blue-600 hover:bg-blue-700 text-white rounded transition-colors"
                >
                  View
                </button>
              )}
              {onVideoDelete && (
                <button
                  onClick={() => onVideoDelete(video.id)}
                  className="px-3 py-1.5 text-sm bg-red-600 hover:bg-red-700 text-white rounded transition-colors"
                >
                  Delete
                </button>
              )}
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

export default VideoList;
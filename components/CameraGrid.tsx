import React, { useEffect, useState, useRef } from 'react';
import VideoPoseAnalysis from './VideoPoseAnalysis';
import QRCodeDisplay from './QRCodeDisplay';
import LiveKitVideo from './LiveKitVideo';
import CameraVideoUpload from './CameraVideoUpload';
import { Metrics } from '../types';
import { liveKitService, CameraConnection } from '../services/livekitService';

interface CameraGridProps {
  isSessionActive: boolean;
  onMetricsUpdate?: (cameraId: string, metrics: Metrics) => void;
  useLiveCamera?: boolean;
}

interface CameraStream {
  id: string;
  position: string;
  stream: MediaStream | null;
  connection: CameraConnection | null;
  isConnected: boolean;
  uploadedVideo: File | null;
  videoUrl: string | null;
  showQRCode: boolean;
}

const CameraView: React.FC<{ 
  position: string, 
  isSessionActive: boolean,
  stream: MediaStream | null,
  connection: CameraConnection | null,
  isConnected: boolean,
  uploadedVideo: File | null,
  videoUrl: string | null,
  showQRCode: boolean,
  onMetricsUpdate?: (metrics: Metrics) => void,
  isMainCamera?: boolean,
  useLiveCamera: boolean,
  onVideoUpload: (file: File) => void,
  onShowQRCode: () => void,
  onHideQRCode: () => void
}> = ({ 
  position, 
  isSessionActive, 
  stream, 
  connection, 
  isConnected, 
  uploadedVideo,
  videoUrl,
  showQRCode,
  onMetricsUpdate, 
  isMainCamera = false, 
  useLiveCamera,
  onVideoUpload,
  onShowQRCode,
  onHideQRCode
}) => {
  const [mediaStreamTrack, setMediaStreamTrack] = useState<MediaStreamTrack | null>(null);
  const videoRef = useRef<HTMLVideoElement>(null);
  
  // Handle MediaStreamTrack from LiveKit
  const handleVideoTrack = (track: MediaStreamTrack | null) => {
    setMediaStreamTrack(track);
    if (track) {
      const newStream = new MediaStream([track]);
      stream = newStream;
    }
  };

  // Show QR code when requested
  if (useLiveCamera && showQRCode && connection) {
    return (
      <div className="relative w-full h-full bg-neutral-900 rounded-xl overflow-hidden border-2 border-neutral-700 shadow-lg">
        <QRCodeDisplay 
          url={connection.url} 
          position={position}
        />
        <button
          onClick={onHideQRCode}
          className="absolute top-3 right-3 bg-neutral-700 hover:bg-neutral-600 text-white p-2 rounded-lg transition-colors"
          title="Back to options"
        >
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
      </div>
    );
  }

  // Show LiveKit video when connected
  if (useLiveCamera && isConnected && connection) {
    if (isMainCamera && onMetricsUpdate && mediaStreamTrack) {
      const videoStream = new MediaStream([mediaStreamTrack]);
      return (
        <div className="relative w-full h-full bg-neutral-900 rounded-xl overflow-hidden border-2 border-neutral-700 shadow-lg">
          <VideoPoseAnalysis
            isActive={isSessionActive}
            onMetricsUpdate={onMetricsUpdate}
            videoStream={videoStream}
          />
          <div className="absolute top-3 left-3 bg-black/70 backdrop-blur-sm text-white text-sm font-bold px-3 py-1.5 rounded-lg">
            {position} (MAIN - LiveKit)
          </div>
        </div>
      );
    }

    return (
      <div className="relative w-full h-full bg-neutral-900 rounded-xl overflow-hidden border-2 border-neutral-700 shadow-lg">
        <LiveKitVideo
          roomName={connection.roomName}
          token={connection.token}
          serverUrl="ws://localhost:7880"
          onVideoTrack={handleVideoTrack}
          onMetricsReceived={(metrics) => {
            if (onMetricsUpdate) {
              onMetricsUpdate(metrics);
            }
          }}
        />
        <div className="absolute top-3 left-3 bg-black/70 backdrop-blur-sm text-white text-sm font-bold px-3 py-1.5 rounded-lg">
          {position} (LiveKit)
        </div>
        {isSessionActive && (
          <div className="absolute top-3 right-3 flex items-center gap-1.5">
            <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
            <span className="text-white text-sm font-bold">LIVE</span>
          </div>
        )}
      </div>
    );
  }

  // Show uploaded video
  if (uploadedVideo && videoUrl) {
    return (
      <div className="relative w-full h-full bg-black rounded-xl overflow-hidden border-2 border-neutral-700 shadow-lg">
        <video
          ref={videoRef}
          className="w-full h-full object-contain"
          src={videoUrl}
          controls
          loop
        />
        <div className="absolute top-3 left-3 bg-black/70 backdrop-blur-sm text-white text-sm font-bold px-3 py-1.5 rounded-lg">
          {position} (Uploaded)
        </div>
        <button
          onClick={() => onVideoUpload(null as any)} // Clear video
          className="absolute top-3 right-3 bg-red-600 hover:bg-red-700 text-white p-2 rounded-lg transition-colors"
          title="Remove video"
        >
          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
          </svg>
        </button>
      </div>
    );
  }

  // Show video upload options
  return (
    <CameraVideoUpload
      position={position}
      onVideoSelect={onVideoUpload}
      hasVideo={!!uploadedVideo}
      onShowQRCode={useLiveCamera ? onShowQRCode : undefined}
    />
  );
};

const CameraGrid: React.FC<CameraGridProps> = ({ 
  isSessionActive, 
  onMetricsUpdate,
  useLiveCamera = false 
}) => {
  const [cameraStreams, setCameraStreams] = useState<CameraStream[]>([
    { id: 'front', position: 'FRONT', stream: null, connection: null, isConnected: false, uploadedVideo: null, videoUrl: null, showQRCode: false },
    { id: 'left', position: 'LEFT', stream: null, connection: null, isConnected: false, uploadedVideo: null, videoUrl: null, showQRCode: false },
    { id: 'right', position: 'RIGHT', stream: null, connection: null, isConnected: false, uploadedVideo: null, videoUrl: null, showQRCode: false },
    { id: 'behind', position: 'BEHIND', stream: null, connection: null, isConnected: false, uploadedVideo: null, videoUrl: null, showQRCode: false }
  ]);

  const [isSyncing, setIsSyncing] = useState(false);

  useEffect(() => {
    if (useLiveCamera) {
      // Generate LiveKit connections for each camera when Live Camera is enabled
      setCameraStreams(prev => prev.map(cs => ({
        ...cs,
        connection: liveKitService.generateCameraConnection(cs.position),
        isConnected: false
      })));
    } else {
      // Clear connections when Live Camera is disabled
      stopAllStreams();
    }

    return () => {
      stopAllStreams();
    };
  }, [useLiveCamera]);

  // Check for connected participants
  useEffect(() => {
    if (!useLiveCamera) return;

    const checkInterval = setInterval(() => {
      setCameraStreams(prev => prev.map(cs => {
        if (cs.connection && !cs.isConnected) {
          // In production, use LiveKit room events to detect connections
          return { ...cs, isConnected: false };
        }
        return cs;
      }));
    }, 2000);

    return () => clearInterval(checkInterval);
  }, [useLiveCamera]);

  const stopAllStreams = () => {
    cameraStreams.forEach(cs => {
      if (cs.stream) {
        cs.stream.getTracks().forEach(track => track.stop());
      }
      if (cs.connection) {
        liveKitService.disconnectFromRoom(cs.connection.roomName);
      }
      if (cs.videoUrl) {
        URL.revokeObjectURL(cs.videoUrl);
      }
    });
    setCameraStreams(prev => prev.map(cs => ({ 
      ...cs, 
      stream: null, 
      connection: null, 
      isConnected: false,
      uploadedVideo: null,
      videoUrl: null,
      showQRCode: false
    })));
  };

  const handleVideoUpload = (cameraId: string) => (file: File | null) => {
    setCameraStreams(prev => prev.map(cs => {
      if (cs.id === cameraId) {
        // Clear previous video URL
        if (cs.videoUrl) {
          URL.revokeObjectURL(cs.videoUrl);
        }
        
        if (file) {
          const url = URL.createObjectURL(file);
          return { ...cs, uploadedVideo: file, videoUrl: url, showQRCode: false };
        } else {
          return { ...cs, uploadedVideo: null, videoUrl: null };
        }
      }
      return cs;
    }));
  };

  const handleShowQRCode = (cameraId: string) => () => {
    setCameraStreams(prev => prev.map(cs => 
      cs.id === cameraId ? { ...cs, showQRCode: true } : cs
    ));
  };

  const handleHideQRCode = (cameraId: string) => () => {
    setCameraStreams(prev => prev.map(cs => 
      cs.id === cameraId ? { ...cs, showQRCode: false } : cs
    ));
  };

  const handleMetricsUpdate = (cameraId: string) => (metrics: Metrics) => {
    if (onMetricsUpdate) {
      onMetricsUpdate(cameraId, metrics);
    }
  };

  const handleSyncAnalysis = async () => {
    setIsSyncing(true);
    
    // Get all uploaded videos
    const uploadedVideos = cameraStreams.filter(cs => cs.uploadedVideo);
    
    if (uploadedVideos.length === 0) {
      alert('Please upload at least one video to analyze');
      setIsSyncing(false);
      return;
    }

    // TODO: Implement actual video sync and analysis
    console.log('Syncing and analyzing videos:', uploadedVideos.map(v => ({
      position: v.position,
      file: v.uploadedVideo
    })));

    // Simulate processing
    setTimeout(() => {
      setIsSyncing(false);
      alert(`Analysis complete for ${uploadedVideos.length} video(s)`);
    }, 3000);
  };

  const hasUploadedVideos = cameraStreams.some(cs => cs.uploadedVideo);

  return (
    <div className="h-full flex flex-col bg-neutral-800 p-4 sm:p-6 rounded-xl shadow-xl border border-neutral-700">
      <div className="flex justify-between items-center mb-4 flex-shrink-0">
        <h3 className="text-xl sm:text-2xl font-bold text-neutral-100">Multi-Camera Feed</h3>
        <div className="flex items-center gap-3">
          {useLiveCamera && (
            <span className="text-sm text-green-400 flex items-center gap-2">
              <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
              LiveKit Mode Active
            </span>
          )}
          {hasUploadedVideos && (
            <button
              onClick={handleSyncAnalysis}
              disabled={isSyncing || isSessionActive}
              className={`px-4 py-2 rounded-lg font-medium transition-all flex items-center gap-2 ${
                isSyncing || isSessionActive
                  ? 'bg-neutral-600 text-neutral-400 cursor-not-allowed'
                  : 'bg-blue-600 hover:bg-blue-700 text-white'
              }`}
            >
              {isSyncing ? (
                <>
                  <svg className="w-4 h-4 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Analyzing...
                </>
              ) : (
                <>
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                  </svg>
                  Sync & Analyze Videos
                </>
              )}
            </button>
          )}
        </div>
      </div>
      
      {/* Bento Box Layout - Responsive Grid */}
      <div className="flex-1 grid grid-cols-1 sm:grid-cols-2 gap-4 min-h-0">
        {/* Front Camera - Main/Larger on desktop */}
        <div className="sm:col-span-2 lg:col-span-1 lg:row-span-2 min-h-[300px]">
          <CameraView 
            position={cameraStreams[0].position} 
            isSessionActive={isSessionActive}
            stream={cameraStreams[0].stream}
            connection={cameraStreams[0].connection}
            isConnected={cameraStreams[0].isConnected}
            uploadedVideo={cameraStreams[0].uploadedVideo}
            videoUrl={cameraStreams[0].videoUrl}
            showQRCode={cameraStreams[0].showQRCode}
            onMetricsUpdate={handleMetricsUpdate(cameraStreams[0].id)}
            isMainCamera={true}
            useLiveCamera={useLiveCamera}
            onVideoUpload={handleVideoUpload(cameraStreams[0].id)}
            onShowQRCode={handleShowQRCode(cameraStreams[0].id)}
            onHideQRCode={handleHideQRCode(cameraStreams[0].id)}
          />
        </div>
        
        {/* Other Cameras - Smaller tiles */}
        <div className="grid grid-cols-1 gap-4 min-h-0">
          {cameraStreams.slice(1).map((cs) => (
            <div key={cs.id} className="min-h-[200px]">
              <CameraView 
                position={cs.position} 
                isSessionActive={isSessionActive}
                stream={cs.stream}
                connection={cs.connection}
                isConnected={cs.isConnected}
                uploadedVideo={cs.uploadedVideo}
                videoUrl={cs.videoUrl}
                showQRCode={cs.showQRCode}
                onMetricsUpdate={undefined}
                isMainCamera={false}
                useLiveCamera={useLiveCamera}
                onVideoUpload={handleVideoUpload(cs.id)}
                onShowQRCode={handleShowQRCode(cs.id)}
                onHideQRCode={handleHideQRCode(cs.id)}
              />
            </div>
          ))}
        </div>
      </div>
      
      {useLiveCamera && (
        <div className="mt-4 p-3 bg-neutral-700/50 rounded-lg backdrop-blur-sm flex-shrink-0">
          <p className="text-xs text-neutral-300">
            <strong>LiveKit Server:</strong> ws://localhost:7880 | 
            Click "QR Code for Live Stream" to connect iPhones
          </p>
        </div>
      )}
    </div>
  );
};

export default CameraGrid;
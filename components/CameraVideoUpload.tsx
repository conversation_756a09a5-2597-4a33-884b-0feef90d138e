import React, { useRef, useState } from 'react';

interface CameraVideoUploadProps {
  position: string;
  onVideoSelect: (file: File) => void;
  hasVideo: boolean;
  onShowQRCode?: () => void;
}

const CameraVideoUpload: React.FC<CameraVideoUploadProps> = ({
  position,
  onVideoSelect,
  hasVideo,
  onShowQRCode
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isDragging, setIsDragging] = useState(false);

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type.startsWith('video/')) {
      onVideoSelect(file);
    }
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = () => {
    setIsDragging(false);
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    setIsDragging(false);
    
    const file = e.dataTransfer.files[0];
    if (file && file.type.startsWith('video/')) {
      onVideoSelect(file);
    }
  };

  return (
    <div 
      className={`relative w-full h-full min-h-[400px] bg-black rounded-xl overflow-hidden border-2 transition-all ${
        isDragging ? 'border-blue-500 bg-blue-900/20' : 'border-neutral-700'
      }`}
      onDragOver={handleDragOver}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      <div className="absolute inset-0 flex flex-col items-center justify-center p-8">
        <div className="text-center space-y-6">
          <div className="text-neutral-400">
            <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z" />
            </svg>
            <p className="text-lg font-medium text-white">{position} Camera</p>
            <p className="text-sm mt-2">No video connected</p>
          </div>

          <div className="flex flex-col gap-3">
            {onShowQRCode && (
              <button
                onClick={onShowQRCode}
                className="px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
              >
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v1m6 11h2m-6 0h-2v4m0-11v3m0 0h.01M12 12h4.01M16 20h2M8 20h2m-2-4h.01M8 8h2m-2 4h.01M4 8h2m-2 4h.01M4 16h2" />
                </svg>
                QR Code for Live Stream
              </button>
            )}
            
            <button
              onClick={() => fileInputRef.current?.click()}
              className="px-6 py-3 bg-neutral-700 hover:bg-neutral-600 text-white rounded-lg font-medium transition-colors flex items-center justify-center gap-2"
            >
              <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
              </svg>
              Upload Video
            </button>
          </div>

          <p className="text-xs text-neutral-500 mt-4">
            Drag and drop a video file here or click to browse
          </p>
        </div>
      </div>

      <input
        ref={fileInputRef}
        type="file"
        accept="video/*"
        onChange={handleFileSelect}
        className="hidden"
      />

      {hasVideo && (
        <div className="absolute top-3 right-3 bg-green-600 text-white text-xs px-2 py-1 rounded">
          Video Ready
        </div>
      )}
    </div>
  );
};

export default CameraVideoUpload;
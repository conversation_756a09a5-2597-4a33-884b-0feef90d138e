import React, { useEffect, useRef, useState } from 'react';
import {
  LiveKitRoom,
  GridLayout,
  ParticipantTile,
  useTracks,
  RoomAudioRenderer,
  useDataChannel,
  useRoomContext
} from '@livekit/components-react';
import { Track, DataPacket } from 'livekit-client';
import '@livekit/components-styles';

interface LiveKitVideoProps {
  roomName: string;
  token: string;
  serverUrl: string;
  onVideoTrack?: (track: MediaStreamTrack | null) => void;
  onMetricsReceived?: (metrics: any) => void;
}

const LiveKitVideo: React.FC<LiveKitVideoProps> = ({ 
  roomName, 
  token, 
  serverUrl,
  onVideoTrack,
  onMetricsReceived
}) => {
  return (
    <LiveKitRoom
      video={false}
      audio={false}
      token={token}
      serverUrl={serverUrl}
      connect={true}
      options={{
        adaptiveStream: true,
        dynacast: true,
      }}
    >
      <VideoRoomComponent 
        onVideoTrack={onVideoTrack} 
        onMetricsReceived={onMetricsReceived}
      />
      <RoomAudioRenderer />
    </LiveKitRoom>
  );
};

interface VideoRoomComponentProps {
  onVideoTrack?: (track: MediaStreamTrack | null) => void;
  onMetricsReceived?: (metrics: any) => void;
}

const VideoRoomComponent: React.FC<VideoRoomComponentProps> = ({ 
  onVideoTrack, 
  onMetricsReceived 
}) => {
  const tracks = useTracks([Track.Source.Camera], {
    onlySubscribed: true,
  });
  
  const room = useRoomContext();
  const [lastMetrics, setLastMetrics] = useState<any>(null);

  // Handle video tracks
  useEffect(() => {
    const videoTrack = tracks.find(t => t.publication.kind === Track.Kind.Video);
    if (videoTrack && videoTrack.publication.track) {
      const mediaStreamTrack = videoTrack.publication.track.mediaStreamTrack;
      onVideoTrack?.(mediaStreamTrack);
    } else {
      onVideoTrack?.(null);
    }
  }, [tracks, onVideoTrack]);

  // Handle data messages from agent
  useEffect(() => {
    if (!room) return;

    const handleDataReceived = (payload: Uint8Array, participant: any, kind: any, topic?: string) => {
      if (topic === 'metrics') {
        try {
          const message = JSON.parse(new TextDecoder().decode(payload));
          if (message.type === 'combat_metrics') {
            setLastMetrics(message.metrics);
            if (onMetricsReceived) {
              onMetricsReceived(message.metrics);
            }
          }
        } catch (e) {
          console.error('Error parsing metrics data:', e);
        }
      }
    };

    room.on('dataReceived', handleDataReceived);

    return () => {
      room.off('dataReceived', handleDataReceived);
    };
  }, [room, onMetricsReceived]);

  return (
    <div className="relative h-full w-full">
      <GridLayout tracks={tracks}>
        <ParticipantTile />
      </GridLayout>
      
      {/* Real-time metrics overlay */}
      {lastMetrics && (
        <div className="absolute bottom-3 left-3 bg-black/70 backdrop-blur-sm text-white text-xs p-2 rounded-lg z-10">
          <div className="grid grid-cols-2 gap-x-3 gap-y-1">
            <span>Posture: {lastMetrics.posture_score?.toFixed(1) || 0}</span>
            <span>Balance: {lastMetrics.balance_score?.toFixed(1) || 0}</span>
            <span>Defense: {lastMetrics.defense_rating?.toFixed(1) || 0}</span>
            <span>Guard: {lastMetrics.guard_position || 'Unknown'}</span>
          </div>
          {lastMetrics.punch_detected && (
            <div className="mt-1 text-yellow-400 font-bold animate-pulse">PUNCH DETECTED!</div>
          )}
        </div>
      )}
    </div>
  );
};

export default LiveKitVideo;
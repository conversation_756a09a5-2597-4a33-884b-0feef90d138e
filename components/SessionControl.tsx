
import React from 'react';
import { SessionState, SessionType } from '../types';
import { SESSION_TYPES } from '../constants';

interface SessionControlProps {
  participantId: string;
  setParticipantId: (id: string) => void;
  sessionType: SessionType;
  setSessionType: (type: SessionType) => void;
  sessionState: SessionState;
  onStart: () => void;
  onStop: () => void;
}

const SessionControl: React.FC<SessionControlProps> = ({
  participantId,
  setParticipantId,
  sessionType,
  setSessionType,
  sessionState,
  onStart,
  onStop,
}) => {
  const isIdle = sessionState === SessionState.Idle;
  const isActive = sessionState === SessionState.Active;

  return (
    <div className="bg-neutral-800 p-4 sm:p-6 rounded-xl shadow-xl border border-neutral-700">
      <h3 className="text-xl sm:text-2xl font-bold mb-4 text-neutral-100">Session Configuration</h3>
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="sm:col-span-1">
          <label htmlFor="participantId" className="block text-sm font-medium text-neutral-200 mb-1">
            Participant ID
          </label>
          <input
            type="text"
            id="participantId"
            value={participantId}
            onChange={(e) => setParticipantId(e.target.value)}
            disabled={!isIdle}
            className="w-full bg-neutral-700 border border-neutral-600 rounded-md px-3 py-2 text-neutral-100 focus:ring-2 focus:ring-brand-primary focus:outline-none transition disabled:opacity-50"
            placeholder="e.g., P001"
          />
        </div>
        <div className="sm:col-span-1">
          <label htmlFor="sessionType" className="block text-sm font-medium text-neutral-200 mb-1">
            Session Type
          </label>
          <select
            id="sessionType"
            value={sessionType}
            onChange={(e) => setSessionType(e.target.value as SessionType)}
            disabled={!isIdle}
            className="w-full bg-neutral-700 border border-neutral-600 rounded-md px-3 py-2 text-neutral-100 focus:ring-2 focus:ring-brand-primary focus:outline-none transition disabled:opacity-50"
          >
            {SESSION_TYPES.map((type) => (
              <option key={type} value={type}>
                {type}
              </option>
            ))}
          </select>
        </div>
        <div className="flex gap-2 sm:gap-4 sm:col-span-2 lg:col-span-2">
          <button
            onClick={onStart}
            disabled={!isIdle || !participantId}
            className="w-full bg-green-600 text-white font-bold py-2 px-4 rounded-md hover:bg-green-500 transition-colors disabled:bg-neutral-600 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5"><path d="M6.3 2.841A1.5 1.5 0 0 0 4.5 4.118V15.882A1.5 1.5 0 0 0 6.3 17.16l9.761-5.882a1.5 1.5 0 0 0 0-2.596L6.3 2.841Z" /></svg>
            Start Session
          </button>
          <button
            onClick={onStop}
            disabled={!isActive}
            className="w-full bg-red-600 text-white font-bold py-2 px-4 rounded-md hover:bg-red-500 transition-colors disabled:bg-neutral-600 disabled:cursor-not-allowed flex items-center justify-center gap-2"
          >
             <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" className="w-5 h-5"><path d="M5.25 3A2.25 2.25 0 0 0 3 5.25v9.5A2.25 2.25 0 0 0 5.25 17h9.5A2.25 2.25 0 0 0 17 14.75v-9.5A2.25 2.25 0 0 0 14.75 3h-9.5Z" /></svg>
            Stop & Analyze
          </button>
        </div>
      </div>
    </div>
  );
};

export default SessionControl;

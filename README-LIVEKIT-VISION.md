# Combat Mirror System - LiveKit Vision Integration

A real-time combat sports training system with AI-powered pose analysis, based on LiveKit's vision demo patterns but enhanced for combat sports applications.

## 🏗️ Architecture

### Core Components

1. **LiveKit Agent** (`agent/enhanced_combat_agent.py`)
   - Real-time video processing with MediaPipe
   - Combat-specific metrics calculation
   - Multi-camera support
   - Based on LiveKit Agents framework

2. **React Web Interface** (`src/`)
   - Multi-camera LiveKit streams
   - QR code generation for mobile connections
   - Real-time metrics display
   - Video upload and sync analysis

3. **iOS Mobile Client** (`ios-client/`)
   - Native SwiftUI app with LiveKit SDK
   - QR code scanning for easy connection
   - Camera switching and controls
   - Real-time video streaming to LiveKit server

4. **LiveKit Infrastructure** (`docker-compose.yml`)
   - LiveKit server with Redis
   - WebRTC signaling and media routing
   - Token-based authentication

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ and npm
- Python 3.9+ with pip
- iOS device with camera (for mobile streaming)

### 1. Start the Complete System
```bash
# One-command startup
./start-combat-mirror.sh
```

This script will:
- Start LiveKit server and Redis in Docker
- Install Python dependencies and start the AI agent
- Start the React web interface
- Display access URLs and mobile setup instructions

### 2. Access the System

**Desktop Interface:** http://localhost:5175
**Mobile Access:** http://[YOUR_NETWORK_IP]:5175
**LiveKit Server:** ws://localhost:7880

### 3. Connect Mobile Cameras

1. **Using iOS App:**
   - Open Xcode and build the iOS client
   - Install on your iPhone
   - Scan QR codes from the web interface

2. **Using Mobile Browser:**
   - Visit the mobile URL on your phone
   - Navigate to the camera-stream.html page
   - Allow camera permissions

## 📱 iOS Client Setup

### Building the iOS App

```bash
cd ios-client
# Open in Xcode
open CombatMirrorClient.swift

# Or build with Swift Package Manager
swift build
```

### Dependencies
- LiveKit Swift SDK 2.0+
- CodeScanner for QR code scanning
- iOS 14.0+ deployment target

## 🤖 AI Agent Features

### Real-time Metrics Calculated

- **Posture Score** (0-100): Spine alignment, shoulder/hip positioning
- **Balance Score** (0-100): Center of mass stability over base of support
- **Defense Rating** (0-100): Guard position and chin tuck quality
- **Head Movement**: Movement magnitude for head movement tracking
- **Punch Detection**: Real-time punch throw identification
- **Stance Analysis**: Orthodox vs Southpaw detection
- **Guard Position**: High/Mid/Low guard classification

### MediaPipe Integration

The agent uses MediaPipe Pose with:
- Model complexity: 2 (highest accuracy)
- 30 FPS processing rate
- Sub-frame pose landmark tracking
- Combat-specific metric algorithms

## 🔧 Configuration

### Environment Variables

```bash
# LiveKit Configuration
LIVEKIT_API_KEY="APIfightmaster"
LIVEKIT_API_SECRET="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ"
LIVEKIT_WS_URL="ws://localhost:7880"

# Web Server
VITE_LIVEKIT_URL="ws://localhost:7880"
```

### LiveKit Server Config

The system uses Docker Compose with:
- **LiveKit Server**: Ports 7880-7882
- **Redis**: Port 6379 (for LiveKit state)
- **Turn Server**: Built-in for NAT traversal

## 📊 Data Flow

1. **iPhone Camera** → LiveKit Room → **LiveKit Agent**
2. **Agent** processes video with MediaPipe → **Metrics Calculation**
3. **Metrics** sent back via LiveKit data channel → **Web Interface**
4. **Real-time Display** of combat metrics and video streams

## 🔍 Monitoring and Debugging

### Log Files
- **Agent Logs**: `agent/agent.log`
- **Web Logs**: `web.log`
- **Docker Logs**: `docker-compose logs -f`

### Health Checks
```bash
# Check LiveKit server
curl http://localhost:7880

# Check web server
curl http://localhost:5175

# Check agent status
ps aux | grep enhanced_combat_agent
```

## 🛠️ Development

### Adding New Metrics

1. Extend `PoseAnalyzer` class in `enhanced_combat_agent.py`
2. Add metric calculation method
3. Update metrics broadcasting in `_broadcast_metrics()`
4. Update web interface to display new metrics

### Camera Positions

The system supports 4 camera angles:
- **FRONT**: Primary analysis camera
- **LEFT**: Side view for stance analysis
- **RIGHT**: Opposite side view
- **BEHIND**: Rear view for posture assessment

### Testing

```bash
# Test individual components
cd agent && python enhanced_combat_agent.py
npm run dev
docker-compose up -d
```

## 🔒 Security

- Token-based authentication for LiveKit rooms
- HTTPS/WSS in production (configure in LiveKit config)
- Camera permissions handled by browser/iOS
- No permanent video storage (stream-only)

## 📈 Performance

### Optimizations
- 30 FPS pose processing for combat applications
- Adaptive streaming based on network conditions
- MediaPipe optimized for real-time performance
- Multiple camera streams with efficient processing

### Resource Usage
- **CPU**: ~15-25% per camera stream (processing)
- **Memory**: ~500MB for agent + MediaPipe models
- **Network**: ~2-5 Mbps per HD camera stream

## 🤝 Based on LiveKit Vision Demo

This implementation follows patterns from the [LiveKit Vision Demo](https://github.com/livekit-examples/vision-demo) but enhances it with:

- **Combat-specific metrics** instead of general AI conversation
- **Multi-camera support** for comprehensive analysis
- **Real-time pose analysis** with MediaPipe
- **Mobile-first design** for camera streaming
- **Professional training interface** for combat sports

## 📝 License

Based on LiveKit's open-source examples with combat sports enhancements.
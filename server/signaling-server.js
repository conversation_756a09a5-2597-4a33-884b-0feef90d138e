const { Server } = require('socket.io');
const http = require('http');

const server = http.createServer();
const io = new Server(server, {
  cors: {
    origin: "*",
    methods: ["GET", "POST"]
  }
});

// Store active rooms and their connections
const rooms = new Map();

io.on('connection', (socket) => {
  console.log('New connection:', socket.id);

  // Join a room with a specific camera ID
  socket.on('join-room', ({ roomId, type }) => {
    socket.join(roomId);
    
    if (!rooms.has(roomId)) {
      rooms.set(roomId, { desktop: null, mobile: null });
    }
    
    const room = rooms.get(roomId);
    room[type] = socket.id;
    
    console.log(`${type} joined room ${roomId}`);
    
    // Notify the other party if both are connected
    if (room.desktop && room.mobile) {
      io.to(roomId).emit('ready-to-connect');
    }
  });

  // Handle WebRTC signaling
  socket.on('signal', ({ roomId, signal, type }) => {
    socket.to(roomId).emit('signal', { signal, type });
  });

  // Handle disconnection
  socket.on('disconnect', () => {
    console.log('Disconnected:', socket.id);
    
    // Clean up rooms
    rooms.forEach((room, roomId) => {
      if (room.desktop === socket.id) {
        room.desktop = null;
        socket.to(roomId).emit('peer-disconnected', { type: 'desktop' });
      } else if (room.mobile === socket.id) {
        room.mobile = null;
        socket.to(roomId).emit('peer-disconnected', { type: 'mobile' });
      }
      
      // Remove empty rooms
      if (!room.desktop && !room.mobile) {
        rooms.delete(roomId);
      }
    });
  });
});

const PORT = process.env.SIGNAL_PORT || 3001;
server.listen(PORT, () => {
  console.log(`Signaling server running on port ${PORT}`);
});
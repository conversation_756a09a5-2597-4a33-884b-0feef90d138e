import tokenService from '../services/tokenService.js';
import logger from '../logger.js';

/**
 * Authentication middleware for Combat Mirror System
 */

/**
 * Extract token from request headers
 * @param {Object} req - Express request object
 * @returns {string|null} Token or null if not found
 */
function extractToken(req) {
  const authHeader = req.headers.authorization;
  
  if (authHeader && authHeader.startsWith('Bearer ')) {
    return authHeader.substring(7);
  }
  
  // Also check for token in cookies (for web sessions)
  if (req.cookies && req.cookies.authToken) {
    return req.cookies.authToken;
  }
  
  return null;
}

/**
 * Middleware to authenticate requests using JWT tokens
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const authenticateToken = (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (!token) {
      logger.warn('Authentication failed: No token provided', {
        ip: req.ip,
        userAgent: req.get('user-agent'),
        url: req.url
      });
      
      return res.status(401).json({
        error: 'Authentication required',
        message: 'No authentication token provided'
      });
    }

    // Verify token
    const decoded = tokenService.verifyAuthToken(token);
    
    // Add user info to request object
    req.user = {
      userId: decoded.userId,
      email: decoded.email,
      roles: decoded.roles,
      tokenId: decoded.jti
    };

    logger.debug('Request authenticated', {
      userId: decoded.userId,
      url: req.url,
      method: req.method
    });

    next();
  } catch (error) {
    logger.warn('Authentication failed: Invalid token', {
      error: error.message,
      ip: req.ip,
      userAgent: req.get('user-agent'),
      url: req.url
    });
    
    return res.status(401).json({
      error: 'Authentication failed',
      message: 'Invalid or expired token'
    });
  }
};

/**
 * Middleware to check if user has required roles
 * @param {string[]} requiredRoles - Array of required roles
 * @returns {Function} Middleware function
 */
export const requireRoles = (requiredRoles) => {
  return (req, res, next) => {
    if (!req.user) {
      return res.status(401).json({
        error: 'Authentication required',
        message: 'User not authenticated'
      });
    }

    const userRoles = req.user.roles || [];
    const hasRequiredRole = requiredRoles.some(role => userRoles.includes(role));

    if (!hasRequiredRole) {
      logger.warn('Authorization failed: Insufficient permissions', {
        userId: req.user.userId,
        userRoles,
        requiredRoles,
        url: req.url
      });

      return res.status(403).json({
        error: 'Insufficient permissions',
        message: `Required roles: ${requiredRoles.join(', ')}`
      });
    }

    next();
  };
};

/**
 * Middleware for optional authentication (doesn't fail if no token)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const optionalAuth = (req, res, next) => {
  try {
    const token = extractToken(req);
    
    if (token) {
      const decoded = tokenService.verifyAuthToken(token);
      req.user = {
        userId: decoded.userId,
        email: decoded.email,
        roles: decoded.roles,
        tokenId: decoded.jti
      };
    }
    
    next();
  } catch (error) {
    // Log but don't fail - this is optional auth
    logger.debug('Optional auth failed', { error: error.message });
    next();
  }
};

/**
 * Middleware to validate API keys for external integrations
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const validateApiKey = async (req, res, next) => {
  try {
    const apiKey = req.headers['x-api-key'];
    
    if (!apiKey) {
      return res.status(401).json({
        error: 'API key required',
        message: 'X-API-Key header is required'
      });
    }

    // Validate API key format
    if (!apiKey.startsWith('cm_') || apiKey.length !== 67) {
      return res.status(401).json({
        error: 'Invalid API key format'
      });
    }

    // Hash the provided key to compare with stored hash
    const crypto = await import('crypto');
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');

    // In production, this would check against a database
    // For now, we'll implement a basic validation
    // TODO: Implement proper API key storage and validation
    
    logger.info('API key validation attempted', {
      keyPrefix: apiKey.substring(0, 10) + '...',
      ip: req.ip
    });

    // Add API key info to request
    req.apiKey = {
      id: 'temp-id', // Would come from database
      purpose: 'external-integration'
    };

    next();
  } catch (error) {
    logger.error('API key validation error', { error: error.message });
    return res.status(500).json({
      error: 'API key validation failed'
    });
  }
};

/**
 * Middleware to check rate limits per user
 * @param {number} maxRequests - Maximum requests per window
 * @param {number} windowMs - Time window in milliseconds
 * @returns {Function} Middleware function
 */
export const userRateLimit = (maxRequests = 100, windowMs = 60000) => {
  const userRequests = new Map();

  return (req, res, next) => {
    const userId = req.user?.userId || req.ip;
    const now = Date.now();
    
    if (!userRequests.has(userId)) {
      userRequests.set(userId, []);
    }
    
    const requests = userRequests.get(userId);
    
    // Remove old requests outside the window
    const validRequests = requests.filter(time => now - time < windowMs);
    
    if (validRequests.length >= maxRequests) {
      logger.warn('Rate limit exceeded', {
        userId,
        requestCount: validRequests.length,
        maxRequests,
        windowMs
      });
      
      return res.status(429).json({
        error: 'Rate limit exceeded',
        message: `Maximum ${maxRequests} requests per ${windowMs / 1000} seconds`
      });
    }
    
    validRequests.push(now);
    userRequests.set(userId, validRequests);
    
    next();
  };
};

/**
 * Development-only middleware to skip authentication
 * NEVER use in production
 */
export const skipAuthInDev = (req, res, next) => {
  if (process.env.NODE_ENV === 'development' && process.env.SKIP_AUTH_IN_DEV === 'true') {
    logger.warn('DEVELOPMENT: Skipping authentication', {
      url: req.url,
      method: req.method
    });
    
    // Add mock user for development
    req.user = {
      userId: 'dev-user-001',
      email: '<EMAIL>',
      roles: ['admin', 'researcher'],
      tokenId: 'dev-token'
    };
  }
  
  next();
};

export default {
  authenticateToken,
  requireRoles,
  optionalAuth,
  validateApiKey,
  userRateLimit,
  skipAuthInDev
};

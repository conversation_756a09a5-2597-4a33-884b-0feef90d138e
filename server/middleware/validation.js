import { body, param, query, validationResult } from 'express-validator';
import DOMPurify from 'isomorphic-dompurify';
import logger from '../logger.js';

/**
 * Input validation and sanitization middleware for Combat Mirror System
 */

/**
 * Handle validation errors
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @param {Function} next - Next middleware function
 */
export const handleValidationErrors = (req, res, next) => {
  const errors = validationResult(req);
  
  if (!errors.isEmpty()) {
    logger.warn('Validation failed', {
      errors: errors.array(),
      url: req.url,
      method: req.method,
      ip: req.ip
    });
    
    return res.status(400).json({
      error: 'Validation failed',
      details: errors.array().map(err => ({
        field: err.path,
        message: err.msg,
        value: err.value
      }))
    });
  }
  
  next();
};

/**
 * Sanitize HTML content to prevent XSS
 * @param {string} html - HTML content to sanitize
 * @returns {string} Sanitized HTML
 */
export const sanitizeHtml = (html) => {
  if (typeof html !== 'string') return '';
  
  return DOMPurify.sanitize(html, {
    ALLOWED_TAGS: ['b', 'i', 'em', 'strong', 'p', 'br'],
    ALLOWED_ATTR: []
  });
};

/**
 * Validation rules for participant data
 */
export const validateParticipant = [
  body('participantId')
    .isLength({ min: 1, max: 50 })
    .matches(/^[a-zA-Z0-9-_]+$/)
    .withMessage('Participant ID must be 1-50 characters, alphanumeric, hyphens, and underscores only'),
  
  body('sessionType')
    .isIn([
      'Baseline Session (5 mins)',
      'Training Session (15 mins)', 
      'Recovery Analysis (5 mins)',
      'Video Analysis (15 mins)'
    ])
    .withMessage('Invalid session type'),
    
  handleValidationErrors
];

/**
 * Validation rules for metrics data
 */
export const validateMetrics = [
  body('metrics.punchRate')
    .isFloat({ min: 0, max: 200 })
    .withMessage('Punch rate must be between 0 and 200'),
    
  body('metrics.punchCount')
    .isInt({ min: 0, max: 10000 })
    .withMessage('Punch count must be between 0 and 10000'),
    
  body('metrics.punchVelocity')
    .isFloat({ min: 0, max: 20 })
    .withMessage('Punch velocity must be between 0 and 20'),
    
  body('metrics.headMovement')
    .isFloat({ min: 0, max: 10 })
    .withMessage('Head movement must be between 0 and 10'),
    
  body('metrics.postureScore')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Posture score must be between 0 and 100'),
    
  body('metrics.gaitBalance')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Gait balance must be between 0 and 100'),
    
  body('metrics.fatigue')
    .isFloat({ min: 0, max: 100 })
    .withMessage('Fatigue must be between 0 and 100'),
    
  handleValidationErrors
];

/**
 * Validation rules for file uploads
 */
export const validateFileUpload = [
  body('category')
    .optional()
    .isIn(['uploads', 'recordings', 'exports'])
    .withMessage('Category must be uploads, recordings, or exports'),
    
  // File validation happens in multer middleware
  (req, res, next) => {
    if (!req.file && !req.files) {
      return res.status(400).json({
        error: 'No file uploaded'
      });
    }
    
    const file = req.file || req.files[0];
    
    // Validate file type
    const allowedTypes = [
      'video/mp4',
      'video/webm', 
      'video/quicktime',
      'video/x-msvideo' // .avi
    ];
    
    if (!allowedTypes.includes(file.mimetype)) {
      return res.status(400).json({
        error: 'Invalid file type',
        message: 'Only MP4, WebM, MOV, and AVI files are allowed'
      });
    }
    
    // Validate file size (500MB max)
    const maxSize = 500 * 1024 * 1024;
    if (file.size > maxSize) {
      return res.status(400).json({
        error: 'File too large',
        message: 'Maximum file size is 500MB'
      });
    }
    
    // Sanitize filename
    const sanitizedFilename = file.originalname
      .replace(/[^a-zA-Z0-9.-]/g, '_')
      .substring(0, 255);
    
    file.sanitizedName = sanitizedFilename;
    
    next();
  },
  
  handleValidationErrors
];

/**
 * Validation rules for LiveKit token requests
 */
export const validateLiveKitToken = [
  body('roomName')
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9-_]+$/)
    .withMessage('Room name must be 1-100 characters, alphanumeric, hyphens, and underscores only'),
    
  body('participantName')
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z0-9-_]+$/)
    .withMessage('Participant name must be 1-100 characters, alphanumeric, hyphens, and underscores only'),
    
  body('canPublish')
    .optional()
    .isBoolean()
    .withMessage('canPublish must be a boolean'),
    
  body('canSubscribe')
    .optional()
    .isBoolean()
    .withMessage('canSubscribe must be a boolean'),
    
  body('canPublishData')
    .optional()
    .isBoolean()
    .withMessage('canPublishData must be a boolean'),
    
  handleValidationErrors
];

/**
 * Validation rules for analysis requests
 */
export const validateAnalysisRequest = [
  ...validateMetrics,
  ...validateParticipant,
  
  // Additional sanitization
  (req, res, next) => {
    // Sanitize any text fields
    if (req.body.participantId) {
      req.body.participantId = req.body.participantId.trim();
    }
    
    if (req.body.sessionType) {
      req.body.sessionType = req.body.sessionType.trim();
    }
    
    // Ensure metrics are properly typed
    if (req.body.metrics) {
      Object.keys(req.body.metrics).forEach(key => {
        const value = req.body.metrics[key];
        if (typeof value === 'string' && !isNaN(value)) {
          req.body.metrics[key] = parseFloat(value);
        }
      });
    }
    
    next();
  }
];

/**
 * Validation for pagination parameters
 */
export const validatePagination = [
  query('page')
    .optional()
    .isInt({ min: 1, max: 1000 })
    .withMessage('Page must be between 1 and 1000'),
    
  query('limit')
    .optional()
    .isInt({ min: 1, max: 100 })
    .withMessage('Limit must be between 1 and 100'),
    
  query('sortBy')
    .optional()
    .isIn(['createdAt', 'updatedAt', 'name', 'size'])
    .withMessage('Invalid sort field'),
    
  query('sortOrder')
    .optional()
    .isIn(['asc', 'desc'])
    .withMessage('Sort order must be asc or desc'),
    
  handleValidationErrors
];

/**
 * Validation for search parameters
 */
export const validateSearch = [
  query('q')
    .optional()
    .isLength({ min: 1, max: 100 })
    .withMessage('Search query must be 1-100 characters'),
    
  query('category')
    .optional()
    .isIn(['uploads', 'recordings', 'exports', 'all'])
    .withMessage('Invalid category'),
    
  // Sanitize search query
  (req, res, next) => {
    if (req.query.q) {
      req.query.q = sanitizeHtml(req.query.q.trim());
    }
    next();
  },
  
  handleValidationErrors
];

/**
 * Validation for user registration/login
 */
export const validateUserAuth = [
  body('email')
    .isEmail()
    .normalizeEmail()
    .withMessage('Valid email is required'),
    
  body('password')
    .isLength({ min: 8, max: 128 })
    .matches(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/)
    .withMessage('Password must be 8-128 characters with uppercase, lowercase, number, and special character'),
    
  body('name')
    .optional()
    .isLength({ min: 1, max: 100 })
    .matches(/^[a-zA-Z\s-']+$/)
    .withMessage('Name must be 1-100 characters, letters, spaces, hyphens, and apostrophes only'),
    
  // Sanitize name field
  (req, res, next) => {
    if (req.body.name) {
      req.body.name = sanitizeHtml(req.body.name.trim());
    }
    next();
  },
  
  handleValidationErrors
];

export default {
  handleValidationErrors,
  sanitizeHtml,
  validateParticipant,
  validateMetrics,
  validateFileUpload,
  validateLiveKitToken,
  validateAnalysisRequest,
  validatePagination,
  validateSearch,
  validateUserAuth
};

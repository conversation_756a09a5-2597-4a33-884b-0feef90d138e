{"name": "combat-mirror-server", "version": "1.0.0", "type": "module", "description": "Backend API server for Combat Mirror System", "main": "index.js", "scripts": {"start": "node index.js", "dev": "node --watch index.js", "build": "echo 'No build required'"}, "dependencies": {"@google/generative-ai": "^0.21.0", "axios": "^1.6.7", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^16.4.5", "express": "^4.21.2", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "multer": "^1.4.5-lts.1", "winston": "^3.17.0"}, "engines": {"node": ">=18.0.0"}}
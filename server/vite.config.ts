import { defineConfig, loadEnv } from 'vite';
import react from '@vitejs/plugin-react';
import basicSsl from '@vitejs/plugin-basic-ssl';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      plugins: [
        react(),
        basicSsl() // Enable HTTPS for mobile camera access
      ],
      define: {
        'process.env.VITE_API_URL': JSON.stringify(env.VITE_API_URL || 'http://localhost:3001'),
      },
      resolve: {
        alias: {
          '@': '.',
        }
      },
      server: {
        https: true, // Enable HTTPS
        host: '0.0.0.0', // Allow external connections
        port: 5173,
        fs: {
          strict: false
        }
      },
      build: {
        chunkSizeWarningLimit: 1000,
        rollupOptions: {
          output: {
            manualChunks: {
              'react-vendor': ['react', 'react-dom'],
              'livekit-vendor': ['livekit-client', 'livekit-server-sdk'],
              'mediapipe-vendor': ['@mediapipe/camera_utils', '@mediapipe/drawing_utils', '@mediapipe/pose'],
            }
          }
        }
      }
    };
});
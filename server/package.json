{"name": "combat-mirror-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "start": "vite preview --host 0.0.0.0 --port ${PORT:-5173}", "start:server": "cd server && npm start", "start:all": "npm run start:server & npm run preview"}, "dependencies": {"@google/genai": "^1.7.0", "@google/generative-ai": "^0.24.1", "@livekit/components-react": "^2.9.14", "@livekit/components-styles": "^1.1.6", "@mediapipe/camera_utils": "^0.3.1675466862", "@mediapipe/drawing_utils": "^0.3.1675466124", "@mediapipe/pose": "^0.5.1675469404", "axios": "^1.11.0", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "livekit-client": "^2.15.4", "livekit-server-sdk": "^2.13.1", "multer": "^2.0.2", "qrcode": "^1.5.4", "react": "^19.1.0", "react-dom": "^19.1.0", "uuid": "^11.1.0", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^22.14.0", "@types/qrcode": "^1.5.5", "@types/react": "^19.1.0", "@types/react-dom": "^19.1.0", "@vitejs/plugin-basic-ssl": "^2.1.0", "@vitejs/plugin-react": "^4.7.0", "autoprefixer": "^10.4.21", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "typescript": "~5.8.2", "vite": "^6.2.0"}}
node:internal/modules/package_json_reader:255
  throw new ERR_MODULE_NOT_FOUND(packageName, fileURLToPath(base), null);
        ^

Error [ERR_MODULE_NOT_FOUND]: Cannot find package 'express' imported from /Users/<USER>/Documents/Documents - Dan's MacBook Pro/CombatMirrorProjects/apps/combat-mirror-system/server/simple-server.js
    at Object.getPackageJSONURL (node:internal/modules/package_json_reader:255:9)
    at packageResolve (node:internal/modules/esm/resolve:767:81)
    at moduleResolve (node:internal/modules/esm/resolve:853:18)
    at defaultResolve (node:internal/modules/esm/resolve:983:11)
    at ModuleLoader.defaultResolve (node:internal/modules/esm/loader:801:12)
    at #cachedDefaultResolve (node:internal/modules/esm/loader:725:25)
    at ModuleLoader.resolve (node:internal/modules/esm/loader:708:38)
    at ModuleLoader.getModuleJobForImport (node:internal/modules/esm/loader:309:38)
    at #link (node:internal/modules/esm/module_job:201:49) {
  code: 'ERR_MODULE_NOT_FOUND'
}

Node.js v24.2.0

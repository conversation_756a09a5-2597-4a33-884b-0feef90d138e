{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm ci", "watchPatterns": ["*.js", "package.json"], "buildCache": true}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10, "region": "us-west1", "memoryLimitMB": 768, "strategy": "blueGreen", "healthcheckPath": "/api/health", "healthcheckTimeout": 30, "healthcheckGracePeriod": 60, "edgeRuntime": false, "volumes": [{"name": "api-uploads", "mountPath": "/app/uploads"}]}}
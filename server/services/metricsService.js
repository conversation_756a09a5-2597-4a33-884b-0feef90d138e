import prometheus from 'prom-client';
import logger from '../logger.js';

/**
 * Metrics collection service for Combat Mirror System
 * Provides Prometheus-compatible metrics for monitoring
 */
class MetricsService {
  constructor() {
    // Create a Registry to register the metrics
    this.register = new prometheus.Registry();
    
    // Add default metrics (CPU, memory, etc.)
    prometheus.collectDefaultMetrics({
      register: this.register,
      prefix: 'combat_mirror_',
    });

    this.initializeCustomMetrics();
  }

  /**
   * Initialize custom application metrics
   */
  initializeCustomMetrics() {
    // HTTP request metrics
    this.httpRequestDuration = new prometheus.Histogram({
      name: 'combat_mirror_http_request_duration_seconds',
      help: 'Duration of HTTP requests in seconds',
      labelNames: ['method', 'route', 'status_code'],
      buckets: [0.1, 0.3, 0.5, 0.7, 1, 3, 5, 7, 10],
      registers: [this.register]
    });

    this.httpRequestTotal = new prometheus.Counter({
      name: 'combat_mirror_http_requests_total',
      help: 'Total number of HTTP requests',
      labelNames: ['method', 'route', 'status_code'],
      registers: [this.register]
    });

    // Authentication metrics
    this.authAttempts = new prometheus.Counter({
      name: 'combat_mirror_auth_attempts_total',
      help: 'Total authentication attempts',
      labelNames: ['result', 'method'],
      registers: [this.register]
    });

    this.activeUsers = new prometheus.Gauge({
      name: 'combat_mirror_active_users',
      help: 'Number of currently active users',
      registers: [this.register]
    });

    // Video processing metrics
    this.videoUploads = new prometheus.Counter({
      name: 'combat_mirror_video_uploads_total',
      help: 'Total number of video uploads',
      labelNames: ['status', 'size_category'],
      registers: [this.register]
    });

    this.videoProcessingDuration = new prometheus.Histogram({
      name: 'combat_mirror_video_processing_duration_seconds',
      help: 'Duration of video processing in seconds',
      labelNames: ['type'],
      buckets: [1, 5, 10, 30, 60, 120, 300],
      registers: [this.register]
    });

    // Analysis metrics
    this.analysisRequests = new prometheus.Counter({
      name: 'combat_mirror_analysis_requests_total',
      help: 'Total number of analysis requests',
      labelNames: ['session_type', 'status'],
      registers: [this.register]
    });

    this.analysisLatency = new prometheus.Histogram({
      name: 'combat_mirror_analysis_latency_seconds',
      help: 'Latency of analysis requests in seconds',
      labelNames: ['session_type'],
      buckets: [1, 2, 5, 10, 15, 30, 60],
      registers: [this.register]
    });

    // LiveKit metrics
    this.livekitConnections = new prometheus.Gauge({
      name: 'combat_mirror_livekit_connections',
      help: 'Number of active LiveKit connections',
      registers: [this.register]
    });

    this.livekitTokens = new prometheus.Counter({
      name: 'combat_mirror_livekit_tokens_total',
      help: 'Total number of LiveKit tokens generated',
      labelNames: ['room_type'],
      registers: [this.register]
    });

    // Error metrics
    this.errors = new prometheus.Counter({
      name: 'combat_mirror_errors_total',
      help: 'Total number of errors',
      labelNames: ['type', 'severity'],
      registers: [this.register]
    });

    // Rate limiting metrics
    this.rateLimitHits = new prometheus.Counter({
      name: 'combat_mirror_rate_limit_hits_total',
      help: 'Total number of rate limit hits',
      labelNames: ['endpoint', 'user_type'],
      registers: [this.register]
    });

    // Business metrics
    this.sessionsStarted = new prometheus.Counter({
      name: 'combat_mirror_sessions_started_total',
      help: 'Total number of sessions started',
      labelNames: ['session_type'],
      registers: [this.register]
    });

    this.sessionsCompleted = new prometheus.Counter({
      name: 'combat_mirror_sessions_completed_total',
      help: 'Total number of sessions completed',
      labelNames: ['session_type'],
      registers: [this.register]
    });

    logger.info('Metrics service initialized with custom metrics');
  }

  /**
   * Middleware to track HTTP request metrics
   */
  httpMetricsMiddleware() {
    return (req, res, next) => {
      const start = Date.now();
      
      // Track request start
      const route = this.normalizeRoute(req.route?.path || req.path);
      
      res.on('finish', () => {
        const duration = (Date.now() - start) / 1000;
        const labels = {
          method: req.method,
          route: route,
          status_code: res.statusCode.toString()
        };

        this.httpRequestDuration.observe(labels, duration);
        this.httpRequestTotal.inc(labels);
      });

      next();
    };
  }

  /**
   * Record authentication attempt
   * @param {string} result - 'success' or 'failure'
   * @param {string} method - 'login', 'token', 'api_key'
   */
  recordAuthAttempt(result, method = 'login') {
    this.authAttempts.inc({ result, method });
    logger.debug('Auth attempt recorded', { result, method });
  }

  /**
   * Update active users count
   * @param {number} count - Current active user count
   */
  setActiveUsers(count) {
    this.activeUsers.set(count);
  }

  /**
   * Record video upload
   * @param {string} status - 'success' or 'failure'
   * @param {number} size - File size in bytes
   */
  recordVideoUpload(status, size) {
    const sizeCategory = this.getSizeCategory(size);
    this.videoUploads.inc({ status, size_category: sizeCategory });
    logger.debug('Video upload recorded', { status, size, sizeCategory });
  }

  /**
   * Record video processing duration
   * @param {string} type - Processing type
   * @param {number} duration - Duration in seconds
   */
  recordVideoProcessing(type, duration) {
    this.videoProcessingDuration.observe({ type }, duration);
  }

  /**
   * Record analysis request
   * @param {string} sessionType - Type of session
   * @param {string} status - 'success' or 'failure'
   * @param {number} latency - Request latency in seconds
   */
  recordAnalysisRequest(sessionType, status, latency) {
    this.analysisRequests.inc({ session_type: sessionType, status });
    if (latency) {
      this.analysisLatency.observe({ session_type: sessionType }, latency);
    }
  }

  /**
   * Record LiveKit connection change
   * @param {number} delta - Change in connection count (+1 or -1)
   */
  updateLivekitConnections(delta) {
    this.livekitConnections.inc(delta);
  }

  /**
   * Record LiveKit token generation
   * @param {string} roomType - Type of room
   */
  recordLivekitToken(roomType = 'default') {
    this.livekitTokens.inc({ room_type: roomType });
  }

  /**
   * Record error occurrence
   * @param {string} type - Error type
   * @param {string} severity - 'low', 'medium', 'high', 'critical'
   */
  recordError(type, severity = 'medium') {
    this.errors.inc({ type, severity });
    logger.debug('Error recorded in metrics', { type, severity });
  }

  /**
   * Record rate limit hit
   * @param {string} endpoint - API endpoint
   * @param {string} userType - 'authenticated' or 'anonymous'
   */
  recordRateLimitHit(endpoint, userType = 'anonymous') {
    this.rateLimitHits.inc({ endpoint, user_type: userType });
  }

  /**
   * Record session start
   * @param {string} sessionType - Type of session
   */
  recordSessionStart(sessionType) {
    this.sessionsStarted.inc({ session_type: sessionType });
  }

  /**
   * Record session completion
   * @param {string} sessionType - Type of session
   */
  recordSessionComplete(sessionType) {
    this.sessionsCompleted.inc({ session_type: sessionType });
  }

  /**
   * Get metrics in Prometheus format
   * @returns {string} Prometheus metrics
   */
  async getMetrics() {
    return await this.register.metrics();
  }

  /**
   * Get metrics as JSON
   * @returns {Object} Metrics data
   */
  async getMetricsJSON() {
    const metrics = await this.register.getMetricsAsJSON();
    return metrics;
  }

  /**
   * Normalize route for consistent labeling
   * @param {string} route - Original route
   * @returns {string} Normalized route
   */
  normalizeRoute(route) {
    if (!route) return 'unknown';
    
    // Replace dynamic segments with placeholders
    return route
      .replace(/\/\d+/g, '/:id')
      .replace(/\/[a-f0-9-]{36}/g, '/:uuid')
      .replace(/\/[a-zA-Z0-9-_]+\.(jpg|jpeg|png|gif|mp4|webm)/g, '/:file');
  }

  /**
   * Categorize file size
   * @param {number} size - File size in bytes
   * @returns {string} Size category
   */
  getSizeCategory(size) {
    if (size < 1024 * 1024) return 'small'; // < 1MB
    if (size < 10 * 1024 * 1024) return 'medium'; // < 10MB
    if (size < 100 * 1024 * 1024) return 'large'; // < 100MB
    return 'xlarge'; // >= 100MB
  }

  /**
   * Reset all metrics (for testing)
   */
  reset() {
    this.register.clear();
    this.initializeCustomMetrics();
  }
}

// Export singleton instance
export const metricsService = new MetricsService();
export default metricsService;

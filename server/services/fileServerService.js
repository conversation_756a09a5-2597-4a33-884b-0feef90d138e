// CopyParty File Server Integration Service
import axios from 'axios';
import FormData from 'form-data';
import fs from 'fs';
import path from 'path';
import logger from '../logger.js';

class FileServerService {
  constructor() {
    this.baseUrl = process.env.COPYPARTY_URL || 'http://copyparty:3923';
    this.uploadPath = '/uploads';
    this.recordingsPath = '/recordings';
    this.exportsPath = '/exports';
  }

  /**
   * Upload a file to CopyParty
   * @param {Buffer|Stream} fileData - File data to upload
   * @param {string} filename - Name of the file
   * @param {string} category - Category (uploads, recordings, exports)
   * @returns {Promise<Object>} Upload result with file URL
   */
  async uploadFile(fileData, filename, category = 'uploads') {
    try {
      const targetPath = this._getCategoryPath(category);
      const uploadUrl = `${this.baseUrl}${targetPath}/${filename}`;
      
      // Use PUT for direct upload (CopyParty supports this)
      const response = await axios.put(uploadUrl, fileData, {
        headers: {
          'Content-Type': 'application/octet-stream',
        },
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
      });

      const fileUrl = `${this.baseUrl}${targetPath}/${filename}`;
      logger.info(`File uploaded to CopyParty: ${fileUrl}`);

      return {
        success: true,
        url: fileUrl,
        filename,
        category,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('CopyParty upload error:', error);
      throw new Error(`Failed to upload file: ${error.message}`);
    }
  }

  /**
   * Upload multiple files
   * @param {Array} files - Array of {data, filename, category}
   * @returns {Promise<Array>} Upload results
   */
  async uploadMultiple(files) {
    const uploadPromises = files.map(file => 
      this.uploadFile(file.data, file.filename, file.category)
    );
    return Promise.all(uploadPromises);
  }

  /**
   * Get file list from a category
   * @param {string} category - Category to list
   * @returns {Promise<Array>} File list
   */
  async listFiles(category = 'uploads') {
    try {
      const targetPath = this._getCategoryPath(category);
      const response = await axios.get(`${this.baseUrl}${targetPath}/?lt`, {
        headers: {
          'Accept': 'application/json',
        },
      });

      // CopyParty returns HTML by default, parse JSON response
      if (response.data && response.data.files) {
        return response.data.files.map(file => ({
          name: file.name,
          size: file.size,
          modified: file.ts,
          url: `${this.baseUrl}${targetPath}/${file.name}`,
        }));
      }

      return [];
    } catch (error) {
      logger.error('CopyParty list error:', error);
      throw new Error(`Failed to list files: ${error.message}`);
    }
  }

  /**
   * Delete a file
   * @param {string} filename - File to delete
   * @param {string} category - Category
   * @returns {Promise<Object>} Deletion result
   */
  async deleteFile(filename, category = 'uploads') {
    try {
      const targetPath = this._getCategoryPath(category);
      const deleteUrl = `${this.baseUrl}${targetPath}/${filename}`;
      
      await axios.delete(deleteUrl);
      
      logger.info(`File deleted from CopyParty: ${filename}`);
      return {
        success: true,
        filename,
        category,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('CopyParty delete error:', error);
      throw new Error(`Failed to delete file: ${error.message}`);
    }
  }

  /**
   * Move file between categories
   * @param {string} filename - File to move
   * @param {string} fromCategory - Source category
   * @param {string} toCategory - Destination category
   * @returns {Promise<Object>} Move result
   */
  async moveFile(filename, fromCategory, toCategory) {
    try {
      const fromPath = this._getCategoryPath(fromCategory);
      const toPath = this._getCategoryPath(toCategory);
      
      const moveData = {
        op: 'move',
        src: `${fromPath}/${filename}`,
        dst: `${toPath}/${filename}`,
      };

      await axios.post(`${this.baseUrl}/`, moveData, {
        headers: {
          'Content-Type': 'application/json',
        },
      });

      logger.info(`File moved: ${filename} from ${fromCategory} to ${toCategory}`);
      return {
        success: true,
        filename,
        fromCategory,
        toCategory,
        newUrl: `${this.baseUrl}${toPath}/${filename}`,
        timestamp: new Date().toISOString(),
      };
    } catch (error) {
      logger.error('CopyParty move error:', error);
      throw new Error(`Failed to move file: ${error.message}`);
    }
  }

  /**
   * Generate a shareable link for a file
   * @param {string} filename - File to share
   * @param {string} category - Category
   * @param {number} expiryHours - Link expiry in hours (0 = permanent)
   * @returns {Promise<Object>} Share link details
   */
  async createShareLink(filename, category = 'uploads', expiryHours = 24) {
    try {
      const targetPath = this._getCategoryPath(category);
      const fileUrl = `${this.baseUrl}${targetPath}/${filename}`;
      
      // CopyParty supports direct linking, so we'll return the direct URL
      // For temporary links, you might need to implement additional logic
      return {
        success: true,
        url: fileUrl,
        filename,
        category,
        expiry: expiryHours > 0 ? new Date(Date.now() + expiryHours * 3600000).toISOString() : null,
        permanent: expiryHours === 0,
      };
    } catch (error) {
      logger.error('CopyParty share link error:', error);
      throw new Error(`Failed to create share link: ${error.message}`);
    }
  }

  /**
   * Get file metadata
   * @param {string} filename - File to check
   * @param {string} category - Category
   * @returns {Promise<Object>} File metadata
   */
  async getFileInfo(filename, category = 'uploads') {
    try {
      const targetPath = this._getCategoryPath(category);
      const response = await axios.head(`${this.baseUrl}${targetPath}/${filename}`);
      
      return {
        exists: true,
        filename,
        category,
        size: parseInt(response.headers['content-length'] || '0'),
        contentType: response.headers['content-type'],
        lastModified: response.headers['last-modified'],
        url: `${this.baseUrl}${targetPath}/${filename}`,
      };
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return { exists: false, filename, category };
      }
      logger.error('CopyParty file info error:', error);
      throw new Error(`Failed to get file info: ${error.message}`);
    }
  }

  /**
   * Helper to get category path
   * @param {string} category - Category name
   * @returns {string} Path for category
   */
  _getCategoryPath(category) {
    const paths = {
      uploads: this.uploadPath,
      recordings: this.recordingsPath,
      exports: this.exportsPath,
    };
    return paths[category] || this.uploadPath;
  }

  /**
   * Health check for CopyParty server
   * @returns {Promise<boolean>} Server health status
   */
  async healthCheck() {
    try {
      const response = await axios.get(`${this.baseUrl}/`, { timeout: 5000 });
      return response.status === 200;
    } catch (error) {
      logger.error('CopyParty health check failed:', error);
      return false;
    }
  }
}

export default new FileServerService();
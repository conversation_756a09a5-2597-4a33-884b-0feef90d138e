import { AccessToken } from 'livekit-server-sdk';
import jwt from 'jsonwebtoken';
import crypto from 'crypto';
import logger from '../logger.js';

/**
 * Secure Token Service for Combat Mirror System
 * Handles JWT tokens for authentication and LiveKit tokens for video streaming
 */
class TokenService {
  constructor() {
    // Validate required environment variables
    this.validateEnvironment();
    
    this.jwtSecret = process.env.JWT_SECRET;
    this.jwtExpiresIn = process.env.JWT_EXPIRES_IN || '24h';
    this.livekitApiKey = process.env.LIVEKIT_API_KEY;
    this.livekitApiSecret = process.env.LIVEKIT_API_SECRET;
  }

  /**
   * Validate that all required environment variables are present
   */
  validateEnvironment() {
    const required = [
      'JWT_SECRET',
      'LIVEKIT_API_KEY', 
      'LIVEKIT_API_SECRET'
    ];

    const missing = required.filter(key => !process.env[key]);
    
    if (missing.length > 0) {
      logger.error('Missing required environment variables', { missing });
      throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
    }
  }

  /**
   * Generate a secure JWT token for user authentication
   * @param {Object} payload - User data to include in token
   * @param {string} payload.userId - User ID
   * @param {string} payload.email - User email
   * @param {string[]} payload.roles - User roles
   * @returns {string} JWT token
   */
  generateAuthToken(payload) {
    try {
      // Sanitize payload
      const sanitizedPayload = {
        userId: payload.userId,
        email: payload.email,
        roles: payload.roles || ['user'],
        iat: Math.floor(Date.now() / 1000),
        jti: crypto.randomUUID() // Unique token ID for revocation
      };

      const token = jwt.sign(sanitizedPayload, this.jwtSecret, {
        expiresIn: this.jwtExpiresIn,
        issuer: 'combat-mirror-system',
        audience: 'combat-mirror-client'
      });

      logger.info('Auth token generated', { 
        userId: payload.userId,
        tokenId: sanitizedPayload.jti 
      });

      return token;
    } catch (error) {
      logger.error('Failed to generate auth token', { error: error.message });
      throw new Error('Token generation failed');
    }
  }

  /**
   * Verify and decode a JWT token
   * @param {string} token - JWT token to verify
   * @returns {Object} Decoded token payload
   */
  verifyAuthToken(token) {
    try {
      const decoded = jwt.verify(token, this.jwtSecret, {
        issuer: 'combat-mirror-system',
        audience: 'combat-mirror-client'
      });

      logger.debug('Auth token verified', { 
        userId: decoded.userId,
        tokenId: decoded.jti 
      });

      return decoded;
    } catch (error) {
      logger.warn('Auth token verification failed', { 
        error: error.message,
        tokenPreview: token?.substring(0, 20) + '...'
      });
      throw new Error('Invalid or expired token');
    }
  }

  /**
   * Generate a LiveKit access token for video streaming
   * @param {string} roomName - LiveKit room name
   * @param {string} participantName - Participant identifier
   * @param {Object} options - Token options
   * @param {boolean} options.canPublish - Can publish video/audio
   * @param {boolean} options.canSubscribe - Can subscribe to streams
   * @param {boolean} options.canPublishData - Can publish data messages
   * @param {string} options.ttl - Token time-to-live
   * @returns {string} LiveKit access token
   */
  generateLiveKitToken(roomName, participantName, options = {}) {
    try {
      // Validate inputs
      if (!roomName || !participantName) {
        throw new Error('Room name and participant name are required');
      }

      // Sanitize inputs
      const sanitizedRoomName = roomName.replace(/[^a-zA-Z0-9-_]/g, '');
      const sanitizedParticipantName = participantName.replace(/[^a-zA-Z0-9-_]/g, '');

      const at = new AccessToken(this.livekitApiKey, this.livekitApiSecret, {
        identity: sanitizedParticipantName,
        ttl: options.ttl || '2h', // Shorter TTL for security
      });

      at.addGrant({
        roomJoin: true,
        room: sanitizedRoomName,
        canPublish: options.canPublish !== false, // Default true
        canSubscribe: options.canSubscribe !== false, // Default true
        canPublishData: options.canPublishData !== false, // Default true
      });

      const token = at.toJwt();

      logger.info('LiveKit token generated', {
        room: sanitizedRoomName,
        participant: sanitizedParticipantName,
        permissions: {
          canPublish: options.canPublish !== false,
          canSubscribe: options.canSubscribe !== false,
          canPublishData: options.canPublishData !== false
        }
      });

      return token;
    } catch (error) {
      logger.error('Failed to generate LiveKit token', { 
        error: error.message,
        roomName,
        participantName 
      });
      throw new Error('LiveKit token generation failed');
    }
  }

  /**
   * Generate a secure session token for temporary access
   * @param {string} sessionId - Session identifier
   * @param {Object} metadata - Session metadata
   * @returns {string} Session token
   */
  generateSessionToken(sessionId, metadata = {}) {
    try {
      const payload = {
        sessionId,
        metadata,
        type: 'session',
        iat: Math.floor(Date.now() / 1000),
        jti: crypto.randomUUID()
      };

      const token = jwt.sign(payload, this.jwtSecret, {
        expiresIn: '1h', // Short-lived session tokens
        issuer: 'combat-mirror-system',
        audience: 'combat-mirror-session'
      });

      logger.info('Session token generated', { 
        sessionId,
        tokenId: payload.jti 
      });

      return token;
    } catch (error) {
      logger.error('Failed to generate session token', { error: error.message });
      throw new Error('Session token generation failed');
    }
  }

  /**
   * Refresh an existing auth token
   * @param {string} token - Current token
   * @returns {string} New token
   */
  refreshAuthToken(token) {
    try {
      const decoded = this.verifyAuthToken(token);
      
      // Generate new token with same payload but new expiration
      const newPayload = {
        userId: decoded.userId,
        email: decoded.email,
        roles: decoded.roles
      };

      return this.generateAuthToken(newPayload);
    } catch (error) {
      logger.error('Failed to refresh auth token', { error: error.message });
      throw new Error('Token refresh failed');
    }
  }

  /**
   * Generate a secure API key for external integrations
   * @param {string} purpose - Purpose of the API key
   * @returns {Object} API key and metadata
   */
  generateApiKey(purpose) {
    const apiKey = 'cm_' + crypto.randomBytes(32).toString('hex');
    const hashedKey = crypto.createHash('sha256').update(apiKey).digest('hex');
    
    const metadata = {
      id: crypto.randomUUID(),
      purpose,
      hashedKey,
      createdAt: new Date().toISOString(),
      lastUsed: null,
      isActive: true
    };

    logger.info('API key generated', { 
      keyId: metadata.id,
      purpose 
    });

    return {
      apiKey, // Return this once to the user
      metadata // Store this in database
    };
  }
}

// Export singleton instance
export const tokenService = new TokenService();
export default tokenService;

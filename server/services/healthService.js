import logger from '../logger.js';
import fileServerService from './fileServerService.js';

/**
 * Health check service for Combat Mirror System
 * Monitors system health and dependencies
 */
class HealthService {
  constructor() {
    this.checks = new Map();
    this.lastResults = new Map();
    this.initializeChecks();
  }

  /**
   * Initialize health checks
   */
  initializeChecks() {
    // Register health checks
    this.registerCheck('database', this.checkDatabase.bind(this));
    this.registerCheck('fileServer', this.checkFileServer.bind(this));
    this.registerCheck('geminiApi', this.checkGeminiApi.bind(this));
    this.registerCheck('memory', this.checkMemory.bind(this));
    this.registerCheck('disk', this.checkDisk.bind(this));
    this.registerCheck('livekit', this.checkLiveKit.bind(this));

    logger.info('Health checks initialized', {
      checks: Array.from(this.checks.keys())
    });
  }

  /**
   * Register a health check
   * @param {string} name - Check name
   * @param {Function} checkFunction - Function that returns health status
   */
  registerCheck(name, checkFunction) {
    this.checks.set(name, checkFunction);
  }

  /**
   * Run all health checks
   * @returns {Object} Health status
   */
  async runAllChecks() {
    const results = {};
    const startTime = Date.now();

    for (const [name, checkFunction] of this.checks) {
      try {
        const checkStart = Date.now();
        const result = await Promise.race([
          checkFunction(),
          this.timeout(5000, `Health check ${name} timed out`)
        ]);
        
        results[name] = {
          status: 'healthy',
          responseTime: Date.now() - checkStart,
          ...result
        };
      } catch (error) {
        results[name] = {
          status: 'unhealthy',
          error: error.message,
          responseTime: Date.now() - checkStart
        };
        
        logger.warn(`Health check failed: ${name}`, {
          error: error.message,
          check: name
        });
      }
    }

    // Store results for comparison
    this.lastResults.set('all', results);

    // Calculate overall health
    const unhealthyChecks = Object.entries(results)
      .filter(([_, result]) => result.status === 'unhealthy');

    const overallStatus = unhealthyChecks.length === 0 ? 'healthy' : 'unhealthy';

    const healthReport = {
      status: overallStatus,
      timestamp: new Date().toISOString(),
      responseTime: Date.now() - startTime,
      checks: results,
      summary: {
        total: Object.keys(results).length,
        healthy: Object.keys(results).length - unhealthyChecks.length,
        unhealthy: unhealthyChecks.length
      }
    };

    if (overallStatus === 'unhealthy') {
      logger.error('System health check failed', {
        unhealthyChecks: unhealthyChecks.map(([name]) => name),
        summary: healthReport.summary
      });
    }

    return healthReport;
  }

  /**
   * Run a specific health check
   * @param {string} checkName - Name of the check to run
   * @returns {Object} Check result
   */
  async runCheck(checkName) {
    const checkFunction = this.checks.get(checkName);
    if (!checkFunction) {
      throw new Error(`Health check '${checkName}' not found`);
    }

    try {
      const startTime = Date.now();
      const result = await Promise.race([
        checkFunction(),
        this.timeout(5000, `Health check ${checkName} timed out`)
      ]);

      return {
        status: 'healthy',
        responseTime: Date.now() - startTime,
        ...result
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        error: error.message,
        responseTime: Date.now() - startTime
      };
    }
  }

  /**
   * Check database connectivity
   */
  async checkDatabase() {
    // TODO: Implement actual database check when database is added
    // For now, simulate a check
    return {
      message: 'Database check not implemented (no database configured)',
      details: {
        type: 'postgresql',
        status: 'not_configured'
      }
    };
  }

  /**
   * Check file server (CopyParty) connectivity
   */
  async checkFileServer() {
    try {
      const isHealthy = await fileServerService.healthCheck();
      
      if (isHealthy) {
        return {
          message: 'File server is accessible',
          details: {
            service: 'CopyParty',
            url: process.env.COPYPARTY_URL || 'http://localhost:3923'
          }
        };
      } else {
        throw new Error('File server is not responding');
      }
    } catch (error) {
      throw new Error(`File server check failed: ${error.message}`);
    }
  }

  /**
   * Check Gemini API connectivity
   */
  async checkGeminiApi() {
    if (!process.env.GEMINI_API_KEY) {
      throw new Error('Gemini API key not configured');
    }

    // Simple check - just verify the key is present and formatted correctly
    const keyFormat = /^[A-Za-z0-9_-]+$/;
    if (!keyFormat.test(process.env.GEMINI_API_KEY)) {
      throw new Error('Gemini API key format is invalid');
    }

    return {
      message: 'Gemini API configuration is valid',
      details: {
        keyConfigured: true,
        keyLength: process.env.GEMINI_API_KEY.length
      }
    };
  }

  /**
   * Check memory usage
   */
  async checkMemory() {
    const memUsage = process.memoryUsage();
    const totalMem = memUsage.heapTotal;
    const usedMem = memUsage.heapUsed;
    const memoryUsagePercent = (usedMem / totalMem) * 100;

    // Consider unhealthy if memory usage > 90%
    if (memoryUsagePercent > 90) {
      throw new Error(`High memory usage: ${memoryUsagePercent.toFixed(2)}%`);
    }

    return {
      message: 'Memory usage is normal',
      details: {
        heapUsed: Math.round(usedMem / 1024 / 1024), // MB
        heapTotal: Math.round(totalMem / 1024 / 1024), // MB
        usagePercent: Math.round(memoryUsagePercent),
        external: Math.round(memUsage.external / 1024 / 1024), // MB
        rss: Math.round(memUsage.rss / 1024 / 1024) // MB
      }
    };
  }

  /**
   * Check disk space
   */
  async checkDisk() {
    try {
      const fs = await import('fs');
      const stats = fs.statSync('.');
      
      // This is a basic check - in production you'd want to check actual disk usage
      return {
        message: 'Disk access is working',
        details: {
          accessible: true,
          // In production, add actual disk space checks here
        }
      };
    } catch (error) {
      throw new Error(`Disk check failed: ${error.message}`);
    }
  }

  /**
   * Check LiveKit connectivity
   */
  async checkLiveKit() {
    const livekitUrl = process.env.LIVEKIT_URL || 'ws://localhost:7880';
    
    if (!process.env.LIVEKIT_API_KEY || !process.env.LIVEKIT_API_SECRET) {
      throw new Error('LiveKit credentials not configured');
    }

    // Basic configuration check
    return {
      message: 'LiveKit configuration is valid',
      details: {
        url: livekitUrl,
        credentialsConfigured: true
      }
    };
  }

  /**
   * Get readiness status (for Kubernetes readiness probes)
   * @returns {Object} Readiness status
   */
  async getReadiness() {
    // Check critical dependencies only
    const criticalChecks = ['fileServer', 'geminiApi'];
    const results = {};

    for (const checkName of criticalChecks) {
      results[checkName] = await this.runCheck(checkName);
    }

    const isReady = Object.values(results).every(result => result.status === 'healthy');

    return {
      status: isReady ? 'ready' : 'not_ready',
      timestamp: new Date().toISOString(),
      checks: results
    };
  }

  /**
   * Get liveness status (for Kubernetes liveness probes)
   * @returns {Object} Liveness status
   */
  async getLiveness() {
    // Basic liveness check - just verify the process is responsive
    return {
      status: 'alive',
      timestamp: new Date().toISOString(),
      uptime: process.uptime(),
      pid: process.pid
    };
  }

  /**
   * Timeout helper
   * @param {number} ms - Timeout in milliseconds
   * @param {string} message - Error message
   * @returns {Promise} Promise that rejects after timeout
   */
  timeout(ms, message) {
    return new Promise((_, reject) => {
      setTimeout(() => reject(new Error(message)), ms);
    });
  }

  /**
   * Get health check history
   * @returns {Object} Historical health data
   */
  getHistory() {
    return {
      lastCheck: this.lastResults.get('all'),
      availableChecks: Array.from(this.checks.keys())
    };
  }
}

// Export singleton instance
export const healthService = new HealthService();
export default healthService;

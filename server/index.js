import express from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import rateLimit from 'express-rate-limit';
import dotenv from 'dotenv';
import multer from 'multer';
import { GoogleGenerativeAI } from '@google/generative-ai';
import logger from './logger.js';
import fileServerService from './services/fileServerService.js';
import tokenService from './services/tokenService.js';
import authMiddleware from './middleware/auth.js';
import validationMiddleware from './middleware/validation.js';

// Load environment variables
dotenv.config();

const app = express();
const PORT = process.env.PORT || 3001;

// Enhanced security middleware
app.use(helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"], // Required for some CSS frameworks
      imgSrc: ["'self'", "data:", "https:"],
      connectSrc: ["'self'", "wss:", "ws:"], // Allow WebSocket connections
      fontSrc: ["'self'", "https:"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'", "blob:"],
      frameSrc: ["'none'"],
    },
  },
  hsts: {
    maxAge: 31536000, // 1 year
    includeSubDomains: true,
    preload: true
  },
  noSniff: true,
  xssFilter: true,
  referrerPolicy: { policy: "strict-origin-when-cross-origin" }
}));
app.use(compression());

// Rate limiting
const limiter = rateLimit({
  windowMs: parseInt(process.env.RATE_LIMIT_WINDOW_MS || '60000'),
  max: parseInt(process.env.RATE_LIMIT_MAX_REQUESTS || '100'),
  message: 'Too many requests from this IP, please try again later.',
  standardHeaders: true,
  legacyHeaders: false,
});
app.use('/api', limiter);

// CORS configuration
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = (process.env.CORS_ORIGIN || 'http://localhost:5173').split(',');
    if (!origin || allowedOrigins.indexOf(origin) !== -1) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  optionsSuccessStatus: 200
};
app.use(cors(corsOptions));

// Other middleware
app.use(logger.requestMiddleware);
app.use(express.json({ limit: '10mb' }));
app.use(express.urlencoded({ extended: true, limit: '10mb' }));

// Validate environment variables
if (!process.env.GEMINI_API_KEY) {
  logger.error('GEMINI_API_KEY not found in environment variables');
  process.exit(1);
}

// Initialize Gemini AI with server-side API key
const genAI = new GoogleGenerativeAI(process.env.GEMINI_API_KEY);

// Configure multer for file uploads
const upload = multer({ 
  storage: multer.memoryStorage(),
  limits: { fileSize: 500 * 1024 * 1024 } // 500MB limit
});

// Health check endpoint (public)
app.get('/api/health', (req, res) => {
  const health = {
    status: 'ok',
    timestamp: new Date().toISOString(),
    uptime: process.uptime(),
    environment: process.env.NODE_ENV,
    version: process.env.RAILWAY_DEPLOYMENT_ID || 'local'
  };
  logger.info('Health check requested', health);
  res.json(health);
});

// Authentication endpoints
app.post('/api/auth/login', validationMiddleware.validateUserAuth, async (req, res) => {
  try {
    const { email, password } = req.body;

    // TODO: Implement proper user authentication with database
    // For now, using a simple demo user
    if (email === '<EMAIL>' && password === 'Demo123!@#') {
      const token = tokenService.generateAuthToken({
        userId: 'demo-user-001',
        email: email,
        roles: ['user', 'researcher']
      });

      logger.info('User logged in', { email });

      res.json({
        success: true,
        token,
        user: {
          userId: 'demo-user-001',
          email: email,
          roles: ['user', 'researcher']
        }
      });
    } else {
      logger.warn('Login failed: Invalid credentials', { email });
      res.status(401).json({
        error: 'Invalid credentials'
      });
    }
  } catch (error) {
    logger.error('Login error', { error: error.message });
    res.status(500).json({ error: 'Login failed' });
  }
});

app.post('/api/auth/refresh', authMiddleware.authenticateToken, (req, res) => {
  try {
    const newToken = tokenService.refreshAuthToken(req.headers.authorization.substring(7));
    res.json({ token: newToken });
  } catch (error) {
    logger.error('Token refresh error', { error: error.message });
    res.status(401).json({ error: 'Token refresh failed' });
  }
});

app.post('/api/auth/logout', authMiddleware.authenticateToken, (req, res) => {
  // TODO: Implement token blacklisting
  logger.info('User logged out', { userId: req.user.userId });
  res.json({ success: true });
});

// LiveKit token endpoint (authenticated)
app.post('/api/livekit/token',
  authMiddleware.authenticateToken,
  validationMiddleware.validateLiveKitToken,
  (req, res) => {
    try {
      const { roomName, participantName, canPublish, canSubscribe, canPublishData } = req.body;

      const token = tokenService.generateLiveKitToken(roomName, participantName, {
        canPublish,
        canSubscribe,
        canPublishData
      });

      res.json({
        token,
        serverUrl: process.env.LIVEKIT_URL || 'ws://localhost:7880'
      });
    } catch (error) {
      logger.error('LiveKit token generation error', { error: error.message });
      res.status(500).json({ error: 'Token generation failed' });
    }
  }
);

// Analysis endpoint - secure API proxy (authenticated)
app.post('/api/analyze',
  authMiddleware.authenticateToken,
  authMiddleware.userRateLimit(10, 60000), // 10 requests per minute per user
  validationMiddleware.validateAnalysisRequest,
  async (req, res) => {
  try {
    const { metrics, participantId, sessionType } = req.body;

    // Validate input
    if (!metrics || !participantId || !sessionType) {
      logger.warn('Invalid analysis request', { 
        hasMetrics: !!metrics, 
        hasParticipantId: !!participantId, 
        hasSessionType: !!sessionType 
      });
      return res.status(400).json({ 
        error: 'Missing required fields: metrics, participantId, sessionType' 
      });
    }

    logger.info('Starting analysis', { participantId, sessionType });

    // Generate prompt
    const prompt = `
You are a world-class biomechanics expert and data analyst for the "Combat Mirror" research project. 
Your task is to analyze the following session data for a combat athlete and provide a comprehensive performance report. 
The data was collected using a multi-camera system and real-time pose estimation.

**Session Details:**
- Participant ID: ${participantId}
- Session Type: ${sessionType}
- Session Duration: ${sessionType.includes('15 mins') ? '15 minutes' : '5 minutes'}

**Biomechanical Data Summary:**
- **Punch Detection Rate:** ${metrics.punchRate.toFixed(1)} punches/min, Total: ${metrics.punchCount} punches.
- **Punch Mechanics:** Average Velocity: ${metrics.punchVelocity.toFixed(2)} m/s.
- **Head Movement:** Average head position variance: ${metrics.headMovement.toFixed(2)} cm.
- **Posture Score:** ${metrics.postureScore.toFixed(0)}/100 (average).
- **Gait & Footwork:** Average balance stability: ${metrics.gaitBalance.toFixed(1)}%.
- **Fatigue Index:** ${metrics.fatigue.toFixed(1)}% performance decline over session.

**Your Task:**
Based on this data, generate a structured report in Markdown format. The report should include:
1.  **Executive Summary:** A brief, high-level overview of the athlete's performance.
2.  **Detailed Analysis by Variable:** For each of the 6 core variables, provide a detailed breakdown. Explain what the numbers mean, identify strengths, and pinpoint areas for improvement.
3.  **Performance Insights:** Synthesize the data to identify overarching patterns. For example, how did fatigue impact posture and punch mechanics?
4.  **Actionable Recommendations:** Provide 3-5 concrete, evidence-based recommendations for the athlete and their coach to improve performance, technique, or conditioning.
5.  **Research Notes:** Briefly mention any data anomalies or interesting patterns that might warrant further investigation in the context of the Combat Mirror study.
`;

    // Call Gemini API
    const model = genAI.getGenerativeModel({ model: 'gemini-1.5-flash' });
    const result = await model.generateContent(prompt);
    const response = result.response;
    const text = response.text();

    logger.info('Analysis completed successfully', { 
      participantId, 
      reportLength: text.length 
    });

    res.json({ report: text });

  } catch (error) {
    logger.error('Analysis error', { 
      error: error.message, 
      stack: error.stack,
      participantId: req.body?.participantId 
    });
    res.status(500).json({ 
      error: 'Failed to generate analysis report',
      message: error.message 
    });
  }
});

// File server endpoints
// Upload file endpoint
app.post('/api/files/upload', upload.single('file'), async (req, res) => {
  try {
    if (!req.file) {
      return res.status(400).json({ error: 'No file uploaded' });
    }

    const { category = 'uploads' } = req.body;
    const result = await fileServerService.uploadFile(
      req.file.buffer,
      req.file.originalname,
      category
    );

    logger.info('File uploaded successfully', result);

    // Trigger video processing if it's a video file
    if (isVideoFile(req.file.originalname)) {
      // Queue video for processing - in a real system this would be async
      logger.info('Video file detected, queuing for analysis', { 
        filename: req.file.originalname,
        category 
      });
      
      // You could add webhook triggers here or queue jobs
      // For now, just log that processing would happen
    }

    res.json(result);
  } catch (error) {
    logger.error('File upload error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// Upload multiple files
app.post('/api/files/upload-multiple', upload.array('files', 10), async (req, res) => {
  try {
    if (!req.files || req.files.length === 0) {
      return res.status(400).json({ error: 'No files uploaded' });
    }

    const { category = 'uploads' } = req.body;
    const files = req.files.map(file => ({
      data: file.buffer,
      filename: file.originalname,
      category
    }));

    const results = await fileServerService.uploadMultiple(files);
    logger.info('Multiple files uploaded', { count: results.length });
    res.json(results);
  } catch (error) {
    logger.error('Multiple file upload error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// List files endpoint
app.get('/api/files/list/:category?', async (req, res) => {
  try {
    const category = req.params.category || 'uploads';
    const files = await fileServerService.listFiles(category);
    res.json({ category, files });
  } catch (error) {
    logger.error('File list error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// Delete file endpoint
app.delete('/api/files/:category/:filename', async (req, res) => {
  try {
    const { category, filename } = req.params;
    const result = await fileServerService.deleteFile(filename, category);
    logger.info('File deleted', result);
    res.json(result);
  } catch (error) {
    logger.error('File delete error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// Move file endpoint
app.post('/api/files/move', async (req, res) => {
  try {
    const { filename, fromCategory, toCategory } = req.body;
    
    if (!filename || !fromCategory || !toCategory) {
      return res.status(400).json({ error: 'Missing required parameters' });
    }

    const result = await fileServerService.moveFile(filename, fromCategory, toCategory);
    logger.info('File moved', result);
    res.json(result);
  } catch (error) {
    logger.error('File move error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// Create share link endpoint
app.post('/api/files/share', async (req, res) => {
  try {
    const { filename, category = 'uploads', expiryHours = 24 } = req.body;
    
    if (!filename) {
      return res.status(400).json({ error: 'Filename is required' });
    }

    const result = await fileServerService.createShareLink(filename, category, expiryHours);
    logger.info('Share link created', result);
    res.json(result);
  } catch (error) {
    logger.error('Share link error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// Get file info endpoint
app.get('/api/files/info/:category/:filename', async (req, res) => {
  try {
    const { category, filename } = req.params;
    const info = await fileServerService.getFileInfo(filename, category);
    res.json(info);
  } catch (error) {
    logger.error('File info error', { error: error.message });
    res.status(500).json({ error: error.message });
  }
});

// File server health check
app.get('/api/files/health', async (req, res) => {
  try {
    const isHealthy = await fileServerService.healthCheck();
    res.json({ 
      status: isHealthy ? 'healthy' : 'unhealthy',
      service: 'CopyParty',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.error('File server health check error', { error: error.message });
    res.status(503).json({ status: 'unhealthy', error: error.message });
  }
});

// Error handling middleware
app.use((err, req, res, next) => {
  logger.error('Unhandled error', { 
    error: err.message, 
    stack: err.stack,
    url: req.url,
    method: req.method
  });
  res.status(500).json({ error: 'Something went wrong!' });
});

// Helper function to check if file is a video
function isVideoFile(filename) {
  const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v', '.flv', '.wmv'];
  return videoExtensions.some(ext => filename.toLowerCase().endsWith(ext));
}

app.listen(PORT, () => {
  logger.info('Combat Mirror API server started', {
    port: PORT,
    corsOrigin: process.env.FRONTEND_URL || 'http://localhost:5176',
    environment: process.env.NODE_ENV || 'development',
    copyPartyIntegration: 'enabled'
  });
});
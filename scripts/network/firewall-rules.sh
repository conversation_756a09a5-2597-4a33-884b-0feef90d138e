#!/bin/bash
# Combat Mirror System - Firewall Rules Configuration
# macOS pfctl firewall setup for network segmentation
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONFIG_FILE="$PROJECT_ROOT/config/network-config.json"
LOG_FILE="$PROJECT_ROOT/logs/firewall-setup.log"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    log "ERROR: $1"
    exit 1
}

success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
    log "SUCCESS: $1"
}

info() {
    echo -e "${YELLOW}INFO: $1${NC}"
    log "INFO: $1"
}

# Create pf configuration
create_pf_config() {
    info "Creating pf firewall configuration..."
    
    local pf_config="/etc/pf.combat-mirror.conf"
    
    cat << EOF | sudo tee "$pf_config"
# Combat Mirror System - Network Segmentation Firewall Rules
# Generated on $(date)

# Macros - define network interfaces and subnets
wifi_if = "en0"
usb_if = "en1"
management_net = "***********/24"
combat_ports = "{8080 3478:3481 9090 3000:3005}"

# Tables for device management
table <trusted_devices> persist file "/etc/pf.trusted_devices"
table <blocked_devices> persist

# Default policies
set block-policy drop
set fingerprints "/etc/pf.os"
set skip on lo0

# Block all by default
block all

# Allow loopback
pass quick on lo0 all

# Allow DHCP traffic
pass quick proto udp from any to any port {67,68}
pass quick proto udp from any port 67 to any port 68

# Allow DNS queries
pass quick proto udp from $management_net to any port 53
pass quick proto tcp from $management_net to any port 53

# Allow Combat Mirror services
pass in quick proto tcp from $management_net to any port $combat_ports
pass out quick proto tcp from any to $management_net port $combat_ports

# Allow established connections
pass in quick on $wifi_if proto tcp from any to any flags S/SA keep state
pass out quick on $wifi_if proto tcp from any to any flags S/SA keep state

# Block direct device-to-device communication
block in quick on $wifi_if from $management_net to $management_net
block out quick on $wifi_if from $management_net to $management_net

# Allow internet access through USB interface
pass out quick on $usb_if from $management_net to any
pass in quick on $usb_if from any to $management_net

# Allow ICMP (ping)
pass quick proto icmp all

# Log blocked connections
block log all
EOF

    # Create trusted devices file
    cat << EOF | sudo tee "/etc/pf.trusted_devices"
# Trusted devices for Combat Mirror System
***********0  # iPad Pro
***********1  # iPhone 15 Pro Max
***********   # MacBook Air Hub
EOF

    success "pf configuration created at $pf_config"
}

# Load firewall rules
load_firewall() {
    info "Loading firewall rules..."
    
    # Backup current rules
    sudo cp /etc/pf.conf /etc/pf.conf.backup 2>/dev/null || true
    
    # Load new rules
    sudo pfctl -f /etc/pf.combat-mirror.conf
    
    # Enable pf
    sudo pfctl -e
    
    # Show current rules
    sudo pfctl -sr
    
    success "Firewall rules loaded and active"
}

# Show firewall status
show_status() {
    info "Current firewall status:"
    echo "================================"
    sudo pfctl -sr
    echo "================================"
    sudo pfctl -sa
}

# Reset firewall to defaults
reset_firewall() {
    info "Resetting firewall to defaults..."
    
    # Disable pf
    sudo pfctl -d
    
    # Restore original configuration
    if [[ -f "/etc/pf.conf.backup" ]]; then
        sudo cp /etc/pf.conf.backup /etc/pf.conf
        sudo pfctl -f /etc/pf.conf
    fi
    
    # Remove custom files
    sudo rm -f /etc/pf.combat-mirror.conf
    sudo rm -f /etc/pf.trusted_devices
    
    success "Firewall reset to defaults"
}

# Test firewall rules
test_firewall() {
    info "Testing firewall rules..."
    
    # Test connectivity
    local test_ip="***********0"
    
    # Test ping
    if ping -c 1 "$test_ip" &>/dev/null; then
        success "ICMP (ping) allowed"
    else
        warning "ICMP (ping) blocked"
    fi
    
    # Test port connectivity
    for port in 8080 3478 9090; do
        if nc -z *********** "$port" 2>/dev/null; then
            success "Port $port accessible"
        else
            warning "Port $port not accessible"
        fi
    done
    
    # Check for blocked inter-device communication
    info "Checking inter-device communication blocking..."
    sudo pfctl -sr | grep -q "block.*192.168.2" && success "Inter-device communication blocked" || warning "Inter-device communication not blocked"
}

# Main function
main() {
    log "Starting firewall configuration"
    
    case "${1:-setup}" in
        "setup")
            create_pf_config
            load_firewall
            test_firewall
            ;;
        "status")
            show_status
            ;;
        "reset")
            reset_firewall
            ;;
        "test")
            test_firewall
            ;;
        *)
            echo "Usage: $0 {setup|status|reset|test}"
            exit 1
            ;;
    esac
    
    success "Firewall configuration completed"
}

# Run main function
main "$@"
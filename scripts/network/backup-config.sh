#!/bin/bash
# Combat Mirror System - Network Configuration Backup Script
# Creates comprehensive backups of network configuration
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
BACKUP_DIR="$PROJECT_ROOT/backups/network-$(date +%Y%m%d-%H%M%S)"
LOG_FILE="$PROJECT_ROOT/logs/backup.log"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

success() {
    echo -e "${GREEN}✓ $1${NC}"
}

info() {
    echo -e "${YELLOW}ℹ $1${NC}"
}

# Create backup directory
mkdir -p "$BACKUP_DIR"
mkdir -p "$(dirname "$LOG_FILE")"

# Backup function
backup_config() {
    local component="$1"
    local source="$2"
    local dest="$3"
    
    if [[ -e "$source" ]]; then
        cp -r "$source" "$dest"
        success "Backed up $component"
    else
        info "Skipping $component (not found)"
    fi
}

# Main backup function
main() {
    echo "Creating network configuration backup..."
    echo "Backup location: $BACKUP_DIR"
    
    # Create backup structure
    mkdir -p "$BACKUP_DIR/config"
    mkdir -p "$BACKUP_DIR/scripts"
    mkdir -p "$BACKUP_DIR/system"
    
    # Backup configuration files
    backup_config "Network Config" "$PROJECT_ROOT/config/network-config.json" "$BACKUP_DIR/config/"
    backup_config "Device Config" "$PROJECT_ROOT/config/devices.json" "$BACKUP_DIR/config/"
    
    # Backup scripts
    backup_config "Network Scripts" "$PROJECT_ROOT/scripts/network" "$BACKUP_DIR/scripts/"
    
    # Backup system configuration
    networksetup -listallnetworkservices > "$BACKUP_DIR/system/network-services.txt"
    networksetup -getinfo "Wi-Fi" > "$BACKUP_DIR/system/wifi-info.txt" 2>/dev/null || true
    ifconfig > "$BACKUP_DIR/system/ifconfig.txt"
    netstat -rn > "$BACKUP_DIR/system/routing-table.txt"
    arp -a > "$BACKUP_DIR/system/arp-table.txt"
    
    # Backup firewall rules
    sudo pfctl -sr > "$BACKUP_DIR/system/pf-rules.txt" 2>/dev/null || true
    sudo pfctl -sn > "$BACKUP_DIR/system/pf-nat.txt" 2>/dev/null || true
    
    # Backup DHCP configuration
    if [[ -f "/usr/local/etc/dnsmasq.conf" ]]; then
        cp "/usr/local/etc/dnsmasq.conf" "$BACKUP_DIR/system/dnsmasq.conf"
    fi
    
    if [[ -f "/var/lib/misc/dnsmasq.leases" ]]; then
        cp "/var/lib/misc/dnsmasq.leases" "$BACKUP_DIR/system/dnsmasq.leases"
    fi
    
    # Create backup manifest
    cat << EOF > "$BACKUP_DIR/backup-manifest.txt"
Combat Mirror Network Backup
Created: $(date)
Hostname: $(hostname)
macOS Version: $(sw_vers -productVersion)

Contents:
- Configuration files
- Network scripts
- System configuration
- Firewall rules
- DHCP/DNS configuration
- Network state

To restore from backup:
1. Review configuration files
2. Run: npm run network:setup
3. Restore custom configurations as needed
EOF
    
    success "Backup completed successfully!"
    echo "Location: $BACKUP_DIR"
    echo ""
    echo "To restore from backup:"
    echo "1. Review files in: $BACKUP_DIR"
    echo "2. Run: npm run network:setup"
    echo "3. Copy custom configurations as needed"
}

# Run backup
main "$@"
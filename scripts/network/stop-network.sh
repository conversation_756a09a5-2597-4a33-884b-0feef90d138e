#!/bin/bash
# Combat Mirror System - Network Stop Script
# Stops all network services gracefully
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/network-stop.log"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✓ $1${NC}"
    log "SUCCESS: $1"
}

warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    log "WARNING: $1"
}

error() {
    echo -e "${RED}✗ $1${NC}"
    log "ERROR: $1"
}

# Stop network services
main() {
    log "Stopping Combat Mirror Network Services..."
    
    # Stop dnsmasq
    if command -v dnsmasq &> /dev/null; then
        info "Stopping DNS/DHCP services..."
        sudo brew services stop dnsmasq 2>/dev/null || warning "dnsmasq not running"
        success "DNS/DHCP services stopped"
    fi
    
    # Disable firewall rules
    info "Disabling firewall rules..."
    sudo pfctl -d 2>/dev/null || warning "pfctl not active"
    success "Firewall rules disabled"
    
    # Stop monitoring
    info "Stopping network monitoring..."
    pkill -f "monitor-network.sh" 2>/dev/null || warning "No monitoring processes found"
    success "Network monitoring stopped"
    
    # Reset network interfaces
    info "Resetting network interfaces..."
    sudo ifconfig en0 down 2>/dev/null || true
    sudo ifconfig en0 up 2>/dev/null || true
    
    # Display final status
    echo ""
    success "Network services stopped successfully!"
    echo ""
    info "To restart network services, run: npm run network:start"
}

# Create logs directory
mkdir -p "$PROJECT_ROOT/logs"

# Run main function
main "$@"
#!/bin/bash
# Combat Mirror System - Network Start Script
# Starts all network services for Phase 1
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/network-start.log"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✓ $1${NC}"
    log "SUCCESS: $1"
}

info() {
    echo -e "${YELLOW}ℹ $1${NC}"
    log "INFO: $1"
}

# Create logs directory
mkdir -p "$PROJECT_ROOT/logs"

# Start network services
main() {
    log "Starting Combat Mirror Network Services..."
    
    # Check if running on macOS
    if [[ "$OSTYPE" != "darwin"* ]]; then
        echo "Error: This script must be run on macOS"
        exit 1
    fi
    
    # Start dnsmasq if installed
    if command -v dnsmasq &> /dev/null; then
        info "Starting DNS/DHCP services..."
        sudo brew services start dnsmasq
        success "DNS/DHCP services started"
    fi
    
    # Load firewall rules
    info "Loading firewall rules..."
    sudo "$SCRIPT_DIR/firewall-rules.sh" setup
    
    # Enable internet sharing (requires manual setup)
    info "Please ensure Internet Sharing is enabled in System Preferences > Sharing"
    info "Share from: USB Ethernet (Android tethering)"
    info "To computers using: Wi-Fi"
    
    # Start network monitoring
    info "Starting network monitoring..."
    nohup "$SCRIPT_DIR/monitor-network.sh" > "$PROJECT_ROOT/logs/network-monitor.log" 2>&1 &
    
    # Display network status
    echo ""
    success "Network services started successfully!"
    echo ""
    info "Network Status:"
    echo "================"
    ifconfig | grep -A 3 "flags=" | head -20
    echo ""
    info "Connected Devices:"
    echo "================="
    arp -a | grep -E "192\.168\.2\." || echo "No devices connected yet"
    
    echo ""
    info "Combat Mirror services available at:"
    echo "- Main Interface: http://***********:8080"
    echo "- Monitoring: http://***********:9090"
    echo ""
    info "To stop network services, run: npm run network:stop"
}

# Run main function
main "$@"
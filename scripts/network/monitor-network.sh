#!/bin/bash
# Combat Mirror System - Network Monitoring Script
# Real-time network monitoring and health checks
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/network-monitor.log"
CONFIG_FILE="$PROJECT_ROOT/config/network-config.json"

# Colors
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Logging
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

print_header() {
    echo -e "${BLUE}=== $1 ===${NC}"
}

print_success() {
    echo -e "${GREEN}✓ $1${NC}"
}

print_error() {
    echo -e "${RED}✗ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
}

# Check network interfaces
check_interfaces() {
    print_header "Network Interfaces"
    
    local interfaces=("en0" "en1" "bridge0")
    for interface in "${interfaces[@]}"; do
        if ifconfig "$interface" &>/dev/null; then
            local status=$(ifconfig "$interface" | grep "status:" | awk '{print $2}')
            local ip=$(ifconfig "$interface" | grep "inet " | awk '{print $2}' || echo "No IP")
            echo "Interface $interface: $status (IP: $ip)"
        fi
    done
}

# Check connected devices
check_devices() {
    print_header "Connected Devices"
    
    local subnet="192.168.2"
    local found_devices=0
    
    for ip in $(seq 1 254); do
        local host="$subnet.$ip"
        if ping -c 1 -W 1 "$host" &>/dev/null; then
            local mac=$(arp -a | grep "$host" | awk '{print $4}' || echo "Unknown")
            local hostname=$(arp -a | grep "$host" | awk '{print $1}' | sed 's/\.$//' || echo "Unknown")
            echo "Device: $host ($mac) - $hostname"
            ((found_devices++))
        fi
    done
    
    if [[ $found_devices -eq 0 ]]; then
        print_warning "No devices found on network"
    else
        print_success "Found $found_devices connected devices"
    fi
}

# Check service ports
check_services() {
    print_header "Service Ports Status"
    
    local services=(8080 3478 3479 3480 3481 9090)
    
    for port in "${services[@]}"; do
        if nc -z *********** "$port" 2>/dev/null; then
            print_success "Port $port: OPEN"
        else
            print_error "Port $port: CLOSED"
        fi
    done
}

# Check bandwidth usage
check_bandwidth() {
    print_header "Bandwidth Usage"
    
    # Check if nload is available
    if command -v nload &>/dev/null; then
        echo "Interface traffic:"
        netstat -ib | awk 'NR==1 || $1 ~ /^[a-z]/ {print $1, $7, $10}' | column -t
    else
        print_warning "nload not available - install with: brew install nload"
    fi
}

# Check DHCP leases
check_dhcp() {
    print_header "DHCP Leases"
    
    if [[ -f "/var/lib/misc/dnsmasq.leases" ]]; then
        cat "/var/lib/misc/dnsmasq.leases" | while read -r line; do
            local ip=$(echo "$line" | awk '{print $3}')
            local mac=$(echo "$line" | awk '{print $2}')
            local hostname=$(echo "$line" | awk '{print $4}')
            echo "Lease: $ip ($mac) - $hostname"
        done
    else
        print_warning "No DHCP leases file found"
    fi
}

# Check firewall status
check_firewall() {
    print_header "Firewall Status"
    
    if sudo pfctl -s info | grep -q "Status: Enabled"; then
        print_success "Firewall: ENABLED"
        local rules_count=$(sudo pfctl -sr | wc -l)
        echo "Active rules: $rules_count"
    else
        print_error "Firewall: DISABLED"
    fi
}

# Check internet connectivity
check_internet() {
    print_header "Internet Connectivity"
    
    # Check if we can reach external servers
    local test_hosts=("*******" "*******" "google.com")
    
    for host in "${test_hosts[@]}"; do
        if ping -c 1 -W 3 "$host" &>/dev/null; then
            print_success "Internet access via $host: OK"
            return 0
        fi
    done
    
    print_error "No internet connectivity detected"
}

# Real-time monitoring
realtime_monitor() {
    print_header "Real-time Network Monitoring"
    echo "Press Ctrl+C to stop monitoring"
    echo ""
    
    while true; do
        clear
        echo "=== Combat Mirror Network Monitor ==="
        echo "Last updated: $(date)"
        echo ""
        
        check_interfaces
        echo ""
        check_devices
        echo ""
        check_services
        echo ""
        check_firewall
        echo ""
        check_internet
        
        sleep 5
    done
}

# Generate report
generate_report() {
    local report_file="$PROJECT_ROOT/logs/network-report-$(date +%Y%m%d-%H%M%S).txt"
    
    {
        echo "Combat Mirror Network Report"
        echo "Generated: $(date)"
        echo "================================"
        echo ""
        
        check_interfaces
        echo ""
        check_devices
        echo ""
        check_services
        echo ""
        check_bandwidth
        echo ""
        check_dhcp
        echo ""
        check_firewall
        echo ""
        check_internet
        
    } > "$report_file"
    
    print_success "Report generated: $report_file"
}

# Main function
main() {
    case "${1:-status}" in
        "status")
            check_interfaces
            echo ""
            check_devices
            echo ""
            check_services
            echo ""
            check_firewall
            echo ""
            check_internet
            ;;
        "realtime")
            realtime_monitor
            ;;
        "report")
            generate_report
            ;;
        *)
            echo "Usage: $0 {status|realtime|report}"
            exit 1
            ;;
    esac
}

# Create logs directory
mkdir -p "$PROJECT_ROOT/logs"

# Run main function
main "$@"
#!/bin/bash
# Combat Mirror System - Phase 1 Network Infrastructure Setup
# MacBook Air as Central Network Hub
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
CONFIG_FILE="$PROJECT_ROOT/config/network-config.json"
LOG_FILE="$PROJECT_ROOT/logs/network-setup.log"
BACKUP_DIR="$PROJECT_ROOT/backups/network-config-$(date +%Y%m%d-%H%M%S)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Error handling
error_exit() {
    echo -e "${RED}ERROR: $1${NC}" >&2
    log "ERROR: $1"
    exit 1
}

# Success message
success() {
    echo -e "${GREEN}SUCCESS: $1${NC}"
    log "SUCCESS: $1"
}

# Warning message
warning() {
    echo -e "${YELLOW}WARNING: $1${NC}"
    log "WARNING: $1"
}

# Info message
info() {
    echo -e "${BLUE}INFO: $1${NC}"
    log "INFO: $1"
}

# Check if running on macOS
check_macos() {
    if [[ "$OSTYPE" != "darwin"* ]]; then
        error_exit "This script must be run on macOS"
    fi
    info "Running on macOS $(sw_vers -productVersion)"
}

# Check for required tools
check_requirements() {
    info "Checking system requirements..."
    
    # Check for required commands
    local required_commands=("ifconfig" "pfctl" "dnsmasq" "networksetup" "airport")
    
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            warning "Command $cmd not found, attempting to install..."
            case $cmd in
                "dnsmasq")
                    if ! command -v brew &> /dev/null; then
                        error_exit "Homebrew not found. Please install Homebrew first: https://brew.sh"
                    fi
                    brew install dnsmasq
                    ;;
                "airport")
                    # airport is part of the system, create symlink if needed
                    sudo ln -sf /System/Library/PrivateFrameworks/Apple80211.framework/Versions/Current/Resources/airport /usr/local/bin/airport
                    ;;
            esac
        fi
    done
    
    success "All requirements satisfied"
}

# Create backup of current network configuration
create_backup() {
    info "Creating backup of current network configuration..."
    mkdir -p "$BACKUP_DIR"
    
    # Backup current network settings
    networksetup -listallnetworkservices > "$BACKUP_DIR/network-services.txt"
    networksetup -getinfo "Wi-Fi" > "$BACKUP_DIR/wifi-info.txt" 2>/dev/null || true
    ifconfig > "$BACKUP_DIR/ifconfig.txt"
    sudo pfctl -sr > "$BACKUP_DIR/pf-rules.txt" 2>/dev/null || true
    
    success "Backup created at $BACKUP_DIR"
}

# Configure USB-C tethering for Android device
setup_usb_tethering() {
    info "Setting up USB-C tethering for Android device..."
    
    # Check if Android device is connected via USB
    local android_device=$(system_profiler SPUSBDataType | grep -i "android" -A 5 -B 5 || true)
    if [[ -z "$android_device" ]]; then
        warning "No Android device detected via USB. Please connect your Android device and enable USB tethering."
        read -p "Press Enter when Android device is connected and tethering is enabled..."
    fi
    
    # Configure network interface for USB tethering
    local usb_interface=$(networksetup -listallhardwareports | grep -A 3 "USB" | grep "Device" | awk '{print $2}' | head -1)
    if [[ -n "$usb_interface" ]]; then
        info "Configuring USB interface: $usb_interface"
        sudo ifconfig "$usb_interface" up
        success "USB tethering interface configured"
    else
        warning "Could not automatically detect USB tethering interface"
    fi
}

# Create WiFi hotspot
setup_wifi_hotspot() {
    info "Setting up WiFi hotspot for iOS devices..."
    
    # Check if WiFi interface exists
    local wifi_interface=$(networksetup -listallhardwareports | grep -A 3 "Wi-Fi" | grep "Device" | awk '{print $2}')
    if [[ -z "$wifi_interface" ]]; then
        error_exit "WiFi interface not found"
    fi
    
    info "WiFi interface detected: $wifi_interface"
    
    # Create WiFi hotspot using macOS Internet Sharing
    # Note: This requires manual configuration in System Preferences
    
    info "Please configure WiFi hotspot manually:"
    info "1. Open System Preferences > Sharing"
    info "2. Select 'Internet Sharing'"
    info "3. Share connection from: USB Ethernet (Android tethering)"
    info "4. To computers using: Wi-Fi"
    info "5. Click Wi-Fi Options and configure:"
    info "   - Network Name: CombatMirror-Hub"
    info "   - Channel: 6"
    info "   - Security: WPA2 Personal"
    info "   - Password: SecureMirror2024!"
    
    read -p "Press Enter after configuring WiFi hotspot..."
    
    success "WiFi hotspot configured"
}

# Configure DHCP and DNS services
setup_dhcp_dns() {
    info "Setting up DHCP and DNS services..."
    
    # Install and configure dnsmasq for DHCP/DNS
    if ! command -v dnsmasq &> /dev/null; then
        info "Installing dnsmasq..."
        brew install dnsmasq
    fi
    
    # Configure dnsmasq
    local dnsmasq_conf="/usr/local/etc/dnsmasq.conf"
    sudo cp "$dnsmasq_conf" "$dnsmasq_conf.backup" 2>/dev/null || true
    
    cat << EOF | sudo tee "$dnsmasq_conf"
# Combat Mirror System - DHCP/DNS Configuration
# Listen on WiFi interface
interface=en0
bind-interfaces

# DHCP range for iOS devices
dhcp-range=*************,*************,*************,3600s

# Reserved IPs for known devices
dhcp-host=ipad-pro-001,************
dhcp-host=iphone-15-pro-max-001,************

# DNS configuration
server=*******
server=*******
local=/combatmirror.local/

# Logging
log-queries
log-dhcp
EOF
    
    # Start dnsmasq
    sudo brew services start dnsmasq
    
    success "DHCP and DNS services configured"
}

# Configure firewall rules
setup_firewall() {
    info "Configuring firewall rules..."
    
    # Create pf configuration file
    local pf_conf="/etc/pf.conf"
    local pf_custom="/etc/pf.combat-mirror.conf"
    
    # Backup original pf.conf
    sudo cp "$pf_conf" "$pf_conf.backup" 2>/dev/null || true
    
    # Create custom pf rules
    cat << EOF | sudo tee "$pf_custom"
# Combat Mirror System - Firewall Rules
# Block direct device-to-device communication
# Allow hub-controlled traffic only

# Macros
wifi_net = "***********/24"
combat_ports = "{8080 3478:3481 9090}"

# Block inter-device communication
block in quick from $wifi_net to $wifi_net

# Allow established connections
pass in quick on lo0 all
pass out quick on lo0 all

# Allow DHCP
pass in quick proto udp from any to any port 67:68
pass out quick proto udp from any to any port 67:68

# Allow DNS
pass in quick proto udp from $wifi_net to any port 53
pass out quick proto udp from any to any port 53

# Allow Combat Mirror services
pass in quick proto tcp from $wifi_net to any port $combat_ports
pass out quick proto tcp from any to any port $combat_ports

# Allow internet access
pass out quick on en0 from $wifi_net to any
pass in quick on en0 from any to $wifi_net
EOF
    
    # Load pf rules
    sudo pfctl -f "$pf_custom"
    sudo pfctl -e
    
    success "Firewall rules configured"
}

# Configure network monitoring
setup_monitoring() {
    info "Setting up network monitoring..."
    
    # Install monitoring tools
    if ! command -v nload &> /dev/null; then
        brew install nload
    fi
    
    if ! command -v iftop &> /dev/null; then
        brew install iftop
    fi
    
    if ! command -v nethogs &> /dev/null; then
        brew install nethogs
    fi
    
    # Create monitoring script
    cat << EOF > "$PROJECT_ROOT/scripts/network/monitor-network.sh"
#!/bin/bash
# Network monitoring script for Combat Mirror System

echo "=== Network Interface Status ==="
ifconfig | grep -A 5 "flags=" | head -20

echo -e "\n=== Active Connections ==="
netstat -an | grep ESTABLISHED | head -10

echo -e "\n=== Bandwidth Usage ==="
nload -m -t 1000 2>/dev/null || echo "nload not available"

echo -e "\n=== DHCP Leases ==="
cat /var/lib/misc/dnsmasq.leases 2>/dev/null || echo "No DHCP leases found"
EOF
    
    chmod +x "$PROJECT_ROOT/scripts/network/monitor-network.sh"
    
    success "Network monitoring tools installed"
}

# Update package.json with network commands
update_package_json() {
    info "Updating package.json with network commands..."
    
    # Add network commands to package.json
    node -e "
    const fs = require('fs');
    const pkg = JSON.parse(fs.readFileSync('package.json', 'utf8'));
    
    pkg.scripts = {
        ...pkg.scripts,
        'network:setup': './scripts/network/setup-network-hub.sh',
        'network:start': './scripts/network/start-network.sh',
        'network:stop': './scripts/network/stop-network.sh',
        'network:status': './scripts/network/monitor-network.sh',
        'network:reset': './scripts/network/reset-network.sh',
        'network:backup': './scripts/network/backup-config.sh'
    };
    
    fs.writeFileSync('package.json', JSON.stringify(pkg, null, 2));
    "
    
    success "Package.json updated with network commands"
}

# Main setup function
main() {
    log "Starting Combat Mirror Phase 1 Network Setup"
    
    check_macos
    check_requirements
    create_backup
    
    setup_usb_tethering
    setup_wifi_hotspot
    setup_dhcp_dns
    setup_firewall
    setup_monitoring
    update_package_json
    
    success "Phase 1 Network Infrastructure setup completed!"
    info "Next steps:"
    info "1. Connect Android device via USB-C and enable USB tethering"
    info "2. Connect iPad Pro and iPhone to WiFi network 'CombatMirror-Hub'"
    info "3. Run 'npm run network:status' to verify network health"
    info "4. Access Combat Mirror services at http://***********:8080"
}

# Run main function
main "$@"
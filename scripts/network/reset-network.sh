#!/bin/bash
# Combat Mirror System - Network Reset Script
# Resets network configuration to defaults
# Author: DevOps Team
# Version: 1.0.0

set -euo pipefail

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(cd "$SCRIPT_DIR/../.." && pwd)"
LOG_FILE="$PROJECT_ROOT/logs/network-reset.log"

# Colors
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m'

log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}✓ $1${NC}"
    log "SUCCESS: $1"
}

warning() {
    echo -e "${YELLOW}⚠ $1${NC}"
    log "WARNING: $1"
}

error() {
    echo -e "${RED}✗ $1${NC}"
    log "ERROR: $1"
}

# Reset network configuration
main() {
    log "Resetting Combat Mirror Network Configuration..."
    
    # Stop all services first
    info "Stopping network services..."
    "$SCRIPT_DIR/stop-network.sh"
    
    # Reset firewall
    info "Resetting firewall to defaults..."
    sudo "$SCRIPT_DIR/firewall-rules.sh" reset
    
    # Reset network interfaces
    info "Resetting network interfaces..."
    sudo ifconfig en0 inet *********** netmask *************
    sudo ifconfig en0 up
    
    # Reset DNS configuration
    info "Resetting DNS configuration..."
    sudo rm -f /usr/local/etc/dnsmasq.conf
    sudo rm -f /etc/resolver/combatmirror.local
    
    # Clear DHCP leases
    info "Clearing DHCP leases..."
    sudo rm -f /var/lib/misc/dnsmasq.leases
    
    # Reset network preferences
    info "Resetting network preferences..."
    sudo networksetup -setdhcp "Wi-Fi"
    
    # Display reset confirmation
    echo ""
    success "Network configuration reset to defaults!"
    echo ""
    info "To reconfigure the network, run: npm run network:setup"
}

# Create logs directory
mkdir -p "$PROJECT_ROOT/logs"

# Run main function
main "$@"
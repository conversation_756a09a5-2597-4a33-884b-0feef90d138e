#!/usr/bin/env node

/**
 * Device Detection Script for Combat Mirror System
 * Detects connected iPad Pro, iPhone 15 Pro Max, and MacBook Pro devices
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

class DeviceDetector {
  constructor() {
    this.devices = [];
    this.supportedDevices = [
      'iPad Pro',
      'iPhone 15 Pro Max',
      'MacBook Pro'
    ];
  }

  async detectDevices() {
    console.log('🔍 Detecting connected devices...');
    
    try {
      // Check for iOS devices
      const iosDevices = this.detectIOSDevices();
      
      // Check for macOS devices
      const macDevices = this.detectMacDevices();
      
      this.devices = [...iosDevices, ...macDevices];
      
      console.log(`✅ Found ${this.devices.length} devices:`);
      this.devices.forEach((device, index) => {
        console.log(`  ${index + 1}. ${device.name} (${device.udid})`);
      });
      
      return this.devices;
      
    } catch (error) {
      console.error('❌ Error detecting devices:', error.message);
      return [];
    }
  }

  detectIOSDevices() {
    const devices = [];
    
    try {
      // Check if ios-deploy is available
      execSync('which ios-deploy', { stdio: 'pipe' });
      
      // Get list of connected iOS devices
      const output = execSync('ios-deploy -c', { encoding: 'utf8' });
      const lines = output.split('\n');
      
      lines.forEach(line => {
        if (line.includes('Found')) {
          const match = line.match(/Found (.*?) \((.*?)\)/);
          if (match) {
            const name = match[1].trim();
            const udid = match[2].trim();
            
            if (this.isSupportedDevice(name)) {
              devices.push({
                name,
                udid,
                type: this.getDeviceType(name),
                platform: 'ios',
                connected: true
              });
            }
          }
        }
      });
      
    } catch (error) {
      console.warn('⚠️  ios-deploy not found. Install with: npm install -g ios-deploy');
    }
    
    return devices;
  }

  detectMacDevices() {
    const devices = [];
    
    try {
      // Check for MacBook Pro via system_profiler
      const output = execSync('system_profiler SPUSBDataType', { encoding: 'utf8' });
      
      if (output.includes('MacBook Pro')) {
        devices.push({
          name: 'MacBook Pro',
          udid: 'macbook-pro-local',
          type: 'macbook-pro',
          platform: 'macos',
          connected: true
        });
      }
      
    } catch (error) {
      console.warn('⚠️  Could not detect Mac devices:', error.message);
    }
    
    return devices;
  }

  isSupportedDevice(name) {
    return this.supportedDevices.some(device => 
      name.toLowerCase().includes(device.toLowerCase())
    );
  }

  getDeviceType(name) {
    if (name.toLowerCase().includes('ipad pro')) return 'ipad-pro';
    if (name.toLowerCase().includes('iphone 15 pro max')) return 'iphone-15-pro-max';
    if (name.toLowerCase().includes('macbook pro')) return 'macbook-pro';
    return 'unknown';
  }

  saveDeviceList() {
    const deviceFile = path.join(__dirname, '../../config/devices.json');
    const data = {
      timestamp: new Date().toISOString(),
      devices: this.devices
    };
    
    fs.writeFileSync(deviceFile, JSON.stringify(data, null, 2));
    console.log(`📁 Device list saved to ${deviceFile}`);
  }

  async run() {
    await this.detectDevices();
    this.saveDeviceList();
    
    if (this.devices.length === 0) {
      console.log('💡 No devices found. Make sure:');
      console.log('   1. Devices are connected via USB');
      console.log('   2. You have trusted this computer on iOS devices');
      console.log('   3. ios-deploy is installed: npm install -g ios-deploy');
    }
  }
}

// CLI Interface
if (require.main === module) {
  const detector = new DeviceDetector();
  detector.run().catch(console.error);
}

module.exports = DeviceDetector;

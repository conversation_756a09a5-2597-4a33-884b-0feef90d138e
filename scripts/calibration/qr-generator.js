#!/usr/bin/env node

/**
 * QR Code Generator for Combat Mirror System Calibration
 * Generates QR codes for device calibration and position matching
 */

const QRCode = require('qrcode');
const fs = require('fs');
const path = require('path');

class QRCodeGenerator {
  constructor() {
    this.outputDir = path.join(__dirname, '../../output/qr-codes');
    this.ensureOutputDir();
  }

  ensureOutputDir() {
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }
  }

  async generateDeviceQR(device, position) {
    const qrData = {
      deviceId: device.udid,
      deviceType: device.type,
      position: position,
      calibration: {
        timestamp: Date.now(),
        version: '1.0.0'
      },
      network: {
        host: this.getLocalIP(),
        port: 5173,
        protocol: 'https'
      }
    };

    const qrString = JSON.stringify(qrData);
    const filename = `${device.type}-${position}-${Date.now()}.png`;
    const filepath = path.join(this.outputDir, filename);

    try {
      await QRCode.toFile(filepath, qrString, {
        type: 'png',
        quality: 0.92,
        errorCorrectionLevel: 'M',
        width: 300,
        margin: 2,
        color: {
          dark: '#000000',
          light: '#FFFFFF'
        }
      });

      console.log(`✅ Generated QR code: ${filename}`);
      return {
        filename,
        filepath,
        data: qrData,
        qrString
      };
    } catch (error) {
      console.error(`❌ Error generating QR code:`, error);
      throw error;
    }
  }

  async generateCalibrationSet(devices) {
    const positions = ['FRONT', 'LEFT', 'RIGHT', 'BACK'];
    const results = [];

    console.log(`🎯 Generating calibration QR codes for ${devices.length} devices...`);

    for (const device of devices) {
      for (const position of positions) {
        const result = await this.generateDeviceQR(device, position);
        results.push(result);
      }
    }

    // Generate summary file
    await this.generateSummaryFile(results);
    
    console.log(`📦 Generated ${results.length} QR codes total`);
    return results;
  }

  async generateSummaryFile(results) {
    const summary = {
      timestamp: new Date().toISOString(),
      totalQRCodes: results.length,
      devices: {},
      positions: {}
    };

    // Group by device
    results.forEach(result => {
      const deviceType = result.data.deviceType;
      if (!summary.devices[deviceType]) {
        summary.devices[deviceType] = [];
      }
      summary.devices[deviceType].push({
        position: result.data.position,
        filename: result.filename
      });
    });

    // Group by position
    results.forEach(result => {
      const position = result.data.position;
      if (!summary.positions[position]) {
        summary.positions[position] = [];
      }
      summary.positions[position].push({
        deviceType: result.data.deviceType,
        filename: result.filename
      });
    });

    const summaryPath = path.join(this.outputDir, 'summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    console.log(`📋 Summary saved to ${summaryPath}`);
  }

  getLocalIP() {
    const os = require('os');
    const interfaces = os.networkInterfaces();
    
    for (const name of Object.keys(interfaces)) {
      for (const iface of interfaces[name]) {
        if (iface.family === 'IPv4' && !iface.internal) {
          return iface.address;
        }
      }
    }
    
    return 'localhost';
  }

  async generateWebInterface() {
    const html = `
<!DOCTYPE html>
<html>
<head>
    <title>Combat Mirror - QR Code Calibration</title>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .device-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        .qr-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }
        .qr-item {
            text-align: center;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
            background: #fafafa;
        }
        .qr-item img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ccc;
            border-radius: 4px;
        }
        .position-label {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }
        .instructions {
            background: #e8f4fd;
            border: 1px solid #b3d9ff;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>Combat Mirror - Device Calibration</h1>
        
        <div class="instructions">
            <h3>📱 How to Use:</h3>
            <ol>
                <li>Connect all devices via USB</li>
                <li>Scan the QR code for each device/position combination</li>
                <li>Position devices according to the calibration protocol</li>
                <li>Verify calibration accuracy</li>
            </ol>
        </div>

        <div id="qr-codes">
            <!-- QR codes will be inserted here -->
        </div>
    </div>

    <script>
        async function loadQRCodeSummary() {
            try {
                const response = await fetch('summary.json');
                const summary = await response.json();
                
                const container = document.getElementById('qr-codes');
                
                Object.entries(summary.devices).forEach(([deviceType, positions]) => {
                    const section = document.createElement('div');
                    section.className = 'device-section';
                    section.innerHTML = \`
                        <h3>\${deviceType.replace('-', ' ').toUpperCase()}</h3>
                        <div class="qr-grid">
                            \${positions.map(pos => \`
                                <div class="qr-item">
                                    <div class="position-label">\${pos.position}</div>
                                    <img src="\${pos.filename}" alt="\${deviceType} \${pos.position}">
                                </div>
                            \`).join('')}
                        </div>
                    \`;
                    container.appendChild(section);
                });
                
            } catch (error) {
                console.error('Error loading QR codes:', error);
                document.getElementById('qr-codes').innerHTML = '<p>Error loading QR codes. Please run the calibration script first.</p>';
            }
        }
        
        loadQRCodeSummary();
    </script>
</body>
</html>`;

    const htmlPath = path.join(this.outputDir, 'index.html');
    fs.writeFileSync(htmlPath, html);
    console.log(`🌐 Web interface saved to ${htmlPath}`);
  }
}

// CLI Interface
async function main() {
  const args = process.argv.slice(2);
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
Usage: node qr-generator.js [options]

Options:
  --devices <file>    Path to devices.json file
  --generate-all      Generate all QR codes
  --web-interface     Generate web interface
  --help              Show this help message

Examples:
  node qr-generator.js --generate-all
  node qr-generator.js --devices ../config/devices.json --web-interface
    `);
    return;
  }

  const generator = new QRCodeGenerator();
  
  let devices = [];
  
  // Load devices from file or use defaults
  const devicesFile = args.find(arg => arg.startsWith('--devices='))?.split('=')[1] || '../../config/devices.json';
  const devicesPath = path.join(__dirname, devicesFile);
  
  if (fs.existsSync(devicesPath)) {
    const data = JSON.parse(fs.readFileSync(devicesPath, 'utf8'));
    devices = data.devices || [];
  } else {
    console.log('⚠️  No devices.json found, using default configuration');
    devices = [
      { type: 'ipad-pro', udid: 'ipad-pro-001' },
      { type: 'iphone-15-pro-max', udid: 'iphone-15-pro-max-001' },
      { type: 'macbook-pro', udid: 'macbook-pro-001' }
    ];
  }

  if (args.includes('--generate-all')) {
    await generator.generateCalibrationSet(devices);
  }

  if (args.includes('--web-interface')) {
    await generator.generateWebInterface();
  }

  console.log('🎉 QR code generation complete!');
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = QRCodeGenerator;

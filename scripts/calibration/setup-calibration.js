#!/usr/bin/env node

/**
 * Complete Calibration Setup Script
 * Runs the full calibration pipeline for iPad Pro, iPhone 15 Pro Max, and MacBook Pro
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const DeviceDetector = require('./device-detector');
const QRCodeGenerator = require('./qr-generator');

class CalibrationSetup {
  constructor() {
    this.configDir = path.join(__dirname, '../../config');
    this.outputDir = path.join(__dirname, '../../output');
    this.calibrationDir = path.join(__dirname, '../../output/qr-codes');
  }

  async run() {
    console.log('🎯 Combat Mirror System - Multi-Device Calibration Setup');
    console.log('='.repeat(60));
    
    try {
      // Step 1: Check prerequisites
      await this.checkPrerequisites();
      
      // Step 2: Detect devices
      const devices = await this.detectDevices();
      
      if (devices.length === 0) {
        console.log('❌ No devices detected. Please connect your devices and try again.');
        process.exit(1);
      }
      
      // Step 3: Generate QR codes
      await this.generateQRCodes(devices);
      
      // Step 4: Create calibration summary
      await this.createCalibrationSummary(devices);
      
      // Step 5: Display next steps
      this.displayNextSteps(devices);
      
    } catch (error) {
      console.error('❌ Setup failed:', error.message);
      process.exit(1);
    }
  }

  async checkPrerequisites() {
    console.log('🔍 Checking prerequisites...');
    
    const checks = [
      { name: 'Node.js', command: 'node --version' },
      { name: 'npm', command: 'npm --version' },
      { name: 'qrcode', command: 'node -e "require(\'qrcode\')"' }
    ];

    for (const check of checks) {
      try {
        const result = execSync(check.command, { encoding: 'utf8', stdio: 'pipe' });
        console.log(`✅ ${check.name}: ${result.trim()}`);
      } catch (error) {
        if (check.name === 'qrcode') {
          console.log('⚠️  Installing qrcode package...');
          execSync('npm install qrcode', { stdio: 'inherit' });
        } else {
          throw new Error(`${check.name} not found: ${error.message}`);
        }
      }
    }

    // Check directories
    if (!fs.existsSync(this.configDir)) {
      fs.mkdirSync(this.configDir, { recursive: true });
    }
    if (!fs.existsSync(this.outputDir)) {
      fs.mkdirSync(this.outputDir, { recursive: true });
    }

    console.log('✅ Prerequisites check complete');
  }

  async detectDevices() {
    console.log('\n📱 Detecting connected devices...');
    
    const detector = new DeviceDetector();
    const devices = await detector.detectDevices();
    
    if (devices.length === 0) {
      console.log('\n💡 Troubleshooting:');
      console.log('1. Connect devices via USB');
      console.log('2. Trust this computer on iOS devices');
      console.log('3. Install ios-deploy: npm install -g ios-deploy');
      console.log('4. Check USB cables and ports');
    }
    
    return devices;
  }

  async generateQRCodes(devices) {
    console.log('\n🎯 Generating calibration QR codes...');
    
    const generator = new QRCodeGenerator();
    await generator.generateCalibrationSet(devices);
    await generator.generateWebInterface();
  }

  async createCalibrationSummary(devices) {
    console.log('\n📋 Creating calibration summary...');
    
    const summary = {
      timestamp: new Date().toISOString(),
      devices: devices.map(device => ({
        id: device.udid,
        name: device.name,
        type: device.type,
        positions: ['FRONT', 'LEFT', 'RIGHT', 'BACK']
      })),
      instructions: {
        usb: "Connect all devices via USB for initial setup",
        qr: "Use QR codes for wireless calibration after initial setup",
        positions: {
          FRONT: "2m in front of user, chest height",
          LEFT: "1.5m left, 1m front, shoulder height",
          RIGHT: "1.5m right, 1m front, shoulder height",
          BACK: "1m behind user, chest height"
        }
      }
    };

    const summaryPath = path.join(this.calibrationDir, 'calibration-summary.json');
    fs.writeFileSync(summaryPath, JSON.stringify(summary, null, 2));
    
    console.log(`✅ Summary saved to ${summaryPath}`);
  }

  displayNextSteps(devices) {
    console.log('\n🎉 Calibration setup complete!');
    console.log('='.repeat(60));
    
    console.log('\n📁 Files generated:');
    console.log(`  • Device list: ${path.join(this.configDir, 'devices.json')}`);
    console.log(`  • QR codes: ${this.calibrationDir}`);
    console.log(`  • Web interface: ${path.join(this.calibrationDir, 'index.html')}`);
    
    console.log('\n🚀 Next steps:');
    console.log('1. Open the web interface:');
    console.log(`   file://${path.join(this.calibrationDir, 'index.html')}`);
    console.log('2. Print or display the QR codes at their designated positions');
    console.log('3. Connect devices via USB for initial calibration');
    console.log('4. Use QR codes for wireless device pairing');
    
    console.log('\n📱 Device URLs:');
    devices.forEach(device => {
      console.log(`  • ${device.name}: https://localhost:5173/camera-stream.html?device=${device.udid}`);
    });
    
    console.log('\n🔧 Calibration commands:');
    console.log('  npm run calibration:detect    # Detect connected devices');
    console.log('  npm run calibration:qr        # Generate QR codes');
    console.log('  npm run calibration:web         # Generate web interface');
    console.log('  npm run calibration:full        # Run complete setup');
  }
}

// CLI Interface
async function main() {
  const setup = new CalibrationSetup();
  await setup.run();
}

if (require.main === module) {
  main().catch(console.error);
}

module.exports = CalibrationSetup;

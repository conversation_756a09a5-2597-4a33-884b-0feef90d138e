# Git and version control
.git
.gitignore
README.md

# Python cache and virtual environments
__pycache__
*.pyc
*.pyo
*.pyd
.Python
env
pip-log.txt
pip-delete-this-directory.txt
.tox
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.log
.git
.mypy_cache
.pytest_cache
.hypothesis

# Development files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode
.idea
*.swp
*.swo
*~

# OS files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
temp/
uploads/
output/
debug_logs/
*.tmp
*.temp

# Documentation
docs/
*.md
!README.md

# Test files
tests/
test_*
*_test.py

# Large files that shouldn't be in container
*.mp4
*.avi
*.mov
*.mkv
*.wav
*.mp3 
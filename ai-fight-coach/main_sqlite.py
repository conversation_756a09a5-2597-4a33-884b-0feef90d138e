"""
AI Fight Coach - SQLite Version
Modified to use SQLite for local development without PostgreSQL
"""

# Suppress TensorFlow/MediaPipe C++ warnings
import os
import json
import logging
import tempfile
import shutil
from datetime import datetime
from typing import Optional, List
from fastapi import FastAPI, File, UploadFile, Form, Request, BackgroundTasks, HTTPException, Depends
from fastapi.responses import HTMLResponse, FileResponse, JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Boolean, Text, ForeignKey, text
from sqlalchemy.orm import sessionmaker, Session, declarative_base, relationship
from pydantic import BaseModel
import uuid
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor
import threading
import time
from contextlib import contextmanager
import hashlib
import mimetypes
import re
from typing import Dict, Any
import traceback

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_logs/app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Ensure debug_logs directory exists
os.makedirs('debug_logs', exist_ok=True)

# Use SQLite for local development
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./ai_fight_coach.db")
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# FastAPI app setup
app = FastAPI(title="AI Fight Coach", version="1.0.0")

# Mount static directories
app.mount("/static", StaticFiles(directory="static"), name="static")
app.mount("/uploads", StaticFiles(directory="uploads"), name="uploads")

# Templates
templates = Jinja2Templates(directory="templates")

# Ensure directories exist
os.makedirs("uploads", exist_ok=True)
os.makedirs("output", exist_ok=True)
os.makedirs("static", exist_ok=True)
os.makedirs("temp", exist_ok=True)

# Database models
class User(Base):
    __tablename__ = "users"
    
    id = Column(Integer, primary_key=True, index=True)
    username = Column(String(50), unique=True, index=True)
    email = Column(String(100), unique=True, index=True)
    hashed_password = Column(String(100))
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)

class VideoAnalysis(Base):
    __tablename__ = "video_analyses"
    
    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(String(36), unique=True, index=True)
    filename = Column(String(255))
    original_path = Column(String(500))
    status = Column(String(20), default="pending")
    progress = Column(Integer, default=0)
    result_video = Column(String(500))
    analysis_text = Column(Text)
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    completed_at = Column(DateTime)
    processing_time = Column(Integer)  # in seconds

# Create tables
Base.metadata.create_all(bind=engine)

# Pydantic models
class VideoUploadResponse(BaseModel):
    job_id: str
    status: str
    message: str

class StatusResponse(BaseModel):
    job_id: str
    status: str
    progress: int
    result_url: Optional[str] = None
    error: Optional[str] = None

# Thread pool for background tasks
executor = ThreadPoolExecutor(max_workers=3)

# Global job tracking
job_status = {}
job_results = {}

# Dependency to get database session
@contextmanager
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Routes
@app.get("/", response_class=HTMLResponse)
async def home(request: Request):
    """Home page with upload form"""
    return templates.TemplateResponse("index.html", {"request": request})

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "timestamp": datetime.utcnow()}

@app.post("/upload", response_model=VideoUploadResponse)
async def upload_video(
    background_tasks: BackgroundTasks,
    video: UploadFile = File(...),
    custom_prompt: Optional[str] = Form(None),
    voice_id: Optional[str] = Form(None)
):
    """Upload video for analysis"""
    try:
        # Validate file type
        if not video.content_type or not video.content_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="File must be a video")
        
        # Generate unique job ID
        job_id = str(uuid.uuid4())
        
        # Save uploaded file
        filename = f"{job_id}_{video.filename}"
        file_path = os.path.join("uploads", filename)
        
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(video.file, buffer)
        
        # Create database record
        with get_db() as db:
            analysis = VideoAnalysis(
                job_id=job_id,
                filename=video.filename,
                original_path=file_path,
                status="pending"
            )
            db.add(analysis)
            db.commit()
        
        # Start background processing
        background_tasks.add_task(
            process_video_async,
            job_id,
            file_path,
            custom_prompt,
            voice_id
        )
        
        return VideoUploadResponse(
            job_id=job_id,
            status="processing",
            message="Video uploaded successfully. Processing started."
        )
        
    except Exception as e:
        logger.error(f"Upload error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/status/{job_id}", response_model=StatusResponse)
async def get_status(job_id: str):
    """Check processing status"""
    try:
        with get_db() as db:
            analysis = db.query(VideoAnalysis).filter(VideoAnalysis.job_id == job_id).first()
            if not analysis:
                raise HTTPException(status_code=404, detail="Job not found")
            
            return StatusResponse(
                job_id=job_id,
                status=analysis.status,
                progress=analysis.progress,
                result_url=f"/static/{os.path.basename(analysis.result_video)}" if analysis.result_video else None,
                error=analysis.error_message
            )
    except Exception as e:
        logger.error(f"Status check error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

@app.get("/voices")
async def list_voices():
    """List available ElevenLabs voices"""
    try:
        from elevenlabs import ElevenLabs
        client = ElevenLabs(api_key=os.getenv("ELEVENLABS_API_KEY"))
        voices = client.voices.get_all()
        return {"voices": [{"voice_id": v.voice_id, "name": v.name} for v in voices]}
    except Exception as e:
        logger.error(f"Voices error: {str(e)}")
        return {"voices": [], "error": str(e)}

@app.get("/jobs")
async def list_jobs():
    """List all jobs"""
    try:
        with get_db() as db:
            analyses = db.query(VideoAnalysis).order_by(VideoAnalysis.created_at.desc()).limit(50).all()
            return {
                "jobs": [
                    {
                        "job_id": a.job_id,
                        "filename": a.filename,
                        "status": a.status,
                        "created_at": a.created_at.isoformat(),
                        "result_url": f"/static/{os.path.basename(a.result_video)}" if a.result_video else None
                    }
                    for a in analyses
                ]
            }
    except Exception as e:
        logger.error(f"Jobs list error: {str(e)}")
        raise HTTPException(status_code=500, detail=str(e))

def process_video_async(job_id: str, file_path: str, custom_prompt: Optional[str] = None, voice_id: Optional[str] = None):
    """Process video in background"""
    try:
        logger.info(f"Starting processing for job {job_id}")
        
        # Update status
        with get_db() as db:
            analysis = db.query(VideoAnalysis).filter(VideoAnalysis.job_id == job_id).first()
            analysis.status = "processing"
            analysis.progress = 10
            db.commit()
        
        # Simulate processing (in real app, this would use Gemini AI)
        time.sleep(2)
        
        # Update progress
        with get_db() as db:
            analysis = db.query(VideoAnalysis).filter(VideoAnalysis.job_id == job_id).first()
            analysis.progress = 50
            db.commit()
        
        # Create a simple result (in real app, this would be AI analysis)
        result_filename = f"result_{job_id}.mp4"
        result_path = os.path.join("static", result_filename)
        
        # Copy original as result for demo
        shutil.copy2(file_path, result_path)
        
        # Update database
        with get_db() as db:
            analysis = db.query(VideoAnalysis).filter(VideoAnalysis.job_id == job_id).first()
            analysis.status = "completed"
            analysis.progress = 100
            analysis.result_video = result_path
            analysis.completed_at = datetime.utcnow()
            db.commit()
        
        logger.info(f"Completed processing for job {job_id}")
        
    except Exception as e:
        logger.error(f"Processing error for job {job_id}: {str(e)}")
        with get_db() as db:
            analysis = db.query(VideoAnalysis).filter(VideoAnalysis.job_id == job_id).first()
            analysis.status = "failed"
            analysis.error_message = str(e)
            db.commit()

# Create templates directory and basic template
os.makedirs("templates", exist_ok=True)

# Create a basic index.html template
index_html = """<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Fight Coach</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .progress { margin: 20px 0; }
        .result { margin: 20px 0; padding: 20px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>🥊 AI Fight Coach</h1>
    <p>Upload your boxing video for AI-powered analysis</p>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="upload-area">
            <input type="file" name="video" accept="video/*" required>
            <p>Select a video file (MP4, AVI, MOV, MKV)</p>
        </div>
        
        <div>
            <label>Custom Prompt (optional):</label>
            <textarea name="custom_prompt" rows="3" placeholder="Enter custom analysis instructions..."></textarea>
        </div>
        
        <button type="submit">Upload & Analyze</button>
    </form>
    
    <div id="progress" class="progress" style="display: none;">
        <h3>Processing...</h3>
        <p id="status">Starting analysis...</p>
        <progress id="progressBar" value="0" max="100"></progress>
    </div>
    
    <div id="result" class="result" style="display: none;">
        <h3>Analysis Complete!</h3>
        <video id="resultVideo" controls style="width: 100%; max-width: 600px;"></video>
        <br>
        <a id="downloadLink" href="#" download>Download Result</a>
    </div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const videoFile = document.querySelector('input[name="video"]').files[0];
            const customPrompt = document.querySelector('textarea[name="custom_prompt"]').value;
            
            formData.append('video', videoFile);
            if (customPrompt) formData.append('custom_prompt', customPrompt);
            
            document.getElementById('progress').style.display = 'block';
            
            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                const jobId = data.job_id;
                
                // Poll for status
                const checkStatus = async () => {
                    const statusResponse = await fetch(`/status/${jobId}`);
                    const statusData = await statusResponse.json();
                    
                    document.getElementById('status').textContent = `Status: ${statusData.status} (${statusData.progress}%)`;
                    document.getElementById('progressBar').value = statusData.progress;
                    
                    if (statusData.status === 'completed') {
                        document.getElementById('progress').style.display = 'none';
                        document.getElementById('result').style.display = 'block';
                        
                        const video = document.getElementById('resultVideo');
                        video.src = statusData.result_url;
                        
                        document.getElementById('downloadLink').href = statusData.result_url;
                    } else if (statusData.status === 'failed') {
                        alert('Processing failed: ' + statusData.error);
                        document.getElementById('progress').style.display = 'none';
                    } else {
                        setTimeout(checkStatus, 2000);
                    }
                };
                
                checkStatus();
                
            } catch (error) {
                alert('Upload failed: ' + error.message);
                document.getElementById('progress').style.display = 'none';
            }
        });
    </script>
</body>
</html>"""

# Write the template
with open("templates/index.html", "w") as f:
    f.write(index_html)

if __name__ == "__main__":
    import uvicorn
    port = int(os.getenv("PORT", 8000))
    uvicorn.run(app, host="0.0.0.0", port=port)

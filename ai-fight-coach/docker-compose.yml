version: '3.8'

services:
  postgres:
    image: postgres:15-alpine
    environment:
      POSTGRES_USER: user
      POSTGRES_PASSWORD: password
      POSTGRES_DB: dbname
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data

  ai-fight-coach:
    build: .
    ports:
      - "8000:8000"
    environment:
      - DATABASE_URL=****************************************/dbname
      - GOOGLE_API_KEY=${GOOGLE_API_KEY}
      - ELEVENLABS_API_KEY=${ELEVENLABS_API_KEY}
      - PORT=8000
    depends_on:
      - postgres
    volumes:
      - ./uploads:/app/uploads
      - ./static:/app/static
      - ./output:/app/output

volumes:
  postgres_data:

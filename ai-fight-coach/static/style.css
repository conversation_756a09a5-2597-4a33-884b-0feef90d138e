/* Main Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Page Styles */
.page {
    display: none;
    min-height: 100vh;
    padding: 20px;
}

.page.active {
    display: block;
}

/* Upload Page */
.upload-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    margin-bottom: 30px;
}

.upload-section h1 {
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 2.5em;
    font-weight: 700;
}

.file-upload-area {
    border: 3px dashed #3498db;
    border-radius: 10px;
    padding: 40px;
    margin: 20px 0;
    cursor: pointer;
    transition: all 0.3s ease;
    background: rgba(52, 152, 219, 0.05);
}

.file-upload-area:hover {
    border-color: #2980b9;
    background: rgba(52, 152, 219, 0.1);
}

.file-upload-area.dragover {
    border-color: #27ae60;
    background: rgba(39, 174, 96, 0.1);
}

.upload-icon {
    font-size: 3em;
    color: #3498db;
    margin-bottom: 20px;
}

.upload-text {
    font-size: 1.2em;
    color: #7f8c8d;
    margin-bottom: 10px;
}

.upload-subtext {
    font-size: 0.9em;
    color: #95a5a6;
}

/* Analysis Options */
.analysis-options {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.analysis-options h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.options-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.option-card {
    background: white;
    border: 2px solid #ecf0f1;
    border-radius: 10px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.option-card:hover {
    border-color: #3498db;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.option-card.selected {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.option-card input[type="radio"] {
    display: none;
}

.option-icon {
    font-size: 2em;
    margin-bottom: 10px;
    color: #3498db;
}

.option-title {
    font-weight: 600;
    margin-bottom: 10px;
    color: #2c3e50;
}

.option-description {
    color: #7f8c8d;
    font-size: 0.9em;
    line-height: 1.4;
}

/* Guidelines */
.guidelines-section {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.guidelines-section h2 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.guidelines-list {
    list-style: none;
    padding: 0;
}

.guidelines-list li {
    padding: 10px 0;
    border-bottom: 1px solid #ecf0f1;
    display: flex;
    align-items: center;
}

.guidelines-list li:last-child {
    border-bottom: none;
}

.guidelines-list li::before {
    content: "✓";
    color: #27ae60;
    font-weight: bold;
    margin-right: 10px;
    font-size: 1.2em;
}

/* Buttons */
.btn {
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 25px;
    font-size: 1.1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
    margin: 10px;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.btn:disabled {
    background: #bdc3c7;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-secondary {
    background: linear-gradient(135deg, #95a5a6, #7f8c8d);
}

.btn-success {
    background: linear-gradient(135deg, #27ae60, #229954);
}

.btn-danger {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
}

/* Progress Page */
.progress-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    text-align: center;
    max-width: 600px;
    margin: 50px auto;
}

.progress-bar {
    width: 100%;
    height: 20px;
    background: #ecf0f1;
    border-radius: 10px;
    overflow: hidden;
    margin: 20px 0;
    position: relative;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #3498db, #2980b9);
    border-radius: 10px;
    transition: width 0.3s ease;
    position: relative;
    overflow: hidden;
}

.progress-fill::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

.progress-text {
    font-size: 1.2em;
    color: #2c3e50;
    margin-bottom: 20px;
    font-weight: 600;
}

.loading-stages {
    margin-top: 30px;
}

.stage {
    padding: 10px;
    margin: 5px 0;
    border-radius: 5px;
    transition: all 0.3s ease;
    opacity: 0.5;
}

.stage.active {
    background: rgba(52, 152, 219, 0.1);
    color: #3498db;
    opacity: 1;
    font-weight: 600;
}

.stage.completed {
    background: rgba(39, 174, 96, 0.1);
    color: #27ae60;
    opacity: 1;
}

/* Results Page */
.results-container {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.results-header {
    text-align: center;
    margin-bottom: 30px;
}

.results-header h1 {
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 2.5em;
}

.results-header p {
    color: #7f8c8d;
    font-size: 1.1em;
}

.video-container {
    margin: 30px 0;
    text-align: center;
}

#resultVideo {
    max-width: 100%;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.action-buttons {
    text-align: center;
    margin: 30px 0;
}

.section-title {
    color: #2c3e50;
    margin: 30px 0 20px 0;
    font-size: 1.8em;
    font-weight: 600;
    border-bottom: 2px solid #3498db;
    padding-bottom: 10px;
}

.highlight-card, .drill-card, .youtube-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 15px auto;  /* Center the cards */
    max-width: 800px;   /* Limit width for better readability */
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    transition: all 0.3s ease;
    text-align: left;   /* Keep text left-aligned within cards */
}

.highlight-card:hover, .drill-card:hover, .youtube-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.15);
}

.highlight-title, .drill-name, .youtube-title-text {
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.highlight-description, .drill-description, .youtube-problem {
    color: #7f8c8d;
    line-height: 1.6;
    margin-bottom: 10px;
}

.highlight-action, .drill-problem {
    color: #3498db;
    font-weight: 600;
    font-style: italic;
}

.youtube-embed {
    margin-top: 15px;
    text-align: center;  /* Center the YouTube embed */
}

.youtube-embed iframe {
    border-radius: 8px;
    box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
    max-width: 100%;    /* Ensure iframe doesn't overflow */
}

.youtube-link {
    display: inline-block;
    background: #e74c3c;
    color: white;
    padding: 10px 20px;
    border-radius: 5px;
    text-decoration: none;
    margin-top: 10px;
    transition: all 0.3s ease;
    text-align: center;  /* Center the button text */
}

.youtube-link:hover {
    background: #c0392b;
    transform: translateY(-1px);
}

/* Feedback Form */
.feedback-form {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 15px;
    padding: 30px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
    margin-top: 30px;
}

.feedback-form h3 {
    color: #2c3e50;
    margin-bottom: 20px;
    text-align: center;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #2c3e50;
    font-weight: 600;
}

.form-group input, .form-group textarea, .form-group select {
    width: 100%;
    padding: 12px;
    border: 2px solid #ecf0f1;
    border-radius: 8px;
    font-size: 1em;
    transition: border-color 0.3s ease;
}

.form-group input:focus, .form-group textarea:focus, .form-group select:focus {
    outline: none;
    border-color: #3498db;
}

.rating-buttons {
    display: flex;
    gap: 10px;
    margin-top: 10px;
}

.rating-btn {
    padding: 10px 15px;
    border: 2px solid #ecf0f1;
    background: white;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rating-btn:hover {
    border-color: #3498db;
    background: rgba(52, 152, 219, 0.05);
}

.rating-btn.selected {
    border-color: #3498db;
    background: #3498db;
    color: white;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .container {
        padding: 10px;
    }
    
    .upload-section, .analysis-options, .guidelines-section, .results-container {
        padding: 20px;
    }
    
    .upload-section h1 {
        font-size: 2em;
    }
    
    .options-grid {
        grid-template-columns: 1fr;
    }
    
    .btn {
        padding: 12px 25px;
        font-size: 1em;
    }
    
    .progress-container {
        margin: 20px auto;
        padding: 20px;
    }
    
    .results-header h1 {
        font-size: 2em;
    }
}

@media (max-width: 480px) {
    .upload-section h1 {
        font-size: 1.8em;
    }
    
    .file-upload-area {
        padding: 20px;
    }
    
    .upload-icon {
        font-size: 2.5em;
    }
    
    .btn {
        padding: 10px 20px;
        font-size: 0.9em;
    }
    
    .rating-buttons {
        flex-wrap: wrap;
    }
    
    .rating-btn {
        flex: 1;
        min-width: 60px;
    }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    body {
        background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
        color: #ecf0f1;
    }
    
    .upload-section, .analysis-options, .guidelines-section, .results-container, .feedback-form {
        background: rgba(44, 62, 80, 0.95);
        color: #ecf0f1;
    }
    
    .option-card, .highlight-card, .drill-card, .youtube-card {
        background: rgba(52, 73, 94, 0.9);
        color: #ecf0f1;
    }
    
    .form-group input, .form-group textarea, .form-group select {
        background: rgba(52, 73, 94, 0.9);
        color: #ecf0f1;
        border-color: #34495e;
    }
    
    .rating-btn {
        background: rgba(52, 73, 94, 0.9);
        color: #ecf0f1;
        border-color: #34495e;
    }
} 
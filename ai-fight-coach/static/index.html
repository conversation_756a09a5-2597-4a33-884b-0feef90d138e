<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>AI Fight Coach - Professional Boxing Analysis</title>
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-V4WX8SQ8LJ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-V4WX8SQ8LJ');
    </script>
    
    <style>
        /* Import Inter font */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap');
        
        /* Reset and base styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: #FFFFFF;
            color: #FFFFFF;
            min-height: 100vh;
            padding: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        /* Main container */
        .main-container {
            display: flex;
            flex-direction: column;
            width: 100%;
            max-width: 540px;
            gap: 24px;
        }
        
        /* Desktop layout */
        @media (min-width: 768px) {
            .main-container {
                flex-direction: row;
                max-width: 960px;
                gap: 24px;
            }
            
            .card {
                width: calc(50% - 12px);
            }
        }
        
        /* Card styles */
        .card {
            background: #151517;
            border-radius: 16px;
            padding: 32px 28px 36px;
            box-shadow: 0px 12px 30px rgba(0,0,0,0.25);
            min-height: 560px;
            display: flex;
            flex-direction: column;
        }
        
        /* Typography */
        .h1 {
            font-size: 28px;
            font-weight: 700;
            line-height: 1.15;
            letter-spacing: -0.02em;
            margin-bottom: 24px;
            color: #FFFFFF;
        }
        
        .h2 {
            font-size: 24px;
            font-weight: 700;
            line-height: 1.2;
            letter-spacing: -0.01em;
            margin-bottom: 24px;
            color: #FFFFFF;
        }
        
        .step-number {
            font-size: 18px;
            font-weight: 600;
            line-height: 1.25;
            font-variant-numeric: tabular-nums;
            color: #FFFFFF;
        }
        
        .step-heading {
            font-size: 18px;
            font-weight: 600;
            line-height: 1.25;
            letter-spacing: -0.01em;
            color: #FFFFFF;
        }
        
        .body-copy {
            font-size: 15px;
            font-weight: 400;
            line-height: 1.45;
            color: #FFFFFF;
        }
        
        .sub-body {
            font-size: 15px;
            font-weight: 400;
            line-height: 1.45;
            color: #D0D0D0;
        }
        
        .button-text {
            font-size: 17px;
            font-weight: 600;
            line-height: 1.1;
            letter-spacing: -0.01em;
            color: #0B0B0C;
        }
        
        .overlay-label {
            font-size: 12px;
            font-weight: 600;
            line-height: 1;
            letter-spacing: 0.02em;
            text-transform: uppercase;
            color: #FFFFFF;
        }
        
        /* Desktop typography adjustments */
        @media (min-width: 768px) {
            .h1 {
                font-size: 32px;
            }
            
            .h2 {
                font-size: 26px;
            }
            
            .step-heading {
                font-size: 20px;
            }
            
            .body-copy {
                font-size: 16px;
            }
        }
        
        /* Bullet points */
        .bullet-list {
            margin-bottom: 32px;
        }
        
        .bullet-item {
            display: flex;
            align-items: flex-start;
            margin-bottom: 12px;
        }
        
        .bullet-icon {
            width: 20px;
            margin-right: 8px;
            flex-shrink: 0;
            margin-top: 2px;
        }
        
        .bullet-text {
            color: #FFFFFF;
            font-size: 15px;
            line-height: 1.45;
        }
        
        /* Primary CTA Button */
        .upload-button {
            width: 100%;
            height: 54px;
            background: #FFFFFF;
            border: none;
            border-radius: 12px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            transition: all 0.2s ease;
            position: relative;
            overflow: hidden;
        }
        
        .upload-button:hover {
            transform: scale(0.95);
        }
        
        .upload-button:active {
            transform: scale(0.95);
        }
        
        /* Hidden file input overlay */
        .file-input-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
        }
        
        /* Analysis type dropdown */
        .analysis-dropdown {
            width: 100%;
            padding: 12px 16px;
            background: #FFFFFF;
            border: 1px solid #E5E5E5;
            border-radius: 8px;
            font-size: 16px;
            color: #0B0B0C;
            margin-bottom: 16px;
            cursor: pointer;
        }
        
        .analysis-dropdown:focus {
            outline: none;
            border-color: #151517;
        }
        
        /* Start analysis button */
        .start-analysis-btn {
            width: 100%;
            height: 54px;
            background: #151517;
            border: 2px solid #FFFFFF;
            border-radius: 12px;
            color: #FFFFFF;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            display: none;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        
        .start-analysis-btn:hover {
            background: #FFFFFF;
            color: #151517;
        }
        
        .start-analysis-btn:active {
            transform: scale(0.95);
        }
        
        /* Video thumbnail */
        .video-thumbnail {
            width: 100%;
            aspect-ratio: 16/9;
            background: #151517;
            border: 1px solid #151517;
            border-radius: 12px;
            position: relative;
            margin-bottom: 8px;
            overflow: hidden;
        }
        
        .play-badge {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 40px;
            height: 40px;
            background: rgba(90, 90, 90, 0.6);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .play-icon {
            width: 0;
            height: 0;
            border-left: 8px solid #FFFFFF;
            border-top: 6px solid transparent;
            border-bottom: 6px solid transparent;
            margin-left: 2px;
        }
        
        .overlay-tag {
            position: absolute;
            bottom: 8px;
            left: 8px;
            background: rgba(0, 0, 0, 0.7);
            padding: 4px 8px;
            border-radius: 8px;
        }
        
        /* Step content */
        .step-content {
            margin-bottom: 32px;
        }
        
        .step-content:last-child {
            margin-bottom: 0;
        }
        
        .step-heading {
            margin-bottom: 6px;
        }
        
        .step-sub-copy {
            margin-bottom: 16px;
        }
        
        /* Responsive adjustments */
        @media (max-width: 767px) {
            body {
                padding: 24px;
            }
            
            .main-container {
                gap: 24px;
            }
            
            .card {
                min-height: auto;
            }
        }
        
        /* Progress and Results pages (keep existing styles) */
        .page {
            display: none;
        }
        
        .page.active {
            display: block;
        }
        
        /* Progress and Results pages styles */
        .progress-container {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            display: block;
        }

        .progress-container h3 {
            color: white;
            font-size: 1.5rem;
            margin-bottom: 20px;
            text-align: center;
        }

        .progress-container p {
            color: #cccccc;
            font-size: 1.1rem;
            text-align: center;
            margin-top: 15px;
        }

        .progress-bar {
            width: 100%;
            height: 20px;
            background: rgba(0, 0, 0, 0.8);
            border-radius: 10px;
            overflow: hidden;
            position: relative;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #dc143c, #8b0000, #dc143c);
            border-radius: 10px;
            transition: width 0.3s ease;
            position: relative;
        }

        .progress-fill::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
            animation: progress-shimmer 2s ease-in-out infinite;
        }

        @keyframes progress-shimmer {
            0%, 100% { transform: translateX(-100%); }
            50% { transform: translateX(100%); }
        }

        .loading-stages {
            margin-top: 20px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .stage {
            padding: 10px 15px;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 8px;
            color: #cccccc;
            font-size: 0.9rem;
            opacity: 0.5;
            transition: all 0.3s ease;
        }

        .stage.active {
            border-color: #000000;
            color: #ffffff;
            opacity: 1;
            background: linear-gradient(135deg, #dc143c 0%, #8b0000 100%);
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
        }

        .stage.completed {
            border-color: #28a745;
            color: #ffffff;
            opacity: 0.8;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }

        /* Results page styles */
        .results-header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
        }

        .results-header h1 {
            color: #000000;
            font-size: 2.5rem;
            margin-bottom: 10px;
            font-weight: 700;
        }

        .results-header p {
            color: #ffffff;
            font-size: 1.2rem;
            opacity: 0.8;
        }

        .video-player-section, .highlights-section, .drills-section, .youtube-section {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
        }

        .section-title {
            color: #000000;
            font-size: 1.8rem;
            margin-bottom: 25px;
            text-align: center;
            font-weight: 600;
        }

        .video-container {
            position: relative;
            border-radius: 10px;
            overflow: hidden;
            margin-bottom: 20px;
            background: #000;
            min-height: 300px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .video-container video {
            width: 100%;
            max-width: 800px;
            height: auto;
            border-radius: 10px;
        }
        
        .video-container .video-placeholder {
            color: #000000;
            text-align: center;
            padding: 40px;
            font-size: 1.1rem;
        }

        .action-buttons-section {
            text-align: center;
            margin: 30px 0;
        }

        .download-btn, .analyze-again-btn {
            background: linear-gradient(135deg, #000000 0%, #8b0000 100%);
            color: white;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
            margin: 0 10px;
        }

        .download-btn:hover, .analyze-again-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.5);
        }

        .highlight-card, .drill-card, .youtube-card {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 15px;
            transition: all 0.3s ease;
        }

        .highlight-card:hover, .drill-card:hover, .youtube-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.3);
        }

        .highlight-title, .drill-name, .youtube-title-text {
            color: #dc143c;
            font-size: 1.2rem;
            font-weight: bold;
            margin-bottom: 10px;
        }

        .highlight-description, .drill-description, .youtube-problem {
            color: #ffffff;
            font-size: 1rem;
            margin-bottom: 8px;
        }

        .highlight-action, .drill-problem {
            color: #cccccc;
            font-size: 0.9rem;
            font-style: italic;
        }

        .youtube-link {
            display: inline-block;
            background: linear-gradient(135deg, #000000 0%, #8b0000 100%);
            color: white;
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.9rem;
            transition: all 0.3s ease;
            margin-top: 10px;
        }

        .youtube-link:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
        }

        .feedback-section {
            display: flex;
            justify-content: center;
            margin: 40px 0;
        }

        .feedback-box {
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 15px;
            padding: 30px;
            max-width: 600px;
            width: 100%;
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.2);
        }

        .feedback-icon {
            font-size: 2rem;
            text-align: center;
            margin-bottom: 15px;
        }

        .feedback-content h4 {
            color: #000000;
            font-size: 1.3rem;
            margin-bottom: 10px;
            text-align: center;
        }

        .feedback-content p {
            color: #ffffff;
            text-align: center;
            margin-bottom: 20px;
        }

        .feedback-input, .feedback-textarea {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 8px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            font-size: 1rem;
        }

        .feedback-input::placeholder, .feedback-textarea::placeholder {
            color: #cccccc;
        }

        .feedback-input:focus, .feedback-textarea:focus {
            outline: none;
            border-color: #000000;
            box-shadow: 0 0 10px rgba(220, 20, 60, 0.3);
        }

        .feedback-buttons {
            display: flex;
            justify-content: center;
            gap: 15px;
            margin-top: 15px;
        }

        .feedback-btn {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 20px;
            font-size: 0.9rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 10px rgba(40, 167, 69, 0.3);
        }

        .feedback-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(40, 167, 69, 0.5);
        }

        .feedback-btn.selected {
            background: linear-gradient(135deg, #dc143c 0%, #8b0000 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 20, 60, 0.5);
        }

        .submit-feedback-btn {
            background: linear-gradient(135deg, #dc143c 0%, #8b0000 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(220, 20, 60, 0.3);
            display: block;
            margin: 0 auto;
            margin-top: 15px;
        }

        .submit-feedback-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.5);
        }

        .youtube-embed {
            margin: 15px 0;
            border-radius: 10px;
            overflow: hidden;
        }

        .youtube-embed iframe {
            border-radius: 10px;
            width: 100%;
            height: 200px;
        }
        
        /* Modal styles */
        .modal {
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.8);
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-content {
            background: rgba(0, 0, 0, 0.9);
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 15px;
            padding: 30px;
            max-width: 400px;
            width: 90%;
            text-align: center;
            color: white;
        }
        
        .modal-content h3 {
            color: #D0D0D0;
            margin-bottom: 15px;
        }
        
        .modal-content p {
            margin-bottom: 20px;
            color: #cccccc;
        }
        
        .modal-content input {
            width: 100%;
            padding: 12px;
            margin: 10px 0;
            border: 1px solid rgba(220, 20, 60, 0.3);
            border-radius: 8px;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            font-size: 1rem;
        }
        
        .modal-content input::placeholder {
            color: #cccccc;
        }
        
        .modal-content button {
            background: linear-gradient(135deg, #000 0%, #8b0000 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 25px;
            cursor: pointer;
            margin-top: 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }
        
        .modal-content button:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(220, 20, 60, 0.5);
        }
    </style>
</head>
<body>
    <!-- Root Page (New Design) -->
    <div id="rootPage" class="page active">
        <div class="main-container">
            <!-- Left Card: Pitch -->
            <div class="card">
                <h1 class="h1">Box Smarter in 5 Minutes - Without a Coach</h1>
                
                <div class="bullet-list">
                    <div class="bullet-item">
                        <span class="bullet-icon">🎥</span>
                        <span class="bullet-text">Upload your boxing video</span>
                    </div>
                    <div class="bullet-item">
                        <span class="bullet-icon">⚡</span>
                        <span class="bullet-text">Get instant feedback & drills to improve</span>
                    </div>
                    <div class="bullet-item">
                        <span class="bullet-icon">🏠</span>
                        <span class="bullet-text">Train at home. No gym. No stress</span>
                    </div>
                </div>
                
                <div style="margin-top: auto;">
                    <select id="analysisType" class="analysis-dropdown">
                        <option value="everything">Complete Analysis (Everything)</option>
                        <option value="head_movement">Defense Focus</option>
                        <option value="punch_techniques">Punch Techniques</option>
                        <option value="footwork">Footwork & Movement</option>
                    </select>
                    
                    <button class="upload-button" onclick="document.getElementById('videoFile').click()">
                        Upload My Clip →
                        <input type="file" id="videoFile" accept="video/*" class="file-input-overlay">
                    </button>
                    
                    <button id="startAnalysisBtn" class="start-analysis-btn" onclick="startAnalysis()">
                        Start Analysis →
                    </button>
                </div>
            </div>
            
            <!-- Right Card: How It Works -->
            <div class="card">
                <h2 class="h2">How It Works</h2>
                
                <div class="step-content">
                    <div class="step-number">1</div>
                    <div class="step-heading">Upload Your Clip</div>
                    <div class="sub-body">10-30 seconds of shadowboxing, bag work, or sparring</div>
                    
                    <div class="video-thumbnail">
                        <div class="play-badge">
                            <div class="play-icon"></div>
                        </div>
                        <div class="overlay-tag">
                            <span class="overlay-label">DROPPING LEFT HAND</span>
                        </div>
                    </div>
                </div>
                
                <div class="step-content">
                    <div class="step-number">2</div>
                    <div class="step-heading">Get an INSTANT breakdown of your boxing skill. </div>
                    <div class="sub-body">Slow Motion BREAKDOWN + Highlighted mistakes and coach level corrections (in as little as 3 minutes)</div>
                </div>
            </div>
        </div>
    </div>

    <!-- Progress Page (Keep existing) -->
    <div id="progressPage" class="page">
        <div class="progress-container">
            <h3>Analyzing your video...</h3>
            <div class="progress-bar">
                <div class="progress-fill" id="progressFill" style="width: 0%"></div>
            </div>
            <p id="progressText">Initializing analysis...</p>
            <div class="loading-stages">
                <div class="stage" id="stage1">📹 Uploading video...</div>
                <div class="stage" id="stage2">🤖 AI analyzing footage...</div>
                <div class="stage" id="stage3">🎯 Detecting highlights...</div>
                <div class="stage" id="stage4">🎬 Processing video...</div>
                <div class="stage" id="stage5">🔊 Generating audio...</div>
                <div class="stage" id="stage6">✨ Finalizing results...</div>
            </div>
        </div>
    </div>

    <!-- Results Page (Keep existing) -->
    <div id="resultsPage" class="page">
        <!-- Keep all existing results page content -->
        <div class="results-header">
            <h1>Your Analysis is Ready</h1>
            <p>Review your boxing technique with AI-powered insights</p>
        </div>

        <div id="videoPlayerContainer" class="video-player-section">
            <h2 class="section-title">Your Analyzed Video</h2>
            <div class="video-container">
                <video id="resultVideo" controls>
                    Your browser does not support the video tag.
                </video>
            </div>
        </div>

        <div id="actionButtonsContainer" class="action-buttons-section">
            <button class="download-btn" onclick="downloadVideo()">Download Video</button>
            <button class="analyze-again-btn" onclick="runAnotherAnalysis()">Run Another Analysis</button>
        </div>
        
        <div id="youtubeContainer" class="youtube-section">
            <h2 class="section-title">YouTube Recommendations</h2>
        </div>

        <div id="highlightsContainer" class="highlights-section">
            <h2 class="section-title">Key Highlights</h2>
        </div>

        <div id="drillsContainer" class="drills-section">
            <h2 class="section-title">Recommended Drills</h2>
        </div>

        <div id="feedbackFormContainer" class="feedback-section">
            <div class="feedback-box">
                <div class="feedback-icon">💬</div>
                <div class="feedback-content">
                    <h4>How was your analysis experience?</h4>
                    <p>We'd love to hear your feedback to make this tool even better for boxers like you!</p>
                    <form id="feedbackForm" onsubmit="submitFeedback(event)">
                        <input type="email" id="feedbackEmail" placeholder="Enter your email (optional)" class="feedback-input">
                        <textarea id="feedbackText" placeholder="Tell us what you think about the analysis, what worked well, and what could be improved..." rows="4" class="feedback-textarea"></textarea>
                        <div class="feedback-buttons">
                            <button type="button" class="feedback-btn positive" onclick="setFeedbackRating('positive')">👍 Great!</button>
                            <button type="button" class="feedback-btn neutral" onclick="setFeedbackRating('neutral')">😐 Okay</button>
                            <button type="button" class="feedback-btn negative" onclick="setFeedbackRating('negative')">👎 Needs Work</button>
                        </div>
                        <button type="submit" class="submit-feedback-btn">Send Feedback</button>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Registration Modal -->
    <div id="registrationModal" class="modal" style="display: none;">
        <div class="modal-content">
            <h3>Complete Your Registration</h3>
            <p>Please enter your details to view your analysis results:</p>
            <form id="registrationForm">
                <input type="text" id="userName" placeholder="Your Name" required>
                <input type="email" id="userEmail" placeholder="Your Email" required>
                <button type="submit">Submit</button>
            </form>
        </div>
    </div>

    <script>
        let selectedAnalysisType = 'everything';
        let currentJobId = null;
        let selectedFeedbackRating = null;

        // Cookie helper functions
        function setCookie(name, value, days) {
            const expires = new Date();
            expires.setTime(expires.getTime() + (days * 24 * 60 * 60 * 1000));
            document.cookie = name + "=" + value + ";expires=" + expires.toUTCString() + ";path=/";
        }

        function getCookie(name) {
            const nameEQ = name + "=";
            const ca = document.cookie.split(';');
            for(let i = 0; i < ca.length; i++) {
                let c = ca[i];
                while (c.charAt(0) == ' ') c = c.substring(1, c.length);
                if (c.indexOf(nameEQ) == 0) return c.substring(nameEQ.length, c.length);
            }
            return null;
        }

        // File selection handler
        document.getElementById('videoFile').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                // Show start analysis button
                document.getElementById('startAnalysisBtn').style.display = 'flex';
            }
        });

        // Analysis type selection
        document.getElementById('analysisType').addEventListener('change', function(e) {
            selectedAnalysisType = e.target.value;
        });

        function startAnalysis() {
            const fileInput = document.getElementById('videoFile');
            
            if (!fileInput.files[0]) {
                alert('Please select a video file first.');
                return;
            }

            // Start loading animation immediately
            startLoadingAnimation();
            
            // Upload video
            uploadVideo(fileInput.files[0], selectedAnalysisType);
        }
        
        function uploadVideo(file, analysisType) {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('analysis_type', analysisType);
            
            // Get user email from cookies
            const userEmail = getCookie('user_email') || '<EMAIL>';
            formData.append('email', userEmail);

            fetch('/upload-video', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('[UPLOAD] Success, job ID:', data.job_id);
                    // Start real job polling with the actual job ID
                    startRealJobPolling(data.job_id);
                } else {
                    console.error('[UPLOAD] Failed:', data.message);
                    alert(`Upload failed: ${data.message}`);
                    // Reset to upload page on failure
                    showPage('rootPage');
                }
            })
            .catch(error => {
                console.error('[UPLOAD] Error:', error);
                alert('Upload failed. Please try again.');
                // Reset to upload page on error
                showPage('rootPage');
            });
        }

        // Loading system
        let loadingInterval;
        let startTime = null;
        
        function startLoadingAnimation() {
            console.log('[LOADING] Starting instant loading animation');
            
            // Clear any existing timers
            if (loadingInterval) {
                clearInterval(loadingInterval);
            }
            
            // Start immediately with provisional jobId
            currentJobId = 'pending';
            startTime = Date.now();
            
            // Show progress page immediately
            showPage('progressPage');
            
            // Start the animation
            loadingInterval = setInterval(updateLoadingProgress, 100);
        }
        
        function updateLoadingProgress() {
            if (!startTime) return;
            
            const elapsed = (Date.now() - startTime) / 1000; // seconds
            const progressFill = document.getElementById('progressFill');
            const progressText = document.getElementById('progressText');
                
            if (!progressFill || !progressText) return;
            
            // Fill-rate curve (total 3 minutes)
            let progressPercent = 0;
            
            if (elapsed <= 30) {
                // Segment A: 0 – 30s → 0 → 50% (linear)
                progressPercent = (elapsed / 30) * 50;
            } else if (elapsed <= 120) {
                // Segment B: 30 – 120s → 50 → 75% (linear)
                progressPercent = 50 + ((elapsed - 30) / 90) * 25;
            } else if (elapsed <= 150) {
                // Segment C: 120 – 150s → 75 → 90% (linear)
                progressPercent = 75 + ((elapsed - 120) / 30) * 15;
            } else {
                // Hold: 150s → finish → stay at 90%
                progressPercent = 90;
            }
            
            // Update progress bar
            progressFill.style.width = `${progressPercent}%`;
            
            // Update stage captions (6 labels)
            updateLoadingStages(progressPercent);
            
            // Update text based on progress
            if (progressPercent <= 15) {
                progressText.textContent = 'Initializing AI Coach...';
            } else if (progressPercent <= 30) {
                progressText.textContent = 'Importing video file';
            } else if (progressPercent <= 45) {
                progressText.textContent = 'Detecting fighter pose';
            } else if (progressPercent <= 60) {
                progressText.textContent = 'Analyzing technique';
            } else if (progressPercent <= 75) {
                progressText.textContent = 'Generating highlights';
            } else if (progressPercent <= 90) {
                progressText.textContent = 'Adding voice-over & captions';
            } else {
                progressText.textContent = 'Finalizing analysis...';
            }
        }
        
        function updateLoadingStages(progress) {
            const stages = document.querySelectorAll('.stage');
            stages.forEach((stage, index) => {
                const thresholds = [15, 30, 45, 60, 75, 90];
                if (progress >= thresholds[index]) {
                    stage.classList.add('active');
                } else {
                    stage.classList.remove('active');
                }
            });
        }
        
        function startRealJobPolling(jobId) {
            console.log('[LOADING] Starting real job polling for:', jobId);
            
            // Clear existing interval
            if (loadingInterval) {
                clearInterval(loadingInterval);
            }
            
            // Update job ID
            currentJobId = jobId;
            
            // Poll every 2 seconds
            loadingInterval = setInterval(() => {
                pollJobStatus(jobId);
            }, 2000);
        }
        
        function pollJobStatus(jobId) {
            fetch(`/status/${jobId}`)
                .then(response => response.json())
                .then(data => {
                    console.log('[POLLING] Status:', data.status);
                    
                    if (data.status === 'completed') {
                        // Job finished - snap to 100% and redirect
                        const progressFill = document.getElementById('progressFill');
                        const progressText = document.getElementById('progressText');
                        
                        if (progressFill) progressFill.style.width = '100%';
                        if (progressText) progressText.textContent = 'Ready! Redirecting...';
                        
                        // Clear polling
                        if (loadingInterval) {
                            clearInterval(loadingInterval);
                        }
                        
                        // Redirect after 0.5s with absolute path
                        setTimeout(() => {
                            window.location.href = `${window.location.origin}/results/${jobId}`;
                        }, 500);
                    }
                    // If not completed, continue polling (bar stays at 90% after 150s)
                })
                .catch(error => {
                    console.error('[POLLING] Error:', error);
                    // Continue polling on error
                });
        }
        
        function showPage(pageId) {
            console.log('showPage called with pageId:', pageId);
            document.querySelectorAll('.page').forEach(page => {
                page.classList.remove('active');
                console.log('Removed active from page:', page.id);
            });
            const targetPage = document.getElementById(pageId);
            console.log('Target page element:', targetPage);
            targetPage.classList.add('active');
            console.log('Added active to page:', pageId);
        }

        function downloadVideo() {
            const videoElement = document.getElementById('resultVideo');
            if (videoElement && videoElement.src) {
                // Create a temporary link to download the video
                const a = document.createElement('a');
                a.href = videoElement.src;
                a.download = 'boxing_analysis.mp4';
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                alert('Video downloaded successfully!');
            } else {
                alert('No video available to download.');
            }
        }

        function runAnotherAnalysis() {
            // Redirect to root page
            window.location.href = '/';
        }

        function setFeedbackRating(rating) {
            console.log('Setting feedback rating:', rating);
            selectedFeedbackRating = rating;
            // Remove active class from all buttons
            document.querySelectorAll('.feedback-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            // Add active class to selected button
            const selectedButton = document.querySelector(`.feedback-btn.${rating}`);
            if (selectedButton) {
                selectedButton.classList.add('active');
                console.log('Added active class to button:', rating);
            } else {
                console.error('Button not found for rating:', rating);
            }
        }

        function submitFeedback(event) {
            event.preventDefault();
            
            const email = document.getElementById('feedbackEmail').value;
            const feedbackText = document.getElementById('feedbackText').value;
            const rating = selectedFeedbackRating;
            
            if (!rating) {
                alert('Please select a rating before submitting feedback.');
                return;
            }
            
            // Create form data
            const formData = new FormData();
            formData.append('email', email || '<EMAIL>');
            formData.append('rating', rating);
            formData.append('feedback_text', feedbackText || 'No text provided');
            
            // Submit feedback
            fetch('/submit-feedback', {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('Thank you for your feedback! We appreciate your input.');
                    // Clear the form
                    document.getElementById('feedbackEmail').value = '';
                    document.getElementById('feedbackText').value = '';
                    selectedFeedbackRating = null;
                    // Update button states
                    document.querySelectorAll('.feedback-btn').forEach(btn => {
                        btn.classList.remove('selected');
                    });
                } else {
                    alert('Failed to submit feedback. Please try again.');
                }
            })
            .catch(error => {
                console.error('Error submitting feedback:', error);
                alert('Failed to submit feedback. Please try again.');
            });
        }

        // Helper function to get cookies
        function getCookie(name) {
            const value = `; ${document.cookie}`;
            const parts = value.split(`; ${name}=`);
            if (parts.length === 2) return parts.pop().split(';').shift();
            return null;
        }
        
        // Initialize page based on URL
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[INIT] Page loaded, checking URL...');
            
            // Check if we're on a results page
            const path = window.location.pathname;
            const resultsMatch = path.match(/\/results\/([^\/]+)/);
            
            if (resultsMatch) {
                const jobId = resultsMatch[1];
                console.log('[INIT] Results page detected, job ID:', jobId);
                
                // Show results page
                showPage('resultsPage');
                
                // Load and display results
                showResults(jobId);
            } else {
                console.log('[INIT] Not on results page, showing root page');
                showPage('rootPage');
            }
        });
        
        // Function to show results (called from initialization or redirect)
        function showResults(jobId) {
            // Check if user is registered
            const userRegistered = getCookie("userRegistered");
            
            if (!userRegistered) {
                // Show registration modal
                showRegistrationModal(jobId);
                return;
            }
            
            // Proceed with normal results display
            displayResults(jobId);
        }

        function showRegistrationModal(jobId) {
            const modal = document.getElementById("registrationModal");
            modal.style.display = "flex";
            
            // Handle form submission
            document.getElementById("registrationForm").onsubmit = async (e) => {
                e.preventDefault();
                
                const name = document.getElementById("userName").value;
                const email = document.getElementById("userEmail").value;
                
                try {
                    const response = await fetch("/api/register", {
                        method: "POST",
                        headers: {"Content-Type": "application/json"},
                        body: JSON.stringify({name, email})
                    });
                    
                    const data = await response.json();
                    
                    if (data.success) {
                        // Set cookie for 1 year
                        setCookie("userRegistered", "true", 365);
                        setCookie("userName", name, 365);
                        setCookie("userEmail", email, 365);
                        
                        // Hide modal and show results
                        modal.style.display = "none";
                        displayResults(jobId);
                    } else {
                        alert("Registration failed: " + data.message);
                    }
                } catch (error) {
                    alert("Registration failed. Please try again.");
                }
            };
        }

        function displayResults(jobId) {
            console.log('[SHOWRESULTS] Starting results display for job:', jobId);
            
            // Set the video source with proper error handling
            const videoElement = document.getElementById('resultVideo');
            if (videoElement) {
                videoElement.src = `/video/${jobId}`;
                videoElement.load();
                
                // Add error handling for video loading
                videoElement.onerror = function() {
                    console.error('[SHOWRESULTS] Video failed to load');
                    videoElement.innerHTML = '<p style="color: #dc143c; text-align: center; padding: 20px;">Video not available. Please try again later.</p>';
                };
                
                videoElement.onloadeddata = function() {
                    console.log('[SHOWRESULTS] Video loaded successfully');
                };
                
                console.log('[SHOWRESULTS] Video src set to:', `/video/${jobId}`);
            } else {
                console.error('[SHOWRESULTS] Video element not found!');
            }
            
            // Fetch and display analysis data
            console.log('[SHOWRESULTS] Fetching analysis data from:', `/analysis/${jobId}`);
            fetch(`/analysis/${jobId}`)
            .then(response => {
                console.log('[SHOWRESULTS] Analysis response status:', response.status);
                console.log('[SHOWRESULTS] Analysis response headers:', response.headers);
                if (!response.ok) {
                    throw new Error(`Analysis fetch failed: ${response.status} ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                console.log('[SHOWRESULTS] Analysis data received:', data);
                console.log('[SHOWRESULTS] Data type:', typeof data);
                console.log('[SHOWRESULTS] Highlights count:', data.highlights ? data.highlights.length : 0);
                if (data.highlights && data.highlights.length > 0) {
                    console.log('[SHOWRESULTS] First highlight:', data.highlights[0]);
                    console.log('[SHOWRESULTS] First highlight fields:', Object.keys(data.highlights[0]));
                }
                
                // Display highlights
                displayHighlights(data.highlights || []);
                
                // Display drills
                displayDrills(data.recommended_drills || []);
                
                // Display YouTube recommendations
                displayYouTube(data.youtube_recommendations || []);
            })
            .catch(error => {
                console.error('[SHOWRESULTS] Error loading analysis:', error);
                console.error('[SHOWRESULTS] Error details:', error.message);
                // Show fallback content
                displayHighlights([{
                    timestamp: "00:00",
                    short_text: "Analysis completed",
                    long_text: "Your video has been processed successfully. Please review the video above.",
                    action_required: "Review your technique"
                }]);
                displayDrills([{
                    drill_name: "Basic Technique Review",
                    description: "Review your uploaded video to identify areas for improvement.",
                    problem_it_fixes: "General technique improvement"
                }]);
                // Add sample YouTube videos for demonstration
                displayYouTube([
                    {
                        title: "Keep your hands up ⚠️",
                        problem_solved: "Low Guard",
                        url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                    },
                    {
                        title: "How to Slip Punches like a PRO Boxer #shorts",
                        problem_solved: "Lack of Head Movement",
                        url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                    },
                    {
                        title: "How to Keep a Strong Boxing Stance with Footwork and Balance",
                        problem_solved: "Poor Footwork",
                        url: "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
                    }
                ]);
            });
        }
        
        // Display functions
        function displayHighlights(highlights) {
            console.log('[DISPLAY] Rendering highlights:', highlights);
            const container = document.getElementById('highlightsContainer');
            
            if (!container) {
                console.error('[DISPLAY] Highlights container not found!');
                return;
            }
            
            // Clear container and add title
            container.innerHTML = '<h2 class="section-title">Key Highlights</h2>';
            
            if (highlights && highlights.length > 0) {
                highlights.forEach((highlight, index) => {
                    console.log('[DISPLAY] Processing highlight:', highlight);
                    console.log('[DISPLAY] Highlight fields:', Object.keys(highlight));
                    
                    // Format timestamp properly
                    const timestamp = highlight.timestamp ? formatTimestamp(highlight.timestamp) : 'N/A';
                    
                    // Get the best available description
                    const description = highlight.long_text || highlight.short_text || highlight.detailed_feedback || 'No description available';
                    
                    // Get action required
                    const actionRequired = highlight.action_required || 'Continue practicing';
                    
                    const card = document.createElement('div');
                    card.className = 'highlight-card';
                    card.innerHTML = `
                        <div class="highlight-title">Highlight ${index + 1} - ${timestamp}</div>
                        <div class="highlight-description">${description}</div>
                        <div class="highlight-action">Action Required: ${actionRequired}</div>
                    `;
                    container.appendChild(card);
                });
                console.log('[DISPLAY] Rendered', highlights.length, 'highlight cards');
            } else {
                // Fallback message
                const fallbackCard = document.createElement('div');
                fallbackCard.className = 'highlight-card';
                fallbackCard.innerHTML = `
                    <div class="highlight-title">Analysis Complete</div>
                    <div class="highlight-description">Your video has been processed successfully. Review the video above to see your technique.</div>
                    <div class="highlight-action">Action Required: Continue practicing and improving your technique</div>
                `;
                container.appendChild(fallbackCard);
                console.log('[DISPLAY] Rendered fallback highlight card');
            }
        }

        function formatTimestamp(timestamp) {
            if (!timestamp) return 'N/A';
            
            // If it's already a formatted string like "00:03", return as is
            if (typeof timestamp === 'string' && timestamp.includes(':')) {
                return timestamp;
            }
            
            // If it's a number (seconds), convert to MM:SS format
            const seconds = parseInt(timestamp);
            if (!isNaN(seconds)) {
                const minutes = Math.floor(seconds / 60);
                const remainingSeconds = seconds % 60;
                return `(${minutes}:${remainingSeconds.toString().padStart(2, '0')})`;
            }
            
            return timestamp.toString();
        }

        function displayDrills(drills) {
            console.log('[DISPLAY] Rendering drills:', drills);
            const container = document.getElementById('drillsContainer');
            
            if (!container) {
                console.error('[DISPLAY] Drills container not found!');
                return;
            }
            
            // Clear container and add title
            container.innerHTML = '<h2 class="section-title">Recommended Drills</h2>';
            
            if (drills && drills.length > 0) {
                drills.forEach((drill, index) => {
                    console.log('[DISPLAY] Processing drill:', drill);
                    console.log('[DISPLAY] Drill fields:', Object.keys(drill));
                    
                    const card = document.createElement('div');
                    card.className = 'drill-card';
                    card.innerHTML = `
                        <div class="drill-name">${drill.drill_name || drill.name || 'Recommended Drill'}</div>
                        <div class="drill-description">${drill.description || drill.drill_description || 'No description available'}</div>
                        <div class="drill-problem">Fixes: ${drill.problem_it_fixes || drill.fixes || 'General technique improvement'}</div>
                    `;
                    container.appendChild(card);
                });
                console.log('[DISPLAY] Rendered', drills.length, 'drill cards');
            } else {
                // Fallback message
                const fallbackCard = document.createElement('div');
                fallbackCard.className = 'drill-card';
                fallbackCard.innerHTML = `
                    <div class="drill-name">Basic Technique Review</div>
                    <div class="drill-description">Review your uploaded video to identify areas for improvement. Focus on your stance, guard position, and movement.</div>
                    <div class="drill-problem">Fixes: General technique improvement and awareness</div>
                `;
                container.appendChild(fallbackCard);
                console.log('[DISPLAY] Rendered fallback drill card');
            }
        }

        function displayYouTube(videos) {
            console.log('[DISPLAY] Rendering YouTube videos:', videos);
            const container = document.getElementById('youtubeContainer');
            
            if (!container) {
                console.error('[DISPLAY] YouTube container not found!');
                return;
            }
            
            // Clear container and add title
            container.innerHTML = '<h2 class="section-title">YouTube Recommendations</h2>';
            
            if (videos && videos.length > 0) {
                videos.forEach((video, index) => {
                    console.log('[DISPLAY] Processing YouTube video:', video);
                    console.log('[DISPLAY] Video fields:', Object.keys(video));
                    
                    const card = document.createElement('div');
                    card.className = 'youtube-card';
                    
                    // Extract YouTube video ID from URL
                    const videoId = extractYouTubeId(video.url || video.youtube_url || video.link || '');
                    
                    if (videoId) {
                        // Create embedded iframe with better styling
                        card.innerHTML = `
                            <div class="youtube-title-text">${video.title || video.name || 'Boxing Tutorial'}</div>
                            <div class="youtube-problem">${video.problem_solved || video.problem_it_fixes || video.description || 'Learn boxing techniques'}</div>
                            <div class="youtube-embed">
                                <iframe 
                                    width="100%" 
                                    height="200" 
                                    src="https://www.youtube.com/embed/${videoId}?rel=0&modestbranding=1" 
                                    frameborder="0" 
                                    allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture" 
                                    allowfullscreen
                                    style="border-radius: 8px; box-shadow: 0 4px 15px rgba(0,0,0,0.3);">
                                </iframe>
                            </div>
                        `;
                    } else {
                        // Fallback to link if no valid video ID
                        card.innerHTML = `
                            <div class="youtube-title-text">${video.title || video.name || 'Boxing Tutorial'}</div>
                            <div class="youtube-problem">${video.problem_solved || video.problem_it_fixes || video.description || 'Learn boxing techniques'}</div>
                            <a href="${video.url || video.youtube_url || video.link || '#'}" target="_blank" class="youtube-link">Watch Video</a>
                        `;
                    }
                    
                    container.appendChild(card);
                });
                console.log('[DISPLAY] Rendered', videos.length, 'YouTube cards');
            } else {
                // Fallback message
                const fallbackCard = document.createElement('div');
                fallbackCard.className = 'youtube-card';
                fallbackCard.innerHTML = `
                    <div class="youtube-title-text">Boxing Technique Fundamentals</div>
                    <div class="youtube-problem">Learn the basics of proper boxing stance, guard, and movement</div>
                    <a href="https://www.youtube.com/results?search_query=boxing+technique+fundamentals" target="_blank" class="youtube-link">Search YouTube</a>
                `;
                container.appendChild(fallbackCard);
                console.log('[DISPLAY] Rendered fallback YouTube card');
            }
        }
        
        // Helper function to extract YouTube video ID
        function extractYouTubeId(url) {
            if (!url) return null;
            
            // Handle direct video IDs
            if (url.length === 11 && /^[a-zA-Z0-9_-]{11}$/.test(url)) {
                return url;
            }
            
            const patterns = [
                /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
                /youtube\.com\/v\/([^&\n?#]+)/,
                /youtube\.com\/watch\?.*v=([^&\n?#]+)/,
                /youtube\.com\/shorts\/([^&\n?#]+)/,
                /youtu\.be\/([^&\n?#]+)/
            ];
            
            for (const pattern of patterns) {
                const match = url.match(pattern);
                if (match && match[1] && match[1].length === 11) {
                    return match[1];
                }
            }
            
            return null;
        }
    </script>
</body>
</html> 
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="color-scheme" content="dark">
    <title>AI Fight Coach - Admin Dashboard</title>
    <link rel="stylesheet" href="style.css?v=2.1">
    
    <!-- Google tag (gtag.js) -->
    <script async src="https://www.googletagmanager.com/gtag/js?id=G-V4WX8SQ8LJ"></script>
    <script>
      window.dataLayer = window.dataLayer || [];
      function gtag(){dataLayer.push(arguments);}
      gtag('js', new Date());

      gtag('config', 'G-V4WX8SQ8LJ');
    </script>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
            color: white;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            padding: 20px;
            border-radius: 15px;
            margin-bottom: 30px;
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .header h1 {
            font-size: 2.5rem;
            font-weight: bold;
        }

        .header h2 {
            font-size: 1.2rem;
            opacity: 0.9;
        }

        .auth-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 30px;
            margin-bottom: 30px;
            text-align: center;
        }

        .auth-section h3 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #fca5a5;
        }

        .auth-input {
            width: 100%;
            max-width: 400px;
            padding: 15px;
            border: 2px solid #dc2626;
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.1);
            color: white;
            font-size: 1rem;
            margin-bottom: 20px;
        }

        .auth-input::placeholder {
            color: rgba(255, 255, 255, 0.7);
        }

        .auth-button {
            background: linear-gradient(135deg, #dc2626 0%, #991b1b 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 10px;
            font-size: 1.1rem;
            font-weight: bold;
            cursor: pointer;
            transition: transform 0.2s;
        }

        .auth-button:hover {
            transform: translateY(-2px);
        }

        .dashboard {
            display: none;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            text-align: center;
        }

        .stat-card h3 {
            font-size: 2rem;
            color: #fca5a5;
            margin-bottom: 10px;
        }

        .stat-card p {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        .section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .section h3 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #fca5a5;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-container {
            overflow-x: auto;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            margin-top: 15px;
        }

        th, td {
            padding: 12px;
            text-align: left;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);
        }

        th {
            background: rgba(220, 38, 38, 0.2);
            color: #fca5a5;
            font-weight: bold;
        }

        tr:hover {
            background: rgba(255, 255, 255, 0.05);
        }

        .error {
            background: rgba(220, 38, 38, 0.2);
            border: 1px solid #dc2626;
            color: #fca5a5;
            padding: 15px;
            border-radius: 10px;
            margin-top: 15px;
        }

        .loading {
            text-align: center;
            padding: 20px;
            color: #fca5a5;
        }

        .analytics-section {
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 30px;
        }

        .analytics-section h3 {
            font-size: 1.5rem;
            margin-bottom: 20px;
            color: #fca5a5;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .analytics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .analytics-card {
            background: rgba(255, 255, 255, 0.05);
            backdrop-filter: blur(5px);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            padding: 20px;
            text-align: center;
        }

        .analytics-card h4 {
            font-size: 1.2rem;
            margin-bottom: 10px;
            color: #fca5a5;
        }

        .stats-content {
            font-size: 1.1rem;
            opacity: 0.8;
        }

        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .header h1 {
                font-size: 2rem;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .table-container {
                font-size: 0.9rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <span style="font-size: 2rem;">🥊</span>
            <div>
                <h1>AI Fight Coach Admin</h1>
                <h2>Analytics Dashboard & User Management</h2>
            </div>
        </div>

        <!-- Authentication Section -->
        <div id="authSection" class="auth-section">
            <h3>🔐 Admin Authentication</h3>
            <input type="password" id="adminToken" class="auth-input" placeholder="Enter admin token">
            <button onclick="authenticate()" class="auth-button">Access Dashboard</button>
            <div id="authError" class="error" style="display: none;"></div>
        </div>

        <!-- Dashboard Section -->
        <div id="dashboard" class="dashboard">
            <!-- Statistics -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalUsers">-</h3>
                    <p>Total Users</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalJobs">-</h3>
                    <p>Total Jobs</p>
                </div>
                <div class="stat-card">
                    <h3 id="totalViews">-</h3>
                    <p>Total Views</p>
                </div>
            </div>

            <!-- User Analytics -->
            <div class="section">
                <h3>👥 User Analytics</h3>
                <div id="usersLoading" class="loading">Loading users...</div>
                <div id="usersError" class="error" style="display: none;"></div>
                <div class="table-container">
                    <table id="usersTable" style="display: none;">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Email</th>
                                <th>Name</th>
                                <th>Signup Date</th>
                                <th>Uploads</th>
                                <th>Jobs</th>
                                <th>Views</th>
                                <th>Completed</th>
                            </tr>
                        </thead>
                        <tbody id="usersTableBody"></tbody>
                    </table>
                </div>
            </div>

            <!-- Job Analytics -->
            <div class="section">
                <h3>🎬 Job Analytics</h3>
                <div id="jobsLoading" class="loading">Loading jobs...</div>
                <div id="jobsError" class="error" style="display: none;"></div>
                <div class="table-container">
                    <table id="jobsTable" style="display: none;">
                        <thead>
                            <tr>
                                <th>Job ID</th>
                                <th>User ID</th>
                                <th>Created</th>
                                <th>Status</th>
                                <th>Type</th>
                                <th>Views</th>
                                <th>Video URL</th>
                            </tr>
                        </thead>
                        <tbody id="jobsTableBody"></tbody>
                    </table>
                </div>
            </div>

            <!-- Page Analytics Section -->
            <div class="analytics-section">
                <h3>📊 Page Analytics</h3>
                <div class="analytics-grid">
                    <div class="analytics-card">
                        <h4>Page Views</h4>
                        <div id="pageViewsStats" class="stats-content">
                            <p>Loading...</p>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h4>Conversion Rates</h4>
                        <div id="conversionRates" class="stats-content">
                            <p>Loading...</p>
                        </div>
                    </div>
                    <div class="analytics-card">
                        <h4>Recent Activity (24h)</h4>
                        <div id="recentViews" class="stats-content">
                            <p>Loading...</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        let adminToken = '';

        function authenticate() {
            const token = document.getElementById('adminToken').value;
            if (!token) {
                showAuthError('Please enter admin token');
                return;
            }

            adminToken = token;
            document.getElementById('authSection').style.display = 'none';
            document.getElementById('dashboard').style.display = 'block';
            loadDashboard();
        }

        function showAuthError(message) {
            const errorDiv = document.getElementById('authError');
            errorDiv.textContent = message;
            errorDiv.style.display = 'block';
        }

        async function loadDashboard() {
            await Promise.all([
                loadStats(),
                loadUsers(),
                loadJobs(),
                loadPageAnalytics() // Load page analytics
            ]);
        }

        async function loadStats() {
            try {
                const response = await fetch('/api/admin/stats', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load statistics');
                }

                const stats = await response.json();
                document.getElementById('totalUsers').textContent = stats.total_users || 0;
                document.getElementById('totalJobs').textContent = stats.total_jobs || 0;
                document.getElementById('totalViews').textContent = stats.total_views || 0;
            } catch (error) {
                console.error('Stats error:', error);
                document.getElementById('totalUsers').textContent = 'Error';
                document.getElementById('totalJobs').textContent = 'Error';
                document.getElementById('totalViews').textContent = 'Error';
            }
        }

        async function loadUsers() {
            try {
                const response = await fetch('/api/admin/users', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load users');
                }

                const users = await response.json();
                displayUsers(users);
            } catch (error) {
                console.error('Users error:', error);
                document.getElementById('usersLoading').style.display = 'none';
                document.getElementById('usersError').textContent = 'Failed to load users';
                document.getElementById('usersError').style.display = 'block';
            }
        }

        async function loadJobs() {
            try {
                const response = await fetch('/api/admin/jobs', {
                    headers: {
                        'Authorization': `Bearer ${adminToken}`
                    }
                });

                if (!response.ok) {
                    throw new Error('Failed to load jobs');
                }

                const jobs = await response.json();
                displayJobs(jobs);
            } catch (error) {
                console.error('Jobs error:', error);
                document.getElementById('jobsLoading').style.display = 'none';
                document.getElementById('jobsError').textContent = 'Failed to load jobs';
                document.getElementById('jobsError').style.display = 'block';
            }
        }

        // Load page analytics
        async function loadPageAnalytics() {
            try {
                const response = await fetch('/api/admin/page-analytics', {
                    headers: {
                        'Authorization': adminToken
                    }
                });
                
                if (!response.ok) {
                    throw new Error('Failed to load page analytics');
                }
                
                const data = await response.json();
                
                // Display page views stats
                const pageViewsStats = document.getElementById('pageViewsStats');
                if (data.page_stats && data.page_stats.length > 0) {
                    let pageViewsHtml = '';
                    data.page_stats.forEach(page => {
                        pageViewsHtml += `
                            <div style="margin-bottom: 10px; padding: 8px; background: rgba(255,255,255,0.1); border-radius: 5px;">
                                <strong>${page.page_name}</strong><br>
                                Views: ${page.views} | Users: ${page.unique_users} | IPs: ${page.unique_ips}
                            </div>
                        `;
                    });
                    pageViewsStats.innerHTML = pageViewsHtml;
                } else {
                    pageViewsStats.innerHTML = '<p>No page views recorded yet</p>';
                }
                
                // Display conversion rates
                const conversionRates = document.getElementById('conversionRates');
                if (data.conversion_rates) {
                    const rates = data.conversion_rates;
                    conversionRates.innerHTML = `
                        <div style="margin-bottom: 10px;">
                            <strong>Total Views:</strong> ${rates.total_views}
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>Total Signups:</strong> ${rates.total_signups}
                        </div>
                        <div style="margin-bottom: 10px;">
                            <strong>Total Uploads:</strong> ${rates.total_uploads}
                        </div>
                        <div style="margin-bottom: 10px; color: #fca5a5;">
                            <strong>Signup Rate:</strong> ${rates.signup_rate}%
                        </div>
                        <div style="margin-bottom: 10px; color: #fca5a5;">
                            <strong>Upload Rate:</strong> ${rates.upload_rate}%
                        </div>
                    `;
                } else {
                    conversionRates.innerHTML = '<p>No conversion data available</p>';
                }
                
                // Display recent views
                const recentViews = document.getElementById('recentViews');
                if (data.recent_views && data.recent_views.length > 0) {
                    let recentHtml = '';
                    data.recent_views.forEach(page => {
                        recentHtml += `
                            <div style="margin-bottom: 8px; padding: 5px; background: rgba(255,255,255,0.1); border-radius: 3px;">
                                <strong>${page.page_name}</strong>: ${page.views} views
                            </div>
                        `;
                    });
                    recentViews.innerHTML = recentHtml;
                } else {
                    recentViews.innerHTML = '<p>No recent activity</p>';
                }
                
            } catch (error) {
                console.error('Error loading page analytics:', error);
                document.getElementById('pageViewsStats').innerHTML = '<p>Failed to load page analytics</p>';
                document.getElementById('conversionRates').innerHTML = '<p>Failed to load conversion rates</p>';
                document.getElementById('recentViews').innerHTML = '<p>Failed to load recent views</p>';
            }
        }

        function displayUsers(users) {
            const table = document.getElementById('usersTable');
            const tbody = document.getElementById('usersTableBody');
            const loading = document.getElementById('usersLoading');

            loading.style.display = 'none';
            table.style.display = 'table';

            tbody.innerHTML = '';
            users.forEach(user => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${user.id}</td>
                    <td>${user.email}</td>
                    <td>${user.name || 'Anonymous'}</td>
                    <td>${new Date(user.signup_ts).toLocaleDateString()}</td>
                    <td>${user.upload_count}</td>
                    <td>${user.total_jobs}</td>
                    <td>${user.total_views}</td>
                    <td>${user.completed_jobs}</td>
                `;
                tbody.appendChild(row);
            });
        }

        function displayJobs(jobs) {
            const table = document.getElementById('jobsTable');
            const tbody = document.getElementById('jobsTableBody');
            const loading = document.getElementById('jobsLoading');

            loading.style.display = 'none';
            table.style.display = 'table';

            tbody.innerHTML = '';
            jobs.forEach(job => {
                const row = document.createElement('tr');
                row.innerHTML = `
                    <td>${job.id}</td>
                    <td>${job.user_id}</td>
                    <td>${new Date(job.created_ts).toLocaleDateString()}</td>
                    <td>${job.status}</td>
                    <td>${job.analysis_type || 'general'}</td>
                    <td>${job.view_count}</td>
                    <td>${job.video_url || 'N/A'}</td>
                `;
                tbody.appendChild(row);
            });
        }
    </script>
</body>
</html> 
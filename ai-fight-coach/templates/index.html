<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI Fight Coach</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .upload-area { border: 2px dashed #ccc; padding: 40px; text-align: center; margin: 20px 0; }
        .progress { margin: 20px 0; }
        .result { margin: 20px 0; padding: 20px; background: #f0f0f0; }
    </style>
</head>
<body>
    <h1>🥊 AI Fight Coach</h1>
    <p>Upload your boxing video for AI-powered analysis</p>
    
    <form id="uploadForm" enctype="multipart/form-data">
        <div class="upload-area">
            <input type="file" name="video" accept="video/*" required>
            <p>Select a video file (MP4, AVI, MOV, MKV)</p>
        </div>
        
        <div>
            <label>Custom Prompt (optional):</label>
            <textarea name="custom_prompt" rows="3" placeholder="Enter custom analysis instructions..."></textarea>
        </div>
        
        <button type="submit">Upload & Analyze</button>
    </form>
    
    <div id="progress" class="progress" style="display: none;">
        <h3>Processing...</h3>
        <p id="status">Starting analysis...</p>
        <progress id="progressBar" value="0" max="100"></progress>
    </div>
    
    <div id="result" class="result" style="display: none;">
        <h3>Analysis Complete!</h3>
        <video id="resultVideo" controls style="width: 100%; max-width: 600px;"></video>
        <br>
        <a id="downloadLink" href="#" download>Download Result</a>
    </div>
    
    <script>
        document.getElementById('uploadForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            
            const formData = new FormData();
            const videoFile = document.querySelector('input[name="video"]').files[0];
            const customPrompt = document.querySelector('textarea[name="custom_prompt"]').value;
            
            formData.append('video', videoFile);
            if (customPrompt) formData.append('custom_prompt', customPrompt);
            
            document.getElementById('progress').style.display = 'block';
            
            try {
                const response = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                
                const data = await response.json();
                const jobId = data.job_id;
                
                // Poll for status
                const checkStatus = async () => {
                    const statusResponse = await fetch(`/status/${jobId}`);
                    const statusData = await statusResponse.json();
                    
                    document.getElementById('status').textContent = `Status: ${statusData.status} (${statusData.progress}%)`;
                    document.getElementById('progressBar').value = statusData.progress;
                    
                    if (statusData.status === 'completed') {
                        document.getElementById('progress').style.display = 'none';
                        document.getElementById('result').style.display = 'block';
                        
                        const video = document.getElementById('resultVideo');
                        video.src = statusData.result_url;
                        
                        document.getElementById('downloadLink').href = statusData.result_url;
                    } else if (statusData.status === 'failed') {
                        alert('Processing failed: ' + statusData.error);
                        document.getElementById('progress').style.display = 'none';
                    } else {
                        setTimeout(checkStatus, 2000);
                    }
                };
                
                checkStatus();
                
            } catch (error) {
                alert('Upload failed: ' + error.message);
                document.getElementById('progress').style.display = 'none';
            }
        });
    </script>
</body>
</html>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>Combat Mirror - Camera Stream</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            overflow: hidden;
            height: 100vh;
            background: #000;
        }
        #video-container {
            position: relative;
            width: 100%;
            height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        video {
            width: 100%;
            height: 100%;
            object-fit: contain;
            background: #000;
        }
        .controls {
            position: absolute;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 10px;
            z-index: 10;
        }
        .status {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            background: rgba(0,0,0,0.5);
            padding: 10px 20px;
            border-radius: 25px;
            font-family: system-ui;
            z-index: 10;
        }
    </style>
</head>
<body>
    <div id="video-container">
        <div class="status" id="status">Initializing...</div>
        <video id="localVideo" autoplay playsinline muted></video>
        <div class="controls">
            <button id="toggleCamera" class="bg-blue-500 hover:bg-blue-600 text-white px-6 py-3 rounded-full font-medium shadow-lg">
                Switch Camera
            </button>
            <button id="disconnect" class="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-full font-medium shadow-lg">
                Disconnect
            </button>
        </div>
    </div>

    <script type="module">
        import { Room, createLocalVideoTrack, VideoPresets, LocalVideoTrack } from 'https://unpkg.com/livekit-client@2.0.0/dist/livekit-client.esm.mjs';

        let room;
        let localVideoTrack;
        let facingMode = 'environment'; // Start with back camera

        // Get URL parameters
        const params = new URLSearchParams(window.location.search);
        const roomName = params.get('room');
        const token = params.get('token');
        const position = params.get('position');

        const statusEl = document.getElementById('status');
        const localVideoEl = document.getElementById('localVideo');
        const toggleCameraBtn = document.getElementById('toggleCamera');
        const disconnectBtn = document.getElementById('disconnect');

        async function connectToRoom() {
            try {
                // Check if we're on HTTPS or localhost
                if (location.protocol !== 'https:' && location.hostname !== 'localhost' && location.hostname !== '127.0.0.1') {
                    statusEl.textContent = 'Camera access requires HTTPS on mobile devices';
                    statusEl.style.background = 'rgba(255,165,0,0.5)';
                    
                    // Show instructions for HTTPS
                    setTimeout(() => {
                        statusEl.innerHTML = `
                            <div style="font-size: 14px;">
                                <div>📱 Mobile Camera Access Issue</div>
                                <div style="font-size: 12px; margin-top: 5px;">
                                    Please use HTTPS or localhost for camera access
                                </div>
                            </div>
                        `;
                    }, 2000);
                    return;
                }

                // Check if getUserMedia is available
                if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                    statusEl.textContent = 'Camera access not supported in this browser';
                    statusEl.style.background = 'rgba(255,0,0,0.5)';
                    return;
                }
                
                statusEl.textContent = 'Requesting camera access...';
                
                // Try to get camera permissions first
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        video: {
                            facingMode: facingMode,
                            width: { ideal: 1280 },
                            height: { ideal: 720 }
                        }
                    });
                    
                    // Stop the test stream
                    stream.getTracks().forEach(track => track.stop());
                } catch (mediaError) {
                    console.error('Media access error:', mediaError);
                    statusEl.textContent = 'Camera permission denied or not available';
                    statusEl.style.background = 'rgba(255,0,0,0.5)';
                    return;
                }
                
                // Create video track using LiveKit
                localVideoTrack = await createLocalVideoTrack({
                    facingMode: facingMode,
                    resolution: VideoPresets.h720.resolution,
                });

                // Attach to video element
                localVideoTrack.attach(localVideoEl);

                statusEl.textContent = 'Connecting to room...';

                // Connect to LiveKit room
                room = new Room({
                    adaptiveStream: true,
                    dynacast: true,
                });

                // Use the actual server IP - update this based on your network
                const serverUrl = window.location.hostname === 'localhost' 
                    ? 'ws://localhost:7880' 
                    : `ws://${window.location.hostname}:7880`;
                    
                console.log('Connecting to:', serverUrl);
                await room.connect(serverUrl, token);
                
                // Publish video track
                await room.localParticipant.publishTrack(localVideoTrack);

                statusEl.textContent = `Connected: ${position} Camera`;
                statusEl.style.background = 'rgba(0,128,0,0.5)';

            } catch (error) {
                console.error('Connection error:', error);
                statusEl.textContent = 'Error: ' + error.message;
                statusEl.style.background = 'rgba(255,0,0,0.5)';
                
                // Show more detailed error for debugging
                setTimeout(() => {
                    statusEl.innerHTML = `
                        <div style="font-size: 12px;">
                            <div>❌ Connection Failed</div>
                            <div style="margin-top: 5px;">
                                ${error.message}
                            </div>
                        </div>
                    `;
                }, 3000);
            }
        }

        // Toggle camera
        toggleCameraBtn.addEventListener('click', async () => {
            try {
                facingMode = facingMode === 'environment' ? 'user' : 'environment';
                
                // Stop current track
                if (localVideoTrack) {
                    localVideoTrack.stop();
                    await room.localParticipant.unpublishTrack(localVideoTrack);
                }

                // Create new track with different camera
                localVideoTrack = await createLocalVideoTrack({
                    facingMode: facingMode,
                    resolution: VideoPresets.h720.resolution,
                });

                // Attach and publish new track
                localVideoTrack.attach(localVideoEl);
                await room.localParticipant.publishTrack(localVideoTrack);

            } catch (error) {
                console.error('Camera switch error:', error);
            }
        });

        // Disconnect
        disconnectBtn.addEventListener('click', () => {
            if (room) {
                room.disconnect();
            }
            window.close();
        });

        // Initialize connection
        if (roomName && token) {
            connectToRoom();
        } else {
            statusEl.textContent = 'Invalid connection parameters';
            statusEl.style.background = 'rgba(255,0,0,0.5)';
        }

        // Prevent screen sleep
        if ('wakeLock' in navigator) {
            navigator.wakeLock.request('screen').catch(console.error);
        }
    </script>
</body>
</html>
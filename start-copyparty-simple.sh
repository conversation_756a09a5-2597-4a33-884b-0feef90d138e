#!/bin/bash

echo "🗂️  Starting Copyparty File Server"
echo "=================================="

# Create directories
mkdir -p uploads recordings exports copyparty-db

# Start Copyparty with simple configuration
python3 copyparty.py \
    -i 0.0.0.0 -p 3923 \
    --rw ./uploads::uploads \
    --rw ./recordings::recordings \
    --rw ./exports::exports \
    --cors \
    --theme 2

# If the above fails, try even simpler
if [ $? -ne 0 ]; then
    echo "Trying simpler configuration..."
    python3 copyparty.py -i 0.0.0.0 -p 3923 --rw .
fi
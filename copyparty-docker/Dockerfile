# Custom Copyparty Docker image for Combat Mirror System
FROM ghcr.io/9001/copyparty:latest

# Set working directory
WORKDIR /srv

# Create required directories
RUN mkdir -p /srv/uploads /srv/recordings /srv/exports /srv/.hist /srv/config /srv/.th

# Copy custom configuration if needed
# COPY copyparty.conf /srv/config/

# Set permissions
RUN chmod -R 755 /srv

# Expose ports
EXPOSE 3923 3990 1033

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 \
  CMD curl -f http://localhost:3923/ || exit 1

# Default command with all features enabled
CMD ["-i", "::", "-p", "3923", \
     "--rw", "/srv/uploads,/srv/recordings,/srv/exports", \
     "--cors", \
     "--theme", "2", \
     "--name", "Combat Mirror File Server", \
     "--no-robots", \
     "--ed", \
     "--em", \
     "--et", \
     "--e2dsa", \
     "--e2ts", \
     "--s", \
     "--th-poke", \
     "--no-thumb-svg", \
     "--j-part-sz", "32", \
     "--j-threads", "4", \
     "--log-req", \
     "--dbpath", "/srv/.hist"]
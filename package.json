{"name": "combat-mirror-system", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "test": "jest", "test:watch": "jest --watch", "start": "npm run dev", "start:https": "HTTPS=true npm run dev", "start:production": "npm run build && npm run preview", "start:livekit": "concurrently \"npm run dev\" \"npm run livekit:server\"", "livekit:server": "livekit-server --config livekit-config.yaml", "livekit:agent": "cd agent && python combat_mirror_agent.py", "calibration:detect": "node scripts/calibration/device-detector.js", "calibration:qr": "node scripts/calibration/qr-generator.js --generate-all", "calibration:web": "node scripts/calibration/qr-generator.js --web-interface", "calibration:full": "npm run calibration:detect && npm run calibration:qr && npm run calibration:web", "copyparty:start": "./start-copyparty.sh", "copyparty:simple": "./start-copyparty-simple.sh", "docker:build": "docker build -t combat-mirror .", "docker:run": "docker run -p 5173:5173 combat-mirror", "docker:dev": "docker-compose up --build", "docker:production": "docker-compose -f docker-compose.yml up --build", "railway:deploy": "railway up", "railway:dev": "railway run npm run dev", "setup": "npm install && npm run calibration:full", "network:setup": "./scripts/network/setup-network-hub.sh", "network:start": "./scripts/network/start-network.sh", "network:stop": "./scripts/network/stop-network.sh", "network:status": "./scripts/network/monitor-network.sh", "network:reset": "./scripts/network/reset-network.sh", "network:firewall": "./scripts/network/firewall-rules.sh", "network:monitor": "./scripts/network/monitor-network.sh realtime", "network:report": "./scripts/network/monitor-network.sh report"}, "dependencies": {"@livekit/components-react": "^2.0.0", "@livekit/components-styles": "^1.0.0", "@mediapipe/camera_utils": "^0.3.1640029074.3", "@mediapipe/drawing_utils": "^0.3.1620248257.0", "@mediapipe/pose": "^0.5.1635988162.3", "@tensorflow/tfjs": "^4.10.0", "@tensorflow/tfjs-backend-webgl": "^4.10.0", "@tensorflow/tfjs-converter": "^4.10.0", "@tensorflow/tfjs-core": "^4.10.0", "@types/qrcode": "^1.5.2", "axios": "^1.5.0", "clsx": "^2.0.0", "lucide-react": "^0.263.1", "qrcode": "^1.5.3", "react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.15.0", "socket.io-client": "^4.7.2", "tailwind-merge": "^1.14.0", "three": "^0.155.0"}, "devDependencies": {"@types/react": "^18.2.15", "@types/react-dom": "^18.2.7", "@types/three": "^0.155.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.3", "autoprefixer": "^10.4.14", "concurrently": "^8.2.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.3", "ios-deploy": "^1.12.2", "jest": "^29.6.2", "postcss": "^8.4.27", "tailwindcss": "^3.3.3", "typescript": "^5.0.2", "vite": "^4.4.5"}}
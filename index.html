
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Combat Mirror System</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
      tailwind.config = {
        theme: {
          extend: {
            colors: {
              'brand-primary': '#00A9FF',
              'brand-secondary': '#89CFF3',
              'brand-accent': '#A0E9FF',
              'brand-light': '#CDF5FD',
              'neutral-900': '#121212',
              'neutral-800': '#1E1E1E',
              'neutral-700': '#2d2d2d',
              'neutral-650': '#343434',
              'neutral-600': '#3c3c3c',
              'neutral-500': '#6b6b6b',
              'neutral-400': '#9b9b9b',
              'neutral-200': '#E0E0E0',
              'neutral-100': '#F5F5F5',
            }
          }
        }
      }
    </script>
  <script type="importmap">
{
  "imports": {
    "@google/genai": "https://esm.sh/@google/genai@^1.7.0",
    "react/": "https://esm.sh/react@^19.1.0/",
    "react": "https://esm.sh/react@^19.1.0",
    "react-dom/": "https://esm.sh/react-dom@^19.1.0/"
  }
}
</script>
</head>
  <body class="bg-neutral-900 text-neutral-100">
    <div id="root"></div>
  <script type="module" src="/index.tsx"></script>
</body>
</html>

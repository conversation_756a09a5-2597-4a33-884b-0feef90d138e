# Combat Mirror System - Local Caddy Configuration
# This file is used when running with docker-compose profile=production

localhost {
    # Frontend
    handle_path /* {
        reverse_proxy frontend:3000
    }
}

localhost:8080 {
    # Backend API
    handle_path /api/* {
        reverse_proxy backend:3001
    }
    
    # Health check endpoint
    handle_path /health {
        reverse_proxy backend:3001
    }
}

localhost:7880 {
    # LiveKit WebSocket
    reverse_proxy livekit:7880 {
        header_up Host {host}
        header_up X-Real-IP {remote}
        header_up X-Forwarded-For {remote}
        header_up X-Forwarded-Proto {scheme}
    }
}
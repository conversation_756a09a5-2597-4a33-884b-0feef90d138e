#!/bin/bash

echo "Starting Combat Mirror System with LiveKit..."

# Check if <PERSON><PERSON> is running
if ! docker info > /dev/null 2>&1; then
    echo "Docker is not running. Please start Docker Desktop first."
    exit 1
fi

# Start LiveKit in Docker
echo "Starting LiveKit server..."
docker-compose up -d

# Wait for LiveKit to be ready
echo "Waiting for LiveKit to be ready..."
sleep 5

# Check if LiveKit is running
if docker ps | grep -q combat-mirror-livekit; then
    echo "LiveKit server is running on ws://localhost:7880"
else
    echo "Failed to start LiveKit server"
    exit 1
fi

# Start Vite dev server
echo "Starting Combat Mirror UI..."
npm run dev
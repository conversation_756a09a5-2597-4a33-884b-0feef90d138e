# CopyParty Configuration for Combat Mirror System

[global]
  e2dsa    # enable file deduplication
  e2ts     # enable file indexing
  ed       # enable markdown editor
  em       # enable media indexing
  et       # enable thumbnails
  j        # enable uploads
  cors     # enable CORS for API access
  s        # searchable
  
  # Authentication (optional - uncomment to enable)
  # -a user:password
  
  # Logging
  log-req  # log all requests
  
  # Performance
  j-part-sz 32  # 32MB upload chunks
  j-threads 4   # 4 upload threads
  
  # Media handling
  th-poke       # generate thumbnails on upload
  no-thumb-svg  # don't thumbnail SVGs
  
[/uploads]
  # Combat Mirror video uploads
  accs:
    rw    # read/write access
  flags:
    dupe  # allow duplicates
    move  # allow moving files
  
[/recordings]
  # LiveKit recordings
  accs:
    rw    # read/write access
  flags:
    dupe  # allow duplicates
    ts    # enable timestamp in filenames
    
[/exports]
  # Exported analysis reports
  accs:
    rw    # read/write access
  flags:
    dupe  # allow duplicates
    move  # allow moving files
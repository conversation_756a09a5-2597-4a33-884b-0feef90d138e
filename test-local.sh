#!/bin/bash

# Combat Mirror System - Local Deployment Test Script
set -e

echo "🧪 Testing Combat Mirror System Local Deployment"
echo "=============================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# Test counter
TESTS_PASSED=0
TESTS_FAILED=0

# Function to run a test
run_test() {
    local test_name=$1
    local test_command=$2
    
    echo -n "Testing $test_name... "
    if eval $test_command > /dev/null 2>&1; then
        echo -e "${GREEN}PASSED${NC}"
        ((TESTS_PASSED++))
    else
        echo -e "${RED}FAILED${NC}"
        ((TESTS_FAILED++))
    fi
}

# Wait for services to be ready
echo -e "${YELLOW}Waiting for services to be fully ready...${NC}"
sleep 10

# Service Health Checks
echo -e "\n${YELLOW}1. Service Health Checks${NC}"
echo "------------------------"
run_test "Redis connectivity" "docker-compose exec -T redis redis-cli ping"
run_test "Backend API health" "curl -f http://localhost:3001/api/health"
run_test "Frontend accessibility" "curl -f http://localhost:3000"
run_test "LiveKit WebSocket" "curl -f http://localhost:7880/"

# API Endpoint Tests
echo -e "\n${YELLOW}2. API Endpoint Tests${NC}"
echo "---------------------"
run_test "CORS headers" "curl -I http://localhost:3001/api/health | grep -i 'access-control-allow-origin'"
run_test "Analysis endpoint" "curl -X POST http://localhost:3001/api/analyze -H 'Content-Type: application/json' -d '{\"test\":true}' -w '%{http_code}' -o /dev/null -s | grep -E '200|400'"

# WebSocket Tests
echo -e "\n${YELLOW}3. WebSocket Tests${NC}"
echo "------------------"
run_test "LiveKit WebSocket upgrade" "curl -i -N -H 'Upgrade: websocket' -H 'Connection: Upgrade' http://localhost:7880/ 2>&1 | grep -E '426|101'"

# Container Health
echo -e "\n${YELLOW}4. Container Health${NC}"
echo "-------------------"
run_test "All containers running" "docker-compose ps | grep -c 'Up' | grep -q 4"
run_test "No container restarts" "docker-compose ps | grep -v 'Restarting' > /dev/null"

# Resource Checks
echo -e "\n${YELLOW}5. Resource Checks${NC}"
echo "------------------"
run_test "Volume mounts exist" "docker-compose exec -T backend ls /app/uploads > /dev/null"
run_test "Log directory exists" "docker-compose exec -T backend ls /app/logs > /dev/null"

# Network Connectivity
echo -e "\n${YELLOW}6. Network Connectivity${NC}"
echo "-----------------------"
run_test "Backend can reach Redis" "docker-compose exec -T backend nc -zv redis 6379"
run_test "Frontend can reach Backend" "docker-compose exec -T frontend wget -q -O - http://backend:3001/api/health > /dev/null"

# Performance Checks
echo -e "\n${YELLOW}7. Performance Checks${NC}"
echo "---------------------"
RESPONSE_TIME=$(curl -o /dev/null -s -w '%{time_total}' http://localhost:3001/api/health)
if (( $(echo "$RESPONSE_TIME < 1.0" | bc -l) )); then
    echo -e "API response time: ${GREEN}${RESPONSE_TIME}s${NC} ✓"
    ((TESTS_PASSED++))
else
    echo -e "API response time: ${RED}${RESPONSE_TIME}s${NC} (slow)"
    ((TESTS_FAILED++))
fi

# Summary
echo -e "\n${YELLOW}Test Summary${NC}"
echo "============"
echo -e "Tests passed: ${GREEN}$TESTS_PASSED${NC}"
echo -e "Tests failed: ${RED}$TESTS_FAILED${NC}"

if [ $TESTS_FAILED -eq 0 ]; then
    echo -e "\n${GREEN}✅ All tests passed! Your local deployment is ready.${NC}"
    echo -e "\nYou can now:"
    echo "  1. Open http://localhost:3000 in your browser"
    echo "  2. Test mobile camera at http://[YOUR_IP]:3000/camera-stream.html"
    echo "  3. Monitor logs with: docker-compose logs -f"
else
    echo -e "\n${RED}❌ Some tests failed. Please check the logs:${NC}"
    echo "  docker-compose logs [service_name]"
    echo -e "\n${YELLOW}Common issues:${NC}"
    echo "  - Services may need more time to start"
    echo "  - Check if ports 3000, 3001, 7880, 6379 are available"
    echo "  - Ensure Docker has enough resources allocated"
fi
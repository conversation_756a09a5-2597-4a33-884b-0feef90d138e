#!/bin/bash

# Combat Mirror System - Local Development Startup Script
set -e

echo "🥊 Combat Mirror System - Local Development"
echo "=========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Get local IP address
LOCAL_IP=$(ipconfig getifaddr en0 || ipconfig getifaddr en1 || echo "************")
echo "📍 Using IP address: $LOCAL_IP"

# Check prerequisites
echo -e "\n${YELLOW}Checking prerequisites...${NC}"

# Check Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker not found. Please install Docker Desktop${NC}"
    exit 1
fi

# Check Docker Compose
if ! command -v docker-compose &> /dev/null; then
    echo -e "${RED}❌ Docker Compose not found. Please install Docker Desktop${NC}"
    exit 1
fi

# Check if Docker is running
if ! docker info &> /dev/null; then
    echo -e "${RED}❌ Docker is not running. Please start Docker Desktop${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Prerequisites checked${NC}"

# Check for .env.local file
if [ ! -f .env.local ]; then
    echo -e "\n${YELLOW}Creating .env.local from template...${NC}"
    cp .env.local.example .env.local
    echo -e "${YELLOW}⚠️  Please edit .env.local and add your GEMINI_API_KEY${NC}"
    echo "   Then run this script again."
    exit 1
fi

# Source environment variables
export $(cat .env.local | grep -v '^#' | xargs)

# Check for GEMINI_API_KEY
if [ -z "$GEMINI_API_KEY" ] || [ "$GEMINI_API_KEY" = "your_gemini_api_key_here" ]; then
    echo -e "${RED}❌ GEMINI_API_KEY not set in .env.local${NC}"
    echo "   Please add your Gemini API key and try again."
    exit 1
fi

# Update environment variables with actual IP
echo -e "\n${YELLOW}Updating configuration with local IP...${NC}"
sed -i.bak "s/************/$LOCAL_IP/g" docker-compose.yml
sed -i.bak "s/************/$LOCAL_IP/g" .env.local

# Create necessary directories
echo -e "\n${YELLOW}Creating directories...${NC}"
mkdir -p uploads logs livekit-data recordings

# Stop any existing containers
echo -e "\n${YELLOW}Stopping existing containers...${NC}"
docker-compose down 2>/dev/null || true

# Pull latest images
echo -e "\n${YELLOW}Pulling latest Docker images...${NC}"
docker-compose pull

# Build containers
echo -e "\n${YELLOW}Building containers...${NC}"
docker-compose build

# Start services
echo -e "\n${YELLOW}Starting services...${NC}"
docker-compose up -d

# Wait for services to be ready
echo -e "\n${YELLOW}Waiting for services to start...${NC}"
sleep 5

# Health checks
echo -e "\n${YELLOW}Running health checks...${NC}"

# Check Redis
if docker-compose exec -T redis redis-cli ping > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Redis: Running${NC}"
else
    echo -e "${RED}❌ Redis: Not responding${NC}"
fi

# Check Backend
if curl -f http://localhost:3001/api/health > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Backend API: Running${NC}"
else
    echo -e "${RED}❌ Backend API: Not responding${NC}"
fi

# Check LiveKit
if curl -f http://localhost:7880/ > /dev/null 2>&1; then
    echo -e "${GREEN}✅ LiveKit: Running${NC}"
else
    echo -e "${RED}❌ LiveKit: Not responding${NC}"
fi

# Check Frontend
if curl -f http://localhost:3000 > /dev/null 2>&1; then
    echo -e "${GREEN}✅ Frontend: Running${NC}"
else
    echo -e "${RED}❌ Frontend: Not responding (may still be building)${NC}"
fi

# Display service URLs
echo -e "\n${GREEN}🎉 Combat Mirror System is running!${NC}"
echo "====================================="
echo -e "Frontend:    ${GREEN}http://localhost:3000${NC}"
echo -e "Backend API: ${GREEN}http://localhost:3001${NC}"
echo -e "LiveKit:     ${GREEN}ws://localhost:7880${NC}"
echo -e "Redis:       ${GREEN}localhost:6379${NC}"
echo ""
echo -e "Mobile Camera: ${GREEN}http://$LOCAL_IP:3000/camera-stream.html${NC}"
echo ""
echo -e "${YELLOW}Useful commands:${NC}"
echo "  View logs:        docker-compose logs -f [service]"
echo "  Stop all:         docker-compose down"
echo "  Restart service:  docker-compose restart [service]"
echo "  View processes:   docker-compose ps"
echo ""
echo -e "${YELLOW}Services: frontend, backend, livekit, redis${NC}"
echo ""
echo -e "${GREEN}Ready for testing! Open http://localhost:3000${NC}"
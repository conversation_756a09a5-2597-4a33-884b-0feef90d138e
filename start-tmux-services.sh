#\!/bin/bash

# Combat Mirror System - Tmux Service Manager
SESSION="combat-mirror"

echo "Starting Combat Mirror services in tmux session: $SESSION"

# Window 0: Backend (port 5557)
tmux rename-window -t $SESSION:0 'backend:5557'
tmux send-keys -t $SESSION:0 'cd ~/Desktop/combat-mirror-system/server && node index.js' Enter

# Window 1: Copyparty (port 5923)
tmux new-window -t $SESSION:1 -n 'copyparty:5923'
tmux send-keys -t $SESSION:1 'cd ~/Desktop/combat-mirror-system && python -m http.server 5923' Enter

# Window 2: Frontend (port 3006)
tmux new-window -t $SESSION:2 -n 'frontend:3006'
tmux send-keys -t $SESSION:2 'cd ~/Desktop/combat-mirror-system && npm run dev' Enter

# Window 3: Monitoring
tmux new-window -t $SESSION:3 -n 'monitor'
tmux send-keys -t $SESSION:3 'watch -n 2 "echo \"=== Combat Mirror System Status ===\"; echo; echo \"Backend (5557):\"; curl -s http://localhost:5557/api/health | jq .; echo; echo \"Copyparty (5923):\"; curl -sI http://localhost:5923 | head -1; echo; echo \"Frontend (3006):\"; lsof -i :3006 | head -2; echo; echo \"=== Process Status ===\"; ps aux | grep -E \"(node.*index|python.*5923|vite)\" | grep -v grep | awk \"{print \\$11, \\$12}\""' Enter

# Split monitoring window to show logs
tmux split-window -h -t $SESSION:3
tmux send-keys -t $SESSION:3.1 'tail -f ~/Desktop/combat-mirror-system/server/*.log 2>/dev/null || echo "Waiting for logs..."' Enter

echo "Services starting in tmux session: $SESSION"
echo ""
echo "To attach and monitor: tmux attach -t $SESSION"
echo "To switch windows: Ctrl+B then window number (0-3)"
echo "To detach: Ctrl+B then D"
echo ""
echo "Windows:"
echo "  0: Backend API (port 5557)"
echo "  1: Copyparty Server (port 5923)"
echo "  2: Frontend Dev Server (port 3006)"
echo "  3: System Monitor & Logs"

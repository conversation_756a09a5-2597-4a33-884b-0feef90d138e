# Combat Mirror System - Production Environment Variables
# IMPORTANT: Never commit actual values to git

# Backend API Configuration
VITE_API_URL=https://combat-mirror-api.railway.app
VITE_COPYPARTY_URL=http://localhost:3923
NODE_ENV=production

# Security
GEMINI_API_KEY=${GEMINI_API_KEY}
CORS_ORIGIN=https://combat-mirror.railway.app

# LiveKit Configuration
VITE_LIVEKIT_URL=wss://combat-mirror-livekit.railway.app
VITE_LIVEKIT_API_KEY=APIfightmaster
LIVEKIT_API_SECRET=${LIVEKIT_API_SECRET}

# Production Settings
LOG_LEVEL=info
DEBUG=false
ENABLE_PROFILING=false
ENABLE_TRACING=true
TRACE_SAMPLE_RATE=0.1

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_VIDEO_SIZE_MB=50
MAX_SESSION_DURATION_MINUTES=30

# Security Headers
ENABLE_HELMET=true
ENABLE_CORS=true
CSP_DIRECTIVES="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';"

# Monitoring (optional)
SENTRY_DSN=${SENTRY_DSN}
ANALYTICS_ID=${ANALYTICS_ID}

# Production Database (if needed)
REDIS_URL=${REDIS_URL}

# Build Settings
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=false
{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "NIXPACKS", "buildCommand": "npm ci && npm run build", "watchPatterns": ["src/**", "package.json"], "buildCache": true}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10, "region": "us-west1", "memoryLimitMB": 512, "strategy": "blueGreen", "healthcheckPath": "/", "healthcheckTimeout": 30, "healthcheckGracePeriod": 60, "edgeRuntime": true, "volumes": [{"name": "video-storage", "mountPath": "/app/uploads"}]}}
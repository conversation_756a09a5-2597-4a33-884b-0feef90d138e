# As this is a starter template project, we don't want to check in the uv.lock and livekit.toml files in its template form
# However, once you have cloned this repo for your own use, LiveKit recommends you check them in and delete this github workflow entirely
    
name: Template Check

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  check-template-files:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Check template files not tracked in git
      run: |
        if git ls-files | grep -q "^uv\.lock$"; then
          echo "Error: uv.lock should not be checked into git"
          echo "Disable this test and commit the file once you have cloned this repo for your own use"
          exit 1
        fi
        if git ls-files | grep -q "^livekit\.toml$"; then
          echo "Error: livekit.toml should not be checked into git"
          echo "Disable this test and commit the file once you have cloned this repo for your own use"
          exit 1
        fi
        echo "✓ uv.lock and livekit.toml are correctly not tracked in git" 
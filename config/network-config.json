{"network": {"hub": {"device": "MacBook Air", "role": "central-hub", "interfaces": {"usb_tethering": {"interface": "en0", "type": "usb-c", "purpose": "internet-input", "provider": "android-device"}, "wifi_ap": {"interface": "en1", "type": "wifi-hotspot", "purpose": "device-network", "ssid": "CombatMirror-Hub", "password": "SecureMirror2024!", "channel": 6, "security": "WPA2"}}}, "subnets": {"management": {"range": "***********/24", "gateway": "***********", "dhcp": {"start": "***********00", "end": "*************", "lease_time": "3600"}, "dns": {"primary": "***********", "secondary": "*******"}}}, "devices": {"android": {"name": "Android Smartphone", "role": "internet-provider", "connection": "usb-tethering", "priority": 1}, "ipad": {"name": "iPad Pro", "role": "client-device", "connection": "wifi", "reserved_ip": "***********0"}, "iphone": {"name": "iPhone 15 Pro Max", "role": "client-device", "connection": "wifi", "reserved_ip": "************"}}, "services": {"combat_mirror": {"port": 8080, "protocol": "tcp", "description": "Main Combat Mirror web interface"}, "livekit": {"ports": [3478, 3479, 3480, 3481], "protocol": "tcp", "description": "LiveKit WebRTC services"}, "monitoring": {"port": 9090, "protocol": "tcp", "description": "Network monitoring dashboard"}}, "firewall": {"policies": {"input": "DROP", "forward": "DROP", "output": "ACCEPT"}, "rules": {"allow_established": true, "allow_loopback": true, "allow_dhcp": true, "allow_dns": true, "allow_combat_mirror": true, "block_inter_device": true}}}}
import { defineConfig, loadEnv } from 'vite';
import basicSsl from '@vitejs/plugin-basic-ssl';

export default defineConfig(({ mode }) => {
    const env = loadEnv(mode, '.', '');
    return {
      plugins: [
        basicSsl() // Enable HTTPS for mobile camera access
      ],
      define: {
        // Removed API key exposure - now using backend proxy
      },
      resolve: {
        alias: {
          '@': '.',
        }
      },
      server: {
        https: true, // Enable HTTPS
        host: '0.0.0.0', // Allow external connections
        fs: {
          strict: false
        }
      }
    };
});
// Input validation utilities for security

/**
 * Sanitizes HTML content to prevent XSS attacks
 */
export const sanitizeHtml = (input: string): string => {
  // Remove script tags and dangerous attributes
  const div = document.createElement('div');
  div.textContent = input;
  return div.innerHTML;
};

/**
 * Validates and sanitizes participant ID
 */
export const validateParticipantId = (id: string): string => {
  // Only allow alphanumeric characters, hyphens, and underscores
  const sanitized = id.replace(/[^a-zA-Z0-9-_]/g, '');
  if (sanitized.length < 1 || sanitized.length > 50) {
    throw new Error('Participant ID must be between 1 and 50 characters');
  }
  return sanitized;
};

/**
 * Validates session type
 */
export const validateSessionType = (type: string): boolean => {
  const validTypes = [
    'Baseline Session (5 mins)',
    'Training Session (15 mins)',
    'Recovery Analysis (5 mins)',
    'Video Analysis (15 mins)'
  ];
  return validTypes.includes(type);
};

/**
 * Validates file upload
 */
export const validateVideoFile = (file: File): void => {
  const maxSize = parseInt(import.meta.env.VITE_MAX_VIDEO_SIZE_MB || '50') * 1024 * 1024;
  const allowedTypes = ['video/mp4', 'video/webm', 'video/quicktime'];
  
  if (file.size > maxSize) {
    throw new Error(`File size exceeds ${maxSize / 1024 / 1024}MB limit`);
  }
  
  if (!allowedTypes.includes(file.type)) {
    throw new Error('Invalid file type. Only MP4, WebM, and MOV files are allowed');
  }
};

/**
 * Sanitizes metrics data
 */
export const sanitizeMetrics = (metrics: any): any => {
  const sanitized: any = {};
  
  // Ensure all metrics are numbers and within reasonable ranges
  sanitized.punchRate = Math.max(0, Math.min(200, Number(metrics.punchRate) || 0));
  sanitized.punchCount = Math.max(0, Math.min(10000, Math.floor(Number(metrics.punchCount) || 0)));
  sanitized.punchVelocity = Math.max(0, Math.min(20, Number(metrics.punchVelocity) || 0));
  sanitized.headMovement = Math.max(0, Math.min(10, Number(metrics.headMovement) || 0));
  sanitized.postureScore = Math.max(0, Math.min(100, Number(metrics.postureScore) || 0));
  sanitized.gaitBalance = Math.max(0, Math.min(100, Number(metrics.gaitBalance) || 0));
  sanitized.fatigue = Math.max(0, Math.min(100, Number(metrics.fatigue) || 0));
  
  return sanitized;
};

/**
 * Rate limiting helper
 */
export class RateLimiter {
  private attempts: Map<string, number[]> = new Map();
  private maxAttempts: number;
  private windowMs: number;

  constructor(maxAttempts = 100, windowMs = 60000) {
    this.maxAttempts = maxAttempts;
    this.windowMs = windowMs;
  }

  check(key: string): boolean {
    const now = Date.now();
    const attempts = this.attempts.get(key) || [];
    
    // Remove old attempts outside the window
    const validAttempts = attempts.filter(time => now - time < this.windowMs);
    
    if (validAttempts.length >= this.maxAttempts) {
      return false; // Rate limit exceeded
    }
    
    validAttempts.push(now);
    this.attempts.set(key, validAttempts);
    return true;
  }

  reset(key: string): void {
    this.attempts.delete(key);
  }
}
# Combat Mirror Vision Agent

An AI-powered combat sports coach based on the LiveKit Vision Demo architecture, enhanced with Google Gemini Live API for real-time video analysis and coaching feedback.

## 🎯 Overview

The Combat Mirror Vision Agent is built on LiveKit's multimodal agent framework and provides:

- **Real-time video analysis** of combat training sessions
- **AI coaching feedback** using Google Gemini Live API
- **Professional technique assessment** for boxing, MMA, and martial arts
- **Safety-focused training guidance** with actionable improvements
- **Multi-camera support** for comprehensive analysis

## 🏗️ Architecture

### Based on LiveKit Vision Demo
This implementation follows the proven architecture from [LiveKit's Vision Demo](https://github.com/livekit-examples/vision-demo) but adapts it specifically for combat sports training:

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   Mobile App    │◄──►│  LiveKit Server  │◄──►│  Vision Agent   │
│  (iOS/Browser)  │    │   (WebRTC Hub)   │    │ (AI Coach)      │
└─────────────────┘    └──────────────────┘    └─────────────────┘
        │                        │                        │
        │                        │                        │
    📱 Camera               🔄 Real-time              🧠 Gemini Live API
    Stream                  Video/Audio                Analysis & Coaching
```

### Key Components

1. **Combat Vision Agent** (`agent/combat_vision_agent.py`)
   - Extends LiveKit's `Agent` class with combat-specific analysis
   - Integrates with Google Gemini Live API for intelligent coaching
   - Processes video frames at optimized intervals (0.5-2 seconds)
   - Generates real-time technique feedback and safety guidance

2. **Enhanced iOS Client** (`ios-client/CombatMirrorVisionApp.swift`)
   - Native SwiftUI app based on LiveKit Swift SDK
   - QR code scanning for easy connection
   - Real-time metrics display and coaching feedback
   - Background operation support for continuous training

3. **Web Interface Integration**
   - Seamless integration with existing React interface
   - HTTPS support for mobile camera access
   - Multi-camera QR code generation
   - Real-time metrics visualization

## 🚀 Quick Start

### Prerequisites

1. **Google Gemini API Key**
   ```bash
   # Get your API key from:
   https://ai.google.dev/gemini-api/docs
   ```

2. **System Requirements**
   - Python 3.9+ (LiveKit Agents requirement)
   - Node.js 18+ (for web interface)
   - Docker (for LiveKit server)

### 1. Start LiveKit Infrastructure

```bash
# Start the complete system (LiveKit + Web Interface)
./start-combat-mirror.sh
```

### 2. Configure Vision Agent

```bash
# Copy environment template
cp agent/.env.example agent/.env

# Edit with your API keys
nano agent/.env
```

Add your Google Gemini API key:
```env
GOOGLE_API_KEY=your_google_gemini_api_key_here
```

### 3. Start Vision Agent

```bash
# Start the AI coach agent
./start-vision-agent.sh
```

### 4. Connect Your Device

1. Open **https://************:5173** on your iPhone
2. Accept the HTTPS certificate warning
3. Toggle **"Use Live Camera"** mode
4. Scan the QR code with your device
5. Allow camera permissions
6. Start your training session!

## 🎯 Combat Analysis Features

### Real-time Coaching Areas

1. **Stance Analysis**
   - Foot positioning and weight distribution
   - Balance and stability assessment
   - Stance type detection (Orthodox/Southpaw)

2. **Defensive Positioning**
   - Guard placement and height
   - Head position and chin tuck
   - Shoulder positioning for protection

3. **Technique Assessment**
   - Punch form and mechanics
   - Rotation and follow-through
   - Timing and rhythm analysis

4. **Safety Focus**
   - Risk identification and prevention
   - Proper form enforcement
   - Injury prevention guidance

### AI Coaching Personality

The agent is programmed as a professional combat sports coach with:
- **Encouraging but constructive feedback**
- **Safety-first approach**
- **Proper boxing/MMA terminology**
- **Actionable improvement suggestions**
- **15-second focused coaching segments**

## 📱 Mobile Integration

### iOS App Features
- Native LiveKit Swift SDK integration
- QR code scanning for instant connection
- Real-time video streaming
- Coaching feedback display
- Background operation support
- Camera switching controls

### Browser Alternative
- Access via mobile Safari at HTTPS URL
- Full camera permissions support
- Works on any iOS/Android device
- No app installation required

## 🔧 Configuration

### Vision Agent Settings

```python
# Frame analysis rate (seconds between analyses)
ANALYSIS_FRAME_RATE = 2.0  # Analyze every 2 seconds

# Coaching focus areas (cycles through these)
ANALYSIS_PROMPTS = [
    "stance and posture",
    "defensive positioning", 
    "balance and weight distribution",
    "guard placement",
    "technique assessment"
]
```

### Gemini Live API Configuration

```python
llm=google.beta.realtime.RealtimeModel(
    voice="Echo",      # Coach-appropriate voice
    temperature=0.7,   # Balanced creativity/consistency
)
```

## 🔍 Troubleshooting

### Common Issues

1. **"Google API Key not set"**
   ```bash
   # Edit your environment file
   nano agent/.env
   # Add: GOOGLE_API_KEY=your_key_here
   ```

2. **"LiveKit server not accessible"**
   ```bash
   # Ensure the main system is running
   ./start-combat-mirror.sh
   # Check Docker containers
   docker ps | grep livekit
   ```

3. **"Camera access denied on mobile"**
   - Ensure you're using HTTPS (not HTTP)
   - Accept certificate warnings
   - Check Safari camera permissions

4. **"Agent dependencies failed to install"**
   ```bash
   # Check Python version (requires 3.9+)
   python3 --version
   # Try with specific Python version
   python3.11 -m venv .venv
   ```

### Debug Mode

```bash
# Run agent with debug logging
cd agent
source .venv/bin/activate
export LOG_LEVEL=DEBUG
python combat_vision_agent.py
```

## 📊 System Integration

### Data Flow

1. **Video Input**: Mobile device captures video
2. **WebRTC Streaming**: LiveKit handles real-time transmission
3. **AI Processing**: Vision agent analyzes frames with Gemini
4. **Coaching Output**: Feedback delivered via audio/text
5. **Metrics Broadcasting**: Real-time stats sent to web interface

### Performance Optimization

- **Frame Rate Control**: Analyzes every 0.5-2 seconds (configurable)
- **Image Compression**: JPEG at 1024x1024 max resolution
- **Batch Processing**: Groups analysis requests for efficiency
- **Smart Caching**: Avoids redundant analysis of similar frames

## 🎮 Usage Scenarios

### Solo Training
- Real-time technique feedback
- Form correction and improvement
- Safety monitoring and guidance
- Progress tracking over sessions

### Coach-Assisted Training
- Supplementary AI analysis
- Objective technique metrics
- Detailed video breakdown
- Enhanced training insights

### Remote Coaching
- Coach observes via web interface
- AI provides immediate feedback
- Recorded sessions for review
- Multi-angle analysis support

## 🔮 Future Enhancements

1. **Advanced Pose Analysis**
   - MediaPipe integration for precise biomechanics
   - 3D pose estimation from multiple cameras
   - Movement pattern recognition

2. **Personalized Training**
   - Individual fighter profiles
   - Custom coaching styles
   - Progressive difficulty adjustment

3. **Performance Analytics**
   - Session-over-session improvement tracking
   - Detailed technique scoring
   - Comparative analysis with professionals

## 📚 References

- [LiveKit Vision Demo](https://github.com/livekit-examples/vision-demo)
- [LiveKit Agents Documentation](https://docs.livekit.io/agents/)
- [Google Gemini Live API](https://ai.google.dev/gemini-api/docs/live)
- [LiveKit Swift SDK](https://github.com/livekit/client-sdk-swift)

---

**Built with ❤️ for combat sports training and safety**
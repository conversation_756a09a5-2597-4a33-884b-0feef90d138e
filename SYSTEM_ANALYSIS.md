# Combat Mirror System - Comprehensive Analysis

## System Overview

The Combat Mirror System is a sophisticated multi-camera combat sports training analysis platform that combines real-time video processing, pose detection, and performance metrics tracking. The system supports both live camera streams (via LiveKit WebRTC) and uploaded video analysis.

## Architecture Components

### 1. Core Technology Stack

- **Frontend Framework**: React 19 with TypeScript
- **Styling**: Tailwind CSS (via CDN)
- **Build Tool**: Vite
- **Real-time Video**: LiveKit Client/Server SDK
- **Computer Vision**: MediaPipe Pose Detection
- **AI Analysis**: Google Gemini API
- **Video Storage**: IndexedDB with sync capabilities
- **Development Server**: Running on port 5176

### 2. Key Features

#### Multi-Camera Support
- 4 camera positions: FRONT (main), LEFT, RIGHT, BEHIND
- Bento box layout with FRONT camera as primary view
- Support for both live streams and uploaded videos

#### Live Streaming (LiveKit Integration)
- WebRTC-based real-time video streaming
- QR code generation for mobile device connections
- Automatic room creation and token generation
- Desktop acts as subscriber, mobile devices as publishers
- Server running on ws://localhost:7880

#### Video Upload & Storage
- Local video file upload with preview
- IndexedDB persistence for offline capability
- Automatic sync queue with retry mechanism
- Sync status tracking (pending → syncing → synced)
- Video metadata management

#### Pose Analysis (MediaPipe)
- Real-time pose detection with 33 landmarks
- Frame-by-frame analysis with history tracking
- Velocity calculations for punch detection
- Performance metrics computation

### 3. Metrics Tracked

```typescript
interface Metrics {
  punchRate: number;       // Punches per minute
  punchCount: number;      // Total punch count
  punchVelocity: number;   // Average punch speed (m/s)
  headMovement: number;    // Head movement in cm
  postureScore: number;    // 0-100 posture quality
  gaitBalance: number;     // 0-100 balance score  
  fatigue: number;         // 0-100 fatigue level
}
```

### 4. Analysis Algorithms

#### Posture Score Calculation
- Shoulder alignment (level check)
- Hip alignment (level check)
- Spine straightness (shoulder-hip alignment)
- Head position (forward lean detection)

#### Balance Score Calculation
- Center of mass stability
- Foot positioning relative to CoM
- Stance width relative to shoulders
- Weight distribution analysis

#### Punch Detection
- Velocity threshold: 3.0 m/s
- Arm extension validation
- Left/right hand differentiation
- Temporal punch history tracking

#### Fatigue Calculation
- Performance degradation over time
- Posture score decline analysis
- Time-based linear fatigue model
- Combined metric approach

### 5. Session Management

#### Session Types
- Baseline Session (5 mins)
- Training Session (15 mins)
- Recovery Analysis (5 mins)

#### Session States
- Idle → Active → Analyzing → Finished

#### Data Flow
1. Session starts → Metrics reset
2. Real-time or simulated data collection
3. Frame analysis via MediaPipe
4. Metrics computation and display
5. Session stop → Report generation
6. Analysis via Gemini API

### 6. UI Components Structure

```
App.tsx
├── Header
├── Dashboard
│   ├── SessionControl
│   ├── CameraGrid
│   │   ├── CameraView (×4)
│   │   │   ├── QRCodeDisplay (LiveKit)
│   │   │   ├── LiveKitVideo
│   │   │   ├── VideoPoseAnalysis
│   │   │   └── CameraVideoUpload
│   │   └── Sync & Analyze Button
│   └── MetricsDisplay
├── VideoUpload
├── VideoList
└── ReportModal
```

### 7. Service Architecture

#### MediaPipeService
- Pose detection initialization
- Landmark processing
- Drawing utilities
- Distance/velocity calculations

#### LiveKitService
- Token generation (JWT)
- Room management
- Connection handling
- QR code URL generation

#### VideoStorageService
- IndexedDB operations
- Sync queue management
- Metadata tracking
- Retry mechanisms

#### VideoAnalysisService
- Frame-by-frame analysis
- Metric calculations
- Punch detection
- Performance tracking

#### GeminiService
- AI-powered report generation
- Performance insights
- Recommendation generation

### 8. Key Configuration

#### Environment Variables Needed
```env
GEMINI_API_KEY=your_api_key_here
```

#### LiveKit Configuration
- URL: ws://localhost:7880
- API Key: APIfightmaster
- API Secret: xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ

### 9. Data Persistence

#### IndexedDB Structure
- Database: CombatMirrorVideos
- Object Store: videos
- Stores video blobs with metadata
- Automatic blob URL recreation on load

#### Sync Architecture
- Local-first approach
- Background sync every 30 seconds
- Failed sync retry mechanism
- Cloud sync simulation (ready for real implementation)

### 10. Performance Considerations

- Frame analysis at 30 FPS
- History limited to 150 frames (5 seconds)
- Punch detection with velocity thresholds
- Optimized pose model (complexity: 2)
- Responsive grid layout for multi-screen support

### 11. Security & Privacy

- Local video storage by default
- Participant ID anonymization
- No automatic cloud upload
- Token-based LiveKit authentication
- HTTPS required for camera access on mobile

### 12. Future Enhancement Opportunities

1. **Cloud Storage Integration**
   - Replace simulateCloudSync with AWS S3/GCS
   - Implement proper authentication
   - Add progress tracking for uploads

2. **Advanced Analytics**
   - Machine learning for pattern recognition
   - Comparative analysis between sessions
   - Predictive injury risk assessment

3. **Multi-User Support**
   - User accounts and profiles
   - Historical data tracking
   - Team/coach collaboration features

4. **Mobile App**
   - Native iOS/Android apps
   - Direct camera integration
   - Offline analysis capability

5. **Real-time Coaching**
   - Audio feedback during sessions
   - Live coach connection
   - Automated technique corrections

## Development Commands

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## System Requirements

- Modern browser with WebRTC support
- Camera permissions for live streaming
- HTTPS for mobile camera access
- Sufficient storage for video files
- Stable internet for sync features

## Current Limitations

1. Cloud sync is simulated (not implemented)
2. LiveKit server must be running locally
3. Mobile camera requires HTTPS setup
4. No user authentication system
5. Limited to 4 camera positions
6. No export functionality for reports

This system provides a solid foundation for combat sports training analysis with room for significant expansion and enhancement based on user needs and feedback.
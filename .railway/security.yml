version: 1
security:
  # Enable secret scanning to prevent accidental credential exposure
  scanSecrets: true
  blockOnSecrets: true
  
  # Enable Railway Shield for enhanced protection
  shield:
    enabled: true
    ddosProtection: true
    
  # Rate limiting configuration
  rateLimiting:
    enabled: true
    rules:
      - path: "/api/*"
        windowMs: 60000  # 1 minute
        max: 100         # 100 requests per minute
      - path: "/api/analyze"
        windowMs: 60000  # 1 minute
        max: 20          # 20 analysis requests per minute
        
  # WAF (Web Application Firewall) rules
  waf:
    enabled: true
    rules:
      - blockSqlInjection: true
      - blockXss: true
      - blockPathTraversal: true
      - blockCommandInjection: true
      
  # CORS configuration
  cors:
    strictOrigin: true
    allowedOrigins:
      - "${FRONTEND_URL}"
      - "https://*.railway.app"
      
  # Headers security
  headers:
    - name: "X-Frame-Options"
      value: "DENY"
    - name: "X-Content-Type-Options"
      value: "nosniff"
    - name: "X-XSS-Protection"
      value: "1; mode=block"
    - name: "Strict-Transport-Security"
      value: "max-age=31536000; includeSubDomains"
    - name: "Content-Security-Policy"
      value: "default-src 'self'; script-src 'self' 'unsafe-inline' https://cdn.jsdelivr.net; style-src 'self' 'unsafe-inline'; img-src 'self' data: blob:; connect-src 'self' wss://*.railway.app"
      
  # IP allowlist/blocklist
  ipRules:
    # Block known malicious IPs (Railway maintains this list)
    blockMalicious: true
    
  # SSL/TLS configuration
  ssl:
    enforceHttps: true
    minTlsVersion: "1.2"
    
# Monitoring and alerting
monitoring:
  # Enable security event logging
  securityLogs: true
  
  # Alert on suspicious activity
  alerts:
    - type: "rate_limit_exceeded"
      threshold: 5
      window: "5m"
    - type: "waf_block"
      threshold: 10
      window: "1h"
    - type: "secret_detected"
      immediate: true
# Combat Mirror System - Staging Environment Configuration
# This file defines staging-specific settings for Railway deployment

version: 1

# Staging environment settings
environment:
  name: staging
  autoPromote: false  # Manual promotion to production
  
# Service-specific staging configurations
services:
  frontend:
    environment:
      NODE_ENV: staging
      VITE_API_URL: ${STAGING_API_URL}
      VITE_LIVEKIT_URL: ${STAGING_LIVEKIT_URL}
      VITE_DEBUG_MODE: true
    deploy:
      numReplicas: 1
      memoryLimitMB: 512
      cpuLimitMillicores: 500
      healthcheckPath: "/"
      healthcheckInterval: 60
      
  backend:
    environment:
      NODE_ENV: staging
      LOG_LEVEL: debug
      CORS_DEBUG: true
      RATE_LIMIT_WINDOW_MS: 60000
      RATE_LIMIT_MAX_REQUESTS: 200  # Higher limit for testing
    deploy:
      numReplicas: 1
      memoryLimitMB: 512
      cpuLimitMillicores: 500
      healthcheckPath: "/api/health"
      healthcheckInterval: 30
      
  livekit:
    environment:
      NODE_ENV: staging
      LIVEKIT_LOG_LEVEL: debug
      MAX_ROOM_DURATION: 3600  # 1 hour for testing
      WEBHOOK_URL: ${STAGING_WEBHOOK_URL}
    deploy:
      numReplicas: 1
      memoryLimitMB: 1024
      cpuLimitMillicores: 1000
      
# Staging-specific features
features:
  # Enable debug logging
  debugging:
    enabled: true
    verboseErrors: true
    stackTraces: true
    
  # Relaxed security for testing
  security:
    rateLimiting:
      multiplier: 2  # Double the rate limits
    cors:
      allowedOrigins:
        - "http://localhost:*"
        - "https://*.railway.app"
        - "https://*.vercel.app"  # For testing deployments
        
  # Performance monitoring
  monitoring:
    enhanced: true
    sampleRate: 1.0  # 100% sampling in staging
    traceExports: true
    
  # Testing features
  testing:
    mockData: true
    bypassAuth: false  # Keep auth enabled
    testEndpoints: true
    
# Staging database configuration
database:
  redis:
    maxMemoryPolicy: "allkeys-lru"
    maxMemoryMB: 256
    
# Staging-specific volumes
volumes:
  - name: "staging-video-storage"
    mountPath: "/app/staging-uploads"
    sizeGB: 10
  - name: "staging-logs"
    mountPath: "/app/logs"
    sizeGB: 5
    
# Staging deployment pipeline
pipeline:
  # Automated testing before deployment
  preDeployTests:
    - name: "Unit Tests"
      command: "npm test"
      timeout: 300
    - name: "Integration Tests"
      command: "npm run test:integration"
      timeout: 600
      
  # Post-deployment validation
  postDeployChecks:
    - name: "Health Check"
      endpoint: "/api/health"
      expectedStatus: 200
    - name: "Frontend Load"
      endpoint: "/"
      expectedStatus: 200
    - name: "WebSocket Test"
      endpoint: "/ws"
      protocol: "websocket"
      
# Staging-specific alerts
alerts:
  slack:
    enabled: true
    channel: "#combat-mirror-staging"
    events:
      - "deployment_started"
      - "deployment_failed"
      - "health_check_failed"
      
# Auto-scaling rules for staging
scaling:
  enabled: false  # Disabled for cost savings
  minReplicas: 1
  maxReplicas: 1
  
# Staging backup configuration
backup:
  enabled: true
  schedule: "0 2 * * *"  # Daily at 2 AM
  retention: 7  # Keep 7 days of backups
  
# Feature flags for staging
featureFlags:
  newAnalysisEngine: true
  experimentalMetrics: true
  debugUI: true
  performanceProfiler: true
  
# Staging-specific environment variables
defaultVariables:
  # Frontend
  VITE_SHOW_DEBUG_PANEL: "true"
  VITE_ENABLE_PROFILER: "true"
  VITE_API_TIMEOUT: "30000"
  
  # Backend
  API_REQUEST_TIMEOUT: "30000"
  ENABLE_REQUEST_LOGGING: "true"
  ENABLE_PERFORMANCE_MONITORING: "true"
  
  # LiveKit
  LIVEKIT_DEV_MODE: "true"
  LIVEKIT_WEBHOOK_SECRET: ${STAGING_WEBHOOK_SECRET}
  
# Cost optimization for staging
costOptimization:
  # Use spot instances when available
  useSpotInstances: true
  
  # Auto-sleep after inactivity
  autoSleep:
    enabled: true
    inactivityMinutes: 60
    
  # Resource limits
  maxMonthlyCost: 50  # USD
  alertThreshold: 40  # Alert at 80% of budget
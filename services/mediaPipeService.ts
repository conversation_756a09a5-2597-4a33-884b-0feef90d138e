// @ts-ignore
import * as poseModule from '@mediapipe/pose';
// @ts-ignore
import * as cameraModule from '@mediapipe/camera_utils';
// @ts-ignore
import * as drawingModule from '@mediapipe/drawing_utils';

const Pose = poseModule.Pose || (window as any).Pose;
const POSE_CONNECTIONS = poseModule.POSE_CONNECTIONS || (window as any).POSE_CONNECTIONS;
const Camera = cameraModule.Camera || (window as any).Camera;
const drawConnectors = drawingModule.drawConnectors || (window as any).drawConnectors;
const drawLandmarks = drawingModule.drawLandmarks || (window as any).drawLandmarks;

export interface PoseLandmark {
  x: number;
  y: number;
  z: number;
  visibility?: number;
}

export interface PoseResults {
  poseLandmarks: PoseLandmark[];
  poseWorldLandmarks: PoseLandmark[];
  segmentationMask?: ImageBitmap;
}

// MediaPipe Pose landmark indices
export const POSE_LANDMARKS = {
  NOSE: 0,
  LEFT_EYE_INNER: 1,
  LEFT_EYE: 2,
  LEFT_EYE_OUTER: 3,
  RIGHT_EYE_INNER: 4,
  RIGHT_EYE: 5,
  RIGHT_EYE_OUTER: 6,
  LEFT_EAR: 7,
  RIGHT_EAR: 8,
  MOUTH_LEFT: 9,
  MOUTH_RIGHT: 10,
  LEFT_SHOULDER: 11,
  RIGHT_SHOULDER: 12,
  LEFT_ELBOW: 13,
  RIGHT_ELBOW: 14,
  LEFT_WRIST: 15,
  RIGHT_WRIST: 16,
  LEFT_PINKY: 17,
  RIGHT_PINKY: 18,
  LEFT_INDEX: 19,
  RIGHT_INDEX: 20,
  LEFT_THUMB: 21,
  RIGHT_THUMB: 22,
  LEFT_HIP: 23,
  RIGHT_HIP: 24,
  LEFT_KNEE: 25,
  RIGHT_KNEE: 26,
  LEFT_ANKLE: 27,
  RIGHT_ANKLE: 28,
  LEFT_HEEL: 29,
  RIGHT_HEEL: 30,
  LEFT_FOOT_INDEX: 31,
  RIGHT_FOOT_INDEX: 32
};

export class MediaPipeService {
  private pose: any = null;
  private camera: any = null;
  private isInitialized = false;

  async initialize() {
    if (this.isInitialized) return;

    // Load scripts dynamically if not available
    if (!Pose) {
      await this.loadMediaPipeScripts();
    }

    this.pose = new Pose({
      locateFile: (file: string) => {
        return `https://cdn.jsdelivr.net/npm/@mediapipe/pose/${file}`;
      }
    });

    this.pose.setOptions({
      modelComplexity: 2, // 0, 1, or 2. Higher = more accurate but slower
      smoothLandmarks: true,
      enableSegmentation: false,
      smoothSegmentation: false,
      minDetectionConfidence: 0.5,
      minTrackingConfidence: 0.5
    });

    this.isInitialized = true;
  }

  private async loadMediaPipeScripts() {
    // Load MediaPipe scripts from CDN
    const scripts = [
      'https://cdn.jsdelivr.net/npm/@mediapipe/camera_utils/camera_utils.js',
      'https://cdn.jsdelivr.net/npm/@mediapipe/control_utils/control_utils.js',
      'https://cdn.jsdelivr.net/npm/@mediapipe/control_utils_3d/control_utils_3d.js',
      'https://cdn.jsdelivr.net/npm/@mediapipe/drawing_utils/drawing_utils.js',
      'https://cdn.jsdelivr.net/npm/@mediapipe/pose/pose.js',
    ];

    for (const src of scripts) {
      await new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = src;
        script.onload = resolve;
        script.onerror = reject;
        document.head.appendChild(script);
      });
    }
  }

  async processVideoElement(
    videoElement: HTMLVideoElement,
    onResults: (results: PoseResults) => void
  ) {
    if (!this.pose) {
      await this.initialize();
    }

    this.pose!.onResults(onResults);

    const CameraConstructor = Camera || (window as any).Camera;
    this.camera = new CameraConstructor(videoElement, {
      onFrame: async () => {
        await this.pose!.send({ image: videoElement });
      },
      width: 1280,
      height: 720
    });

    this.camera.start();
  }

  async processVideoFrame(
    videoFrame: ImageBitmap | HTMLCanvasElement | HTMLVideoElement,
    timestamp: number
  ): Promise<PoseResults | null> {
    if (!this.pose) {
      await this.initialize();
    }

    return new Promise((resolve) => {
      this.pose!.onResults((results: any) => {
        resolve(results as PoseResults);
      });

      this.pose!.send({ image: videoFrame });
    });
  }

  drawPose(
    canvasCtx: CanvasRenderingContext2D,
    results: PoseResults,
    width: number,
    height: number
  ) {
    canvasCtx.save();
    canvasCtx.clearRect(0, 0, width, height);

    if (results.poseLandmarks) {
      const drawConnectorsFunc = drawConnectors || (window as any).drawConnectors;
      const drawLandmarksFunc = drawLandmarks || (window as any).drawLandmarks;
      const connections = POSE_CONNECTIONS || (window as any).POSE_CONNECTIONS;
      
      drawConnectorsFunc(canvasCtx, results.poseLandmarks, connections, {
        color: '#00FF00',
        lineWidth: 4
      });
      drawLandmarksFunc(canvasCtx, results.poseLandmarks, {
        color: '#FF0000',
        lineWidth: 2
      });
    }

    canvasCtx.restore();
  }

  stop() {
    if (this.camera) {
      this.camera.stop();
      this.camera = null;
    }
  }

  // Utility function to calculate angle between three points
  static calculateAngle(
    point1: PoseLandmark,
    point2: PoseLandmark,
    point3: PoseLandmark
  ): number {
    const radians = Math.atan2(point3.y - point2.y, point3.x - point2.x) -
                    Math.atan2(point1.y - point2.y, point1.x - point2.x);
    let angle = Math.abs(radians * 180.0 / Math.PI);
    if (angle > 180.0) {
      angle = 360 - angle;
    }
    return angle;
  }

  // Utility function to calculate distance between two points
  static calculateDistance(point1: PoseLandmark, point2: PoseLandmark): number {
    return Math.sqrt(
      Math.pow(point2.x - point1.x, 2) +
      Math.pow(point2.y - point1.y, 2) +
      Math.pow(point2.z - point1.z, 2)
    );
  }

  // Utility function to calculate velocity between two positions over time
  static calculateVelocity(
    point1: PoseLandmark,
    point2: PoseLandmark,
    deltaTime: number // in seconds
  ): number {
    const distance = this.calculateDistance(point1, point2);
    return distance / deltaTime;
  }
}

export const mediaPipeService = new MediaPipeService();
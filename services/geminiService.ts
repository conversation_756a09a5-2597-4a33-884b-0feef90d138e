
import { Metrics, SessionType } from '../types';

// Use backend API instead of direct Gemini API calls
const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001';

export const generateAnalysisReport = async (
  metrics: Metrics,
  participantId: string,
  sessionType: SessionType
): Promise<string> => {
  try {
    const response = await fetch(`${API_BASE_URL}/api/analyze`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        metrics,
        participantId,
        sessionType
      })
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.message || 'Failed to generate report');
    }

    const data = await response.json();
    return data.report;

  } catch (error) {
    console.error("Error generating analysis report:", error);
    return "Error: Could not generate the analysis report. Please check the console for more details.";
  }
};

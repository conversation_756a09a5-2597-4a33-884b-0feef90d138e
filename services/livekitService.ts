import { AccessToken } from 'livekit-server-sdk';
import { Room, RoomEvent, VideoPresets, createLocalVideoTrack } from 'livekit-client';

// LiveKit server configuration - SECURITY: Never expose secrets in client code
const LIVEKIT_URL = import.meta.env.VITE_LIVEKIT_URL || 'ws://localhost:7880';

// SECURITY FIX: API keys should NEVER be in client-side code
// These will be moved to server-side token generation
const API_KEY = ''; // REMOVED - Use server-side token generation
const API_SECRET = ''; // REMOVED - Use server-side token generation

export interface CameraConnection {
  roomName: string;
  token: string;
  participantName: string;
  url: string;
}

export class LiveKitService {
  private rooms: Map<string, Room> = new Map();

  // Generate access token for a participant
  generateToken(roomName: string, participantName: string, isPublisher: boolean = true): string {
    const at = new AccessToken(API_KEY, API_SECRET, {
      identity: participantName,
      ttl: '24h',
    });

    at.addGrant({ 
      roomJoin: true, 
      room: roomName,
      canPublish: isPublisher,
      canSubscribe: true,
      canPublishData: true,
    });

    return at.toJwt();
  }

  // Generate connection info for QR code
  generateCameraConnection(cameraPosition: string): CameraConnection {
    const roomName = `combat-mirror-${cameraPosition.toLowerCase()}-${Date.now()}`;
    const participantName = `iphone-${cameraPosition.toLowerCase()}`;
    const token = this.generateToken(roomName, participantName, true);
    
    // Create URL for mobile device - use HTTPS for camera access
    const isProduction = window.location.hostname !== 'localhost';
    const host = isProduction ? window.location.hostname : '************';
    const port = isProduction ? '' : (window.location.port || '5173');
    const protocol = 'https'; // Always use HTTPS for mobile camera access
    const baseUrl = isProduction ? `${protocol}://${host}` : `${protocol}://${host}:${port}`;
    const mobileUrl = `${baseUrl}/camera-stream.html?room=${roomName}&token=${encodeURIComponent(token)}&position=${cameraPosition}`;

    return {
      roomName,
      token,
      participantName,
      url: mobileUrl
    };
  }

  // Connect desktop app to receive video from mobile
  async connectToRoom(roomName: string, cameraPosition: string): Promise<Room> {
    const room = new Room({
      adaptiveStream: true,
      dynacast: true,
      videoCaptureDefaults: {
        resolution: VideoPresets.h720.resolution,
      },
    });

    const token = this.generateToken(roomName, `desktop-${cameraPosition}`, false);

    // Event handlers
    room.on(RoomEvent.TrackSubscribed, (track, publication, participant) => {
      console.log(`Track subscribed from ${participant.identity}:`, track.kind);
    });

    room.on(RoomEvent.TrackUnsubscribed, (track, publication, participant) => {
      console.log(`Track unsubscribed from ${participant.identity}:`, track.kind);
    });

    room.on(RoomEvent.Disconnected, () => {
      console.log(`Disconnected from room ${roomName}`);
    });

    await room.connect(LIVEKIT_URL, token);
    this.rooms.set(roomName, room);

    return room;
  }

  // Disconnect from a room
  async disconnectFromRoom(roomName: string) {
    const room = this.rooms.get(roomName);
    if (room) {
      await room.disconnect();
      this.rooms.delete(roomName);
    }
  }

  // Get all active rooms
  getActiveRooms(): Map<string, Room> {
    return this.rooms;
  }

  // Create local video track for mobile publishing
  async createCameraTrack() {
    return await createLocalVideoTrack({
      facingMode: 'environment', // Use back camera
      resolution: VideoPresets.h720.resolution,
    });
  }
}

export const liveKitService = new LiveKitService();
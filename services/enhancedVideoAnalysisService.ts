import { mediaPipeService, MediaPipeService, PoseResults, PoseLandmark, POSE_LANDMARKS } from './mediaPipeService';
import { Metrics } from '../types';

// Enhanced punch type detection from PunchTracker
export enum PunchType {
  JAB = 'jab',
  CROSS = 'cross',
  HOOK = 'hook',
  UPPERCUT = 'uppercut'
}

interface Position {
  x: number;
  y: number;
  z: number;
}

interface VelocityVector {
  magnitude: number;
  direction: { x: number; y: number; z: number };
}

interface FrameAnalysis {
  timestamp: number;
  poseLandmarks: PoseLandmark[];
  metrics: Partial<Metrics>;
}

interface EnhancedPunchData {
  timestamp: number;
  hand: 'left' | 'right';
  type: PunchType;
  velocity: number;
  direction: { x: number; y: number };
  startPosition: PoseLandmark;
  endPosition: PoseLandmark;
  armExtensionRatio: number;
  confidence: number;
}

interface ComboPattern {
  id: string;
  name: string;
  sequence: PunchType[];
  maxTimeBetweenPunches: number;
  minTimeBetweenPunches: number;
}

interface DetectedCombo {
  pattern: ComboPattern;
  timestamp: number;
  accuracy: number;
  punches: EnhancedPunchData[];
}

export class EnhancedVideoAnalysisService {
  private frameHistory: FrameAnalysis[] = [];
  private punchHistory: EnhancedPunchData[] = [];
  private detectedCombos: DetectedCombo[] = [];
  private analysisStartTime: number = 0;
  private lastFrameTime: number = 0;
  
  // Enhanced punch detection parameters from PunchTracker
  private velocityThreshold = 50; // pixels per frame (adjustable)
  private directionThreshold = 0.7; // cosine similarity threshold
  private punchCooldown = 0.5; // seconds between punches
  private lastPunchTime: { left: number; right: number } = { left: 0, right: 0 };
  
  // Position history for velocity calculation (from PunchTracker)
  private positionHistory: Map<string, Position[]> = new Map();
  private timestampHistory: Map<string, number[]> = new Map();
  private readonly POSITION_HISTORY_LENGTH = 10;
  
  // Combo detection patterns
  private comboPatterns: ComboPattern[] = [
    { 
      id: 'jab-cross', 
      name: 'One-Two', 
      sequence: [PunchType.JAB, PunchType.CROSS], 
      maxTimeBetweenPunches: 800,
      minTimeBetweenPunches: 200 
    },
    { 
      id: 'jab-cross-hook', 
      name: 'Classic Combo', 
      sequence: [PunchType.JAB, PunchType.CROSS, PunchType.HOOK], 
      maxTimeBetweenPunches: 1200,
      minTimeBetweenPunches: 200 
    },
    { 
      id: 'double-jab-cross', 
      name: 'Double Jab Cross', 
      sequence: [PunchType.JAB, PunchType.JAB, PunchType.CROSS], 
      maxTimeBetweenPunches: 1000,
      minTimeBetweenPunches: 150 
    },
    {
      id: 'hook-cross-hook',
      name: 'Hook Combination',
      sequence: [PunchType.HOOK, PunchType.CROSS, PunchType.HOOK],
      maxTimeBetweenPunches: 1200,
      minTimeBetweenPunches: 200
    }
  ];

  // Calibration data
  private calibrationData = {
    velocityMultiplier: 1.0,
    directionAdjust: 0.0,
    userReachLength: 0.7 // Normalized arm reach
  };

  // Performance optimization
  private readonly MAX_FRAME_HISTORY = 150;
  private readonly MAX_PUNCH_HISTORY = 100;
  private readonly MAX_COMBO_HISTORY = 50;

  constructor() {
    this.initializeHistories();
  }

  private initializeHistories() {
    // Initialize position histories for both wrists
    ['left_wrist', 'right_wrist'].forEach(key => {
      this.positionHistory.set(key, []);
      this.timestampHistory.set(key, []);
    });
  }

  startAnalysis() {
    this.frameHistory = [];
    this.punchHistory = [];
    this.detectedCombos = [];
    this.analysisStartTime = Date.now();
    this.lastFrameTime = this.analysisStartTime;
    this.lastPunchTime = { left: 0, right: 0 };
    this.initializeHistories();
  }

  async analyzeFrame(poseResults: PoseResults): Promise<Metrics> {
    const currentTime = Date.now();
    
    if (!poseResults.poseLandmarks || poseResults.poseLandmarks.length === 0) {
      return this.getCurrentMetrics();
    }

    const landmarks = poseResults.poseLandmarks;
    
    // Update position histories for velocity calculation
    this.updatePositionHistories(landmarks, currentTime);
    
    // Calculate frame-specific metrics
    const frameMetrics: Partial<Metrics> = {
      postureScore: this.calculatePostureScore(landmarks),
      gaitBalance: this.calculateGaitBalance(landmarks),
      headMovement: this.calculateHeadMovement(landmarks),
    };

    // Enhanced punch detection
    const detectedPunches = this.detectEnhancedPunches(landmarks, currentTime);
    detectedPunches.forEach(punch => {
      this.punchHistory.push(punch);
      
      // Check for combos
      const combo = this.detectCombo();
      if (combo) {
        this.detectedCombos.push(combo);
        console.log(`Combo detected: ${combo.pattern.name} with ${combo.accuracy.toFixed(1)}% accuracy`);
      }
    });

    // Maintain history limits
    this.pruneHistories();

    // Store frame analysis
    this.frameHistory.push({
      timestamp: currentTime,
      poseLandmarks: landmarks,
      metrics: frameMetrics
    });

    this.lastFrameTime = currentTime;
    return this.getCurrentMetrics();
  }

  private updatePositionHistories(landmarks: PoseLandmark[], timestamp: number) {
    const leftWrist = landmarks[POSE_LANDMARKS.LEFT_WRIST];
    const rightWrist = landmarks[POSE_LANDMARKS.RIGHT_WRIST];
    
    // Update left wrist history
    const leftHistory = this.positionHistory.get('left_wrist')!;
    const leftTimestamps = this.timestampHistory.get('left_wrist')!;
    leftHistory.push({ x: leftWrist.x, y: leftWrist.y, z: leftWrist.z });
    leftTimestamps.push(timestamp);
    
    // Update right wrist history
    const rightHistory = this.positionHistory.get('right_wrist')!;
    const rightTimestamps = this.timestampHistory.get('right_wrist')!;
    rightHistory.push({ x: rightWrist.x, y: rightWrist.y, z: rightWrist.z });
    rightTimestamps.push(timestamp);
    
    // Maintain history length
    if (leftHistory.length > this.POSITION_HISTORY_LENGTH) {
      leftHistory.shift();
      leftTimestamps.shift();
    }
    if (rightHistory.length > this.POSITION_HISTORY_LENGTH) {
      rightHistory.shift();
      rightTimestamps.shift();
    }
  }

  private calculateVelocity(positions: Position[], timestamps: number[]): VelocityVector {
    if (positions.length < 2 || timestamps.length < 2) {
      return { magnitude: 0, direction: { x: 0, y: 0, z: 0 } };
    }

    // Use last 3 frames for averaging (if available)
    const framesToUse = Math.min(3, positions.length - 1);
    let totalVelocity = 0;
    let avgDirection = { x: 0, y: 0, z: 0 };

    for (let i = 0; i < framesToUse; i++) {
      const idx = positions.length - 2 - i;
      if (idx < 0) break;

      const pos1 = positions[idx];
      const pos2 = positions[idx + 1];
      const time1 = timestamps[idx];
      const time2 = timestamps[idx + 1];

      const dx = pos2.x - pos1.x;
      const dy = pos2.y - pos1.y;
      const dz = pos2.z - pos1.z;
      const dt = (time2 - time1) / 1000; // Convert to seconds

      if (dt === 0) continue;

      const displacement = Math.sqrt(dx * dx + dy * dy + dz * dz);
      const velocity = displacement / dt;
      
      totalVelocity += velocity;
      
      if (displacement > 0) {
        avgDirection.x += dx / displacement;
        avgDirection.y += dy / displacement;
        avgDirection.z += dz / displacement;
      }
    }

    const avgVelocity = totalVelocity / framesToUse;
    const dirMagnitude = Math.sqrt(
      avgDirection.x * avgDirection.x + 
      avgDirection.y * avgDirection.y + 
      avgDirection.z * avgDirection.z
    );

    if (dirMagnitude > 0) {
      avgDirection.x /= dirMagnitude;
      avgDirection.y /= dirMagnitude;
      avgDirection.z /= dirMagnitude;
    }

    return {
      magnitude: avgVelocity * this.calibrationData.velocityMultiplier,
      direction: avgDirection
    };
  }

  private detectEnhancedPunches(landmarks: PoseLandmark[], currentTime: number): EnhancedPunchData[] {
    const detectedPunches: EnhancedPunchData[] = [];
    
    const hands = [
      { side: 'left' as const, wristKey: 'left_wrist' },
      { side: 'right' as const, wristKey: 'right_wrist' }
    ];

    for (const hand of hands) {
      const positions = this.positionHistory.get(hand.wristKey)!;
      const timestamps = this.timestampHistory.get(hand.wristKey)!;
      
      if (positions.length < 2) continue;

      const velocityVector = this.calculateVelocity(positions, timestamps);
      
      // Check if this is a punch motion
      if (this.isPunchMotion(velocityVector, hand.side, currentTime)) {
        const punchType = this.classifyPunchType(landmarks, hand.side, velocityVector);
        const armExtension = this.calculateArmExtension(landmarks, hand.side);
        
        const punch: EnhancedPunchData = {
          timestamp: currentTime,
          hand: hand.side,
          type: punchType,
          velocity: velocityVector.magnitude,
          direction: { x: velocityVector.direction.x, y: velocityVector.direction.y },
          startPosition: this.positionToLandmark(positions[positions.length - 2]),
          endPosition: this.positionToLandmark(positions[positions.length - 1]),
          armExtensionRatio: armExtension,
          confidence: this.calculatePunchConfidence(velocityVector, armExtension)
        };
        
        detectedPunches.push(punch);
        this.lastPunchTime[hand.side] = currentTime;
        
        console.log(`${punchType.toUpperCase()} detected - ${hand.side} hand, velocity: ${velocityVector.magnitude.toFixed(1)}`);
      }
    }

    return detectedPunches;
  }

  private isPunchMotion(velocity: VelocityVector, hand: 'left' | 'right', currentTime: number): boolean {
    // Check velocity threshold
    if (velocity.magnitude < this.velocityThreshold) {
      return false;
    }

    // Check direction (punches generally move forward)
    // For left hand, x direction should be negative (in mirrored view)
    // For right hand, x direction should be positive
    const expectedDirection = hand === 'left' ? velocity.direction.x < 0 : velocity.direction.x > 0;
    if (!expectedDirection) {
      return false;
    }

    // Check cooldown to prevent multiple detections
    const timeSinceLastPunch = (currentTime - this.lastPunchTime[hand]) / 1000;
    if (timeSinceLastPunch < this.punchCooldown) {
      return false;
    }

    return true;
  }

  private classifyPunchType(
    landmarks: PoseLandmark[], 
    hand: 'left' | 'right',
    velocity: VelocityVector
  ): PunchType {
    const wristIdx = hand === 'left' ? POSE_LANDMARKS.LEFT_WRIST : POSE_LANDMARKS.RIGHT_WRIST;
    const elbowIdx = hand === 'left' ? POSE_LANDMARKS.LEFT_ELBOW : POSE_LANDMARKS.RIGHT_ELBOW;
    const shoulderIdx = hand === 'left' ? POSE_LANDMARKS.LEFT_SHOULDER : POSE_LANDMARKS.RIGHT_SHOULDER;
    
    const wrist = landmarks[wristIdx];
    const elbow = landmarks[elbowIdx];
    const shoulder = landmarks[shoulderIdx];
    
    // Calculate arm extension
    const armExtension = this.calculateArmExtension(landmarks, hand);
    
    // Check vertical position of wrist relative to elbow
    const wristAboveElbow = wrist.y < elbow.y;
    
    // Check horizontal position of wrist relative to shoulder
    const wristOutsideShoulder = hand === 'left' 
      ? wrist.x < shoulder.x 
      : wrist.x > shoulder.x;
    
    // Classify based on arm position and extension
    if (wristAboveElbow && armExtension > 0.7) {
      return PunchType.UPPERCUT;
    } else if (wristOutsideShoulder && armExtension < 0.7) {
      return PunchType.HOOK;
    } else if (hand === 'right' && armExtension > 0.8) {
      return PunchType.CROSS;
    } else {
      return PunchType.JAB;
    }
  }

  private calculateArmExtension(landmarks: PoseLandmark[], hand: 'left' | 'right'): number {
    const wristIdx = hand === 'left' ? POSE_LANDMARKS.LEFT_WRIST : POSE_LANDMARKS.RIGHT_WRIST;
    const elbowIdx = hand === 'left' ? POSE_LANDMARKS.LEFT_ELBOW : POSE_LANDMARKS.RIGHT_ELBOW;
    const shoulderIdx = hand === 'left' ? POSE_LANDMARKS.LEFT_SHOULDER : POSE_LANDMARKS.RIGHT_SHOULDER;
    
    const wrist = landmarks[wristIdx];
    const elbow = landmarks[elbowIdx];
    const shoulder = landmarks[shoulderIdx];
    
    // Calculate distances
    const wristToShoulder = MediaPipeService.calculateDistance(wrist, shoulder);
    const elbowToShoulder = MediaPipeService.calculateDistance(elbow, shoulder);
    const wristToElbow = MediaPipeService.calculateDistance(wrist, elbow);
    
    const armLength = elbowToShoulder + wristToElbow;
    
    return armLength > 0 ? wristToShoulder / armLength : 0;
  }

  private calculatePunchConfidence(velocity: VelocityVector, armExtension: number): number {
    // Calculate confidence based on velocity and arm extension
    const velocityScore = Math.min(velocity.magnitude / 100, 1.0);
    const extensionScore = armExtension;
    
    return (velocityScore * 0.6 + extensionScore * 0.4) * 100;
  }

  private detectCombo(): DetectedCombo | null {
    if (this.punchHistory.length < 2) return null;
    
    const recentPunches = this.punchHistory.slice(-5); // Check last 5 punches
    const currentTime = Date.now();
    
    for (const pattern of this.comboPatterns) {
      if (recentPunches.length < pattern.sequence.length) continue;
      
      // Check if recent punches match the pattern
      const candidatePunches = recentPunches.slice(-pattern.sequence.length);
      
      // Check punch types match
      const typesMatch = candidatePunches.every(
        (punch, idx) => punch.type === pattern.sequence[idx]
      );
      
      if (!typesMatch) continue;
      
      // Check timing constraints
      let validTiming = true;
      for (let i = 1; i < candidatePunches.length; i++) {
        const timeDiff = candidatePunches[i].timestamp - candidatePunches[i-1].timestamp;
        if (timeDiff < pattern.minTimeBetweenPunches || timeDiff > pattern.maxTimeBetweenPunches) {
          validTiming = false;
          break;
        }
      }
      
      if (validTiming) {
        // Calculate accuracy based on timing precision
        let totalTimeDiff = 0;
        let optimalTimeDiff = (pattern.minTimeBetweenPunches + pattern.maxTimeBetweenPunches) / 2;
        
        for (let i = 1; i < candidatePunches.length; i++) {
          const timeDiff = candidatePunches[i].timestamp - candidatePunches[i-1].timestamp;
          totalTimeDiff += Math.abs(timeDiff - optimalTimeDiff);
        }
        
        const avgTimingError = totalTimeDiff / (candidatePunches.length - 1);
        const accuracy = Math.max(0, 100 - (avgTimingError / optimalTimeDiff * 50));
        
        return {
          pattern,
          timestamp: currentTime,
          accuracy,
          punches: candidatePunches
        };
      }
    }
    
    return null;
  }

  private positionToLandmark(position: Position): PoseLandmark {
    return {
      x: position.x,
      y: position.y,
      z: position.z,
      visibility: 1.0
    };
  }

  private calculatePostureScore(landmarks: PoseLandmark[]): number {
    const leftShoulder = landmarks[POSE_LANDMARKS.LEFT_SHOULDER];
    const rightShoulder = landmarks[POSE_LANDMARKS.RIGHT_SHOULDER];
    const leftHip = landmarks[POSE_LANDMARKS.LEFT_HIP];
    const rightHip = landmarks[POSE_LANDMARKS.RIGHT_HIP];
    const nose = landmarks[POSE_LANDMARKS.NOSE];

    let score = 100;

    const shoulderTilt = Math.abs(leftShoulder.y - rightShoulder.y);
    score -= shoulderTilt * 50;

    const hipTilt = Math.abs(leftHip.y - rightHip.y);
    score -= hipTilt * 50;

    const shoulderCenter = {
      x: (leftShoulder.x + rightShoulder.x) / 2,
      y: (leftShoulder.y + rightShoulder.y) / 2,
      z: (leftShoulder.z + rightShoulder.z) / 2
    };
    const hipCenter = {
      x: (leftHip.x + rightHip.x) / 2,
      y: (leftHip.y + rightHip.y) / 2,
      z: (leftHip.z + rightHip.z) / 2
    };

    const spineDeviation = Math.abs(shoulderCenter.x - hipCenter.x);
    score -= spineDeviation * 100;

    const headForward = nose.z - shoulderCenter.z;
    if (headForward > 0.1) {
      score -= (headForward - 0.1) * 100;
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateGaitBalance(landmarks: PoseLandmark[]): number {
    const leftAnkle = landmarks[POSE_LANDMARKS.LEFT_ANKLE];
    const rightAnkle = landmarks[POSE_LANDMARKS.RIGHT_ANKLE];
    const leftHip = landmarks[POSE_LANDMARKS.LEFT_HIP];
    const rightHip = landmarks[POSE_LANDMARKS.RIGHT_HIP];

    let score = 100;

    const centerOfMass = {
      x: (leftHip.x + rightHip.x) / 2,
      y: (leftHip.y + rightHip.y) / 2,
      z: (leftHip.z + rightHip.z) / 2
    };

    const feetCenter = {
      x: (leftAnkle.x + rightAnkle.x) / 2,
      y: (leftAnkle.y + rightAnkle.y) / 2
    };

    const balanceDeviation = Math.abs(centerOfMass.x - feetCenter.x);
    score -= balanceDeviation * 100;

    const leftShoulder = landmarks[POSE_LANDMARKS.LEFT_SHOULDER];
    const rightShoulder = landmarks[POSE_LANDMARKS.RIGHT_SHOULDER];
    const shoulderWidth = Math.abs(rightShoulder.x - leftShoulder.x);
    const stanceWidth = Math.abs(rightAnkle.x - leftAnkle.x);
    
    const stanceRatio = stanceWidth / shoulderWidth;
    if (stanceRatio < 0.8 || stanceRatio > 1.2) {
      score -= Math.abs(1.0 - stanceRatio) * 20;
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateHeadMovement(landmarks: PoseLandmark[]): number {
    const recentFrames = this.frameHistory.slice(-30);
    if (recentFrames.length < 2) return 0;

    let totalMovement = 0;
    for (let i = 1; i < recentFrames.length; i++) {
      const prevNose = recentFrames[i-1].poseLandmarks[POSE_LANDMARKS.NOSE];
      const currNose = recentFrames[i].poseLandmarks[POSE_LANDMARKS.NOSE];
      const movement = MediaPipeService.calculateDistance(prevNose, currNose) * 200;
      totalMovement += movement;
    }

    return totalMovement / recentFrames.length;
  }

  private calculateFatigue(): number {
    if (this.frameHistory.length < 60) return 0;

    const initialFrames = this.frameHistory.slice(0, 30);
    const recentFrames = this.frameHistory.slice(-30);

    const initialPosture = initialFrames
      .map(f => f.metrics.postureScore || 0)
      .reduce((a, b) => a + b, 0) / initialFrames.length;
    
    const recentPosture = recentFrames
      .map(f => f.metrics.postureScore || 0)
      .reduce((a, b) => a + b, 0) / recentFrames.length;

    const sessionMinutes = (Date.now() - this.analysisStartTime) / 60000;
    const expectedFatigue = Math.min(sessionMinutes * 2, 50);

    const postureFatigue = Math.max(0, (initialPosture - recentPosture) * 2);
    
    return Math.min(100, expectedFatigue + postureFatigue);
  }

  private pruneHistories() {
    if (this.frameHistory.length > this.MAX_FRAME_HISTORY) {
      this.frameHistory = this.frameHistory.slice(-this.MAX_FRAME_HISTORY);
    }
    
    if (this.punchHistory.length > this.MAX_PUNCH_HISTORY) {
      this.punchHistory = this.punchHistory.slice(-this.MAX_PUNCH_HISTORY);
    }
    
    if (this.detectedCombos.length > this.MAX_COMBO_HISTORY) {
      this.detectedCombos = this.detectedCombos.slice(-this.MAX_COMBO_HISTORY);
    }
  }

  private getCurrentMetrics(): Metrics {
    const currentTime = Date.now();
    
    const recentPunches = this.punchHistory.filter(
      p => currentTime - p.timestamp < 60000
    );
    const punchRate = recentPunches.length;
    const punchCount = this.punchHistory.length;
    
    const punchVelocity = recentPunches.length > 0
      ? recentPunches.reduce((sum, p) => sum + p.velocity, 0) / recentPunches.length
      : 0;

    const latestFrame = this.frameHistory[this.frameHistory.length - 1];
    const postureScore = latestFrame?.metrics.postureScore || 95;
    const gaitBalance = latestFrame?.metrics.gaitBalance || 98;
    const headMovement = latestFrame?.metrics.headMovement || 0;

    const fatigue = this.calculateFatigue();

    return {
      punchRate,
      punchCount,
      punchVelocity,
      headMovement,
      postureScore,
      gaitBalance,
      fatigue
    };
  }

  // Public methods for accessing enhanced data
  getPunchHistory(): EnhancedPunchData[] {
    return [...this.punchHistory];
  }

  getComboHistory(): DetectedCombo[] {
    return [...this.detectedCombos];
  }

  getPunchTypeBreakdown(): Record<PunchType, number> {
    const breakdown: Record<PunchType, number> = {
      [PunchType.JAB]: 0,
      [PunchType.CROSS]: 0,
      [PunchType.HOOK]: 0,
      [PunchType.UPPERCUT]: 0
    };

    this.punchHistory.forEach(punch => {
      breakdown[punch.type]++;
    });

    return breakdown;
  }

  getSessionSummary(): string {
    const punchBreakdown = this.getPunchTypeBreakdown();
    const totalPunches = this.punchHistory.length;
    const totalCombos = this.detectedCombos.length;
    
    const dominantHand = this.punchHistory.filter(p => p.hand === 'right').length > 
                        this.punchHistory.filter(p => p.hand === 'left').length 
                        ? 'right' : 'left';
    
    const avgVelocity = totalPunches > 0
      ? this.punchHistory.reduce((sum, p) => sum + p.velocity, 0) / totalPunches
      : 0;

    const summary = [
      `Total punches thrown: ${totalPunches}`,
      `Dominant hand: ${dominantHand}`,
      `Average punch velocity: ${avgVelocity.toFixed(1)} pixels/frame`,
      `Jabs: ${punchBreakdown[PunchType.JAB]}`,
      `Crosses: ${punchBreakdown[PunchType.CROSS]}`,
      `Hooks: ${punchBreakdown[PunchType.HOOK]}`,
      `Uppercuts: ${punchBreakdown[PunchType.UPPERCUT]}`,
      `Combos completed: ${totalCombos}`
    ];

    if (this.detectedCombos.length > 0) {
      const bestCombo = this.detectedCombos.reduce((best, combo) => 
        combo.accuracy > best.accuracy ? combo : best
      );
      summary.push(`Best combo: ${bestCombo.pattern.name} (${bestCombo.accuracy.toFixed(1)}% accuracy)`);
    }

    return summary.join('\n');
  }

  // Calibration methods
  adjustSensitivity(increase: boolean, step: number = 5) {
    if (increase) {
      this.velocityThreshold = Math.max(5, this.velocityThreshold - step);
    } else {
      this.velocityThreshold = this.velocityThreshold + step;
    }
    console.log(`Punch detection sensitivity adjusted. New threshold: ${this.velocityThreshold}`);
  }

  setCalibrationData(data: Partial<typeof this.calibrationData>) {
    this.calibrationData = { ...this.calibrationData, ...data };
    console.log('Calibration data updated:', this.calibrationData);
  }

  reset() {
    this.frameHistory = [];
    this.punchHistory = [];
    this.detectedCombos = [];
    this.initializeHistories();
    this.lastPunchTime = { left: 0, right: 0 };
  }
}

export const enhancedVideoAnalysisService = new EnhancedVideoAnalysisService();

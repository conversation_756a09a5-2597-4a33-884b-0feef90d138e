interface VideoMetadata {
  id: string;
  filename: string;
  size: number;
  uploadedAt: Date;
  participantId: string;
  sessionType: string;
  url: string;
  syncStatus: 'pending' | 'syncing' | 'synced' | 'failed';
}

class VideoStorageService {
  private videos: Map<string, VideoMetadata> = new Map();
  private syncQueue: string[] = [];
  private isSyncing = false;
  private blobUrls: Map<string, string> = new Map(); // Track blob URLs for cleanup

  // Store video locally using IndexedDB for persistence
  async storeVideo(file: File, participantId: string, sessionType: string): Promise<VideoMetadata> {
    const id = `video_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const url = URL.createObjectURL(file);
    this.blobUrls.set(id, url); // Track for cleanup
    
    const metadata: VideoMetadata = {
      id,
      filename: file.name,
      size: file.size,
      uploadedAt: new Date(),
      participantId,
      sessionType,
      url,
      syncStatus: 'pending'
    };

    // Store metadata in memory
    this.videos.set(id, metadata);
    
    // Store in IndexedDB for persistence
    await this.saveToIndexedDB(id, file, metadata);
    
    // Add to sync queue
    this.syncQueue.push(id);
    this.processSyncQueue();
    
    return metadata;
  }

  private async saveToIndexedDB(id: string, file: File, metadata: VideoMetadata): Promise<void> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('CombatMirrorVideos', 1);
      
      request.onupgradeneeded = (event) => {
        const db = (event.target as IDBOpenDBRequest).result;
        if (!db.objectStoreNames.contains('videos')) {
          db.createObjectStore('videos', { keyPath: 'id' });
        }
      };
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['videos'], 'readwrite');
        const store = transaction.objectStore('videos');
        
        // Convert file to blob for storage
        const reader = new FileReader();
        reader.onloadend = () => {
          const videoData = {
            ...metadata,
            blob: reader.result
          };
          store.put(videoData);
          resolve();
        };
        reader.readAsArrayBuffer(file);
      };
      
      request.onerror = () => reject(request.error);
    });
  }

  async loadVideosFromIndexedDB(): Promise<VideoMetadata[]> {
    return new Promise((resolve, reject) => {
      const request = indexedDB.open('CombatMirrorVideos', 1);
      
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['videos'], 'readonly');
        const store = transaction.objectStore('videos');
        const getAllRequest = store.getAll();
        
        getAllRequest.onsuccess = () => {
          const videos = getAllRequest.result.map(video => {
            // Recreate blob URL from stored data
            if (video.blob) {
              const blob = new Blob([video.blob], { type: 'video/mp4' });
              const blobUrl = URL.createObjectURL(blob);
              video.url = blobUrl;
              this.blobUrls.set(video.id, blobUrl); // Track for cleanup
            }
            delete video.blob; // Remove blob from metadata
            return video as VideoMetadata;
          });
          
          // Restore to memory map
          videos.forEach(video => {
            this.videos.set(video.id, video);
            if (video.syncStatus === 'pending' || video.syncStatus === 'failed') {
              this.syncQueue.push(video.id);
            }
          });
          
          resolve(videos);
        };
        
        getAllRequest.onerror = () => reject(getAllRequest.error);
      };
      
      request.onerror = () => reject(request.error);
    });
  }

  private async processSyncQueue(): Promise<void> {
    if (this.isSyncing || this.syncQueue.length === 0) return;
    
    this.isSyncing = true;
    
    while (this.syncQueue.length > 0) {
      const videoId = this.syncQueue.shift()!;
      const video = this.videos.get(videoId);
      
      if (video) {
        await this.syncVideo(video);
      }
    }
    
    this.isSyncing = false;
  }

  private async syncVideo(video: VideoMetadata): Promise<void> {
    try {
      // Update status to syncing
      video.syncStatus = 'syncing';
      this.updateVideoMetadata(video);
      
      // Simulate sync to cloud storage (replace with actual cloud storage API)
      await this.simulateCloudSync(video);
      
      // Update status to synced
      video.syncStatus = 'synced';
      this.updateVideoMetadata(video);
      
    } catch (error) {
      console.error('Failed to sync video:', error);
      video.syncStatus = 'failed';
      this.updateVideoMetadata(video);
      
      // Re-add to queue for retry
      this.syncQueue.push(video.id);
    }
  }

  private async simulateCloudSync(video: VideoMetadata): Promise<void> {
    // Simulate network delay
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    // In production, this would upload to cloud storage (S3, Google Cloud Storage, etc.)
    console.log(`Syncing video ${video.id} to cloud...`);
    
    // Simulate 10% chance of failure for demo purposes
    if (Math.random() < 0.1) {
      throw new Error('Network error during sync');
    }
  }

  private async updateVideoMetadata(video: VideoMetadata): Promise<void> {
    // Update in IndexedDB
    const request = indexedDB.open('CombatMirrorVideos', 1);
    
    request.onsuccess = () => {
      const db = request.result;
      const transaction = db.transaction(['videos'], 'readwrite');
      const store = transaction.objectStore('videos');
      
      // Get the full record with blob
      const getRequest = store.get(video.id);
      getRequest.onsuccess = () => {
        const fullRecord = getRequest.result;
        if (fullRecord) {
          // Update metadata while preserving blob
          Object.assign(fullRecord, video);
          store.put(fullRecord);
        }
      };
    };
  }

  getVideos(): VideoMetadata[] {
    return Array.from(this.videos.values());
  }

  getVideosByParticipant(participantId: string): VideoMetadata[] {
    return this.getVideos().filter(v => v.participantId === participantId);
  }

  getVideoById(id: string): VideoMetadata | undefined {
    return this.videos.get(id);
  }

  async deleteVideo(id: string): Promise<void> {
    const video = this.videos.get(id);
    if (video) {
      // Revoke blob URL to free memory
      const blobUrl = this.blobUrls.get(id);
      if (blobUrl) {
        URL.revokeObjectURL(blobUrl);
        this.blobUrls.delete(id);
      }
      
      // Remove from memory
      this.videos.delete(id);
      
      // Remove from sync queue
      this.syncQueue = this.syncQueue.filter(videoId => videoId !== id);
      
      // Remove from IndexedDB
      const request = indexedDB.open('CombatMirrorVideos', 1);
      request.onsuccess = () => {
        const db = request.result;
        const transaction = db.transaction(['videos'], 'readwrite');
        const store = transaction.objectStore('videos');
        store.delete(id);
      };
    }
  }

  // Retry failed syncs
  retryFailedSyncs(): void {
    const failedVideos = this.getVideos().filter(v => v.syncStatus === 'failed');
    failedVideos.forEach(video => {
      if (!this.syncQueue.includes(video.id)) {
        this.syncQueue.push(video.id);
      }
    });
    this.processSyncQueue();
  }

  // Cleanup all blob URLs when service is destroyed
  cleanup(): void {
    this.blobUrls.forEach((url) => {
      URL.revokeObjectURL(url);
    });
    this.blobUrls.clear();
  }
}

// Export singleton instance
export const videoStorageService = new VideoStorageService();
export type { VideoMetadata };
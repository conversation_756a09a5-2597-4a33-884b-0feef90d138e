import { mediaPipeService, PoseResults, PoseLandmark, POSE_LANDMARKS } from './mediaPipeService';
import { Metrics } from '../types';

interface FrameAnalysis {
  timestamp: number;
  poseLandmarks: PoseLandmark[];
  metrics: Partial<Metrics>;
}

interface PunchData {
  timestamp: number;
  hand: 'left' | 'right';
  velocity: number;
  startPosition: PoseLandmark;
  endPosition: PoseLandmark;
}

export class VideoAnalysisService {
  private frameHistory: FrameAnalysis[] = [];
  private punchHistory: PunchData[] = [];
  private analysisStartTime: number = 0;
  private lastFrameTime: number = 0;
  private punchThresholdVelocity = 3.0; // m/s threshold for punch detection
  private frameRate = 30; // assumed frame rate

  // Previous frame landmarks for velocity calculations
  private previousLandmarks: PoseLandmark[] | null = null;

  // PERFORMANCE OPTIMIZATION: Configuration for memory management
  private readonly MAX_FRAME_HISTORY = 150; // 5 seconds at 30fps
  private readonly MAX_PUNCH_HISTORY = 100; // Keep last 100 punches
  private readonly FRAME_SKIP_INTERVAL = 2; // Process every 2nd frame for performance
  private frameSkipCounter = 0;

  startAnalysis() {
    this.frameHistory = [];
    this.punchHistory = [];
    this.analysisStartTime = Date.now();
    this.lastFrameTime = this.analysisStartTime;
    this.previousLandmarks = null;
  }

  async analyzeFrame(poseResults: PoseResults): Promise<Metrics> {
    const currentTime = Date.now();
    const deltaTime = (currentTime - this.lastFrameTime) / 1000; // Convert to seconds

    // PERFORMANCE OPTIMIZATION: Frame skipping for better performance
    this.frameSkipCounter++;
    if (this.frameSkipCounter % this.FRAME_SKIP_INTERVAL !== 0) {
      return this.getCurrentMetrics();
    }

    if (!poseResults.poseLandmarks || poseResults.poseLandmarks.length === 0) {
      return this.getCurrentMetrics();
    }

    const landmarks = poseResults.poseLandmarks;

    // Calculate frame-specific metrics
    const frameMetrics: Partial<Metrics> = {
      postureScore: this.calculatePostureScore(landmarks),
      gaitBalance: this.calculateGaitBalance(landmarks),
      headMovement: this.calculateHeadMovement(landmarks, deltaTime),
    };

    // Detect punches and calculate velocity
    if (this.previousLandmarks) {
      const punchData = this.detectPunches(this.previousLandmarks, landmarks, deltaTime);
      if (punchData) {
        this.punchHistory.push(punchData);
        frameMetrics.punchVelocity = punchData.velocity;

        // MEMORY OPTIMIZATION: Limit punch history size
        if (this.punchHistory.length > this.MAX_PUNCH_HISTORY) {
          this.punchHistory.shift();
        }
      }
    }

    // Store frame analysis
    this.frameHistory.push({
      timestamp: currentTime,
      poseLandmarks: landmarks,
      metrics: frameMetrics
    });

    // MEMORY OPTIMIZATION: Use configured max instead of hardcoded value
    if (this.frameHistory.length > this.MAX_FRAME_HISTORY) {
      this.frameHistory.shift();
    }

    this.previousLandmarks = landmarks;
    this.lastFrameTime = currentTime;

    return this.getCurrentMetrics();
  }

  private calculatePostureScore(landmarks: PoseLandmark[]): number {
    // Posture score based on:
    // 1. Shoulder alignment (should be level)
    // 2. Hip alignment (should be level)
    // 3. Spine straightness (shoulders-hips alignment)
    // 4. Head position (not too forward)

    const leftShoulder = landmarks[POSE_LANDMARKS.LEFT_SHOULDER];
    const rightShoulder = landmarks[POSE_LANDMARKS.RIGHT_SHOULDER];
    const leftHip = landmarks[POSE_LANDMARKS.LEFT_HIP];
    const rightHip = landmarks[POSE_LANDMARKS.RIGHT_HIP];
    const nose = landmarks[POSE_LANDMARKS.NOSE];

    let score = 100;

    // Check shoulder level (penalize tilting)
    const shoulderTilt = Math.abs(leftShoulder.y - rightShoulder.y);
    score -= shoulderTilt * 50; // Deduct up to 15 points for shoulder tilt

    // Check hip level
    const hipTilt = Math.abs(leftHip.y - rightHip.y);
    score -= hipTilt * 50; // Deduct up to 15 points for hip tilt

    // Check spine alignment (shoulders center to hips center)
    const shoulderCenter = {
      x: (leftShoulder.x + rightShoulder.x) / 2,
      y: (leftShoulder.y + rightShoulder.y) / 2,
      z: (leftShoulder.z + rightShoulder.z) / 2
    };
    const hipCenter = {
      x: (leftHip.x + rightHip.x) / 2,
      y: (leftHip.y + rightHip.y) / 2,
      z: (leftHip.z + rightHip.z) / 2
    };

    const spineDeviation = Math.abs(shoulderCenter.x - hipCenter.x);
    score -= spineDeviation * 100; // Deduct up to 20 points for spine misalignment

    // Check head forward position
    const headForward = nose.z - shoulderCenter.z;
    if (headForward > 0.1) { // Head too far forward
      score -= (headForward - 0.1) * 100;
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateGaitBalance(landmarks: PoseLandmark[]): number {
    // Balance score based on:
    // 1. Center of mass stability
    // 2. Foot positioning
    // 3. Weight distribution

    const leftAnkle = landmarks[POSE_LANDMARKS.LEFT_ANKLE];
    const rightAnkle = landmarks[POSE_LANDMARKS.RIGHT_ANKLE];
    const leftHip = landmarks[POSE_LANDMARKS.LEFT_HIP];
    const rightHip = landmarks[POSE_LANDMARKS.RIGHT_HIP];

    let score = 100;

    // Calculate center of mass (simplified using hip center)
    const centerOfMass = {
      x: (leftHip.x + rightHip.x) / 2,
      y: (leftHip.y + rightHip.y) / 2,
      z: (leftHip.z + rightHip.z) / 2
    };

    // Check if center of mass is between feet
    const feetCenter = {
      x: (leftAnkle.x + rightAnkle.x) / 2,
      y: (leftAnkle.y + rightAnkle.y) / 2
    };

    const balanceDeviation = Math.abs(centerOfMass.x - feetCenter.x);
    score -= balanceDeviation * 100; // Deduct for off-balance

    // Check stance width (should be shoulder-width)
    const leftShoulder = landmarks[POSE_LANDMARKS.LEFT_SHOULDER];
    const rightShoulder = landmarks[POSE_LANDMARKS.RIGHT_SHOULDER];
    const shoulderWidth = Math.abs(rightShoulder.x - leftShoulder.x);
    const stanceWidth = Math.abs(rightAnkle.x - leftAnkle.x);
    
    const stanceRatio = stanceWidth / shoulderWidth;
    if (stanceRatio < 0.8 || stanceRatio > 1.2) {
      score -= Math.abs(1.0 - stanceRatio) * 20;
    }

    return Math.max(0, Math.min(100, score));
  }

  private calculateHeadMovement(landmarks: PoseLandmark[], deltaTime: number): number {
    if (!this.previousLandmarks) return 0;

    const currentNose = landmarks[POSE_LANDMARKS.NOSE];
    const previousNose = this.previousLandmarks[POSE_LANDMARKS.NOSE];

    // Calculate movement in cm (assuming normalized coordinates 0-1 represent ~2m span)
    const movement = mediaPipeService.calculateDistance(previousNose, currentNose) * 200; // Convert to cm

    // Store recent head movements for averaging
    const recentMovements = this.frameHistory
      .slice(-30) // Last 1 second at 30fps
      .map(frame => frame.metrics.headMovement || 0);
    
    recentMovements.push(movement);
    
    // Return average movement
    return recentMovements.reduce((a, b) => a + b, 0) / recentMovements.length;
  }

  private detectPunches(
    previousLandmarks: PoseLandmark[],
    currentLandmarks: PoseLandmark[],
    deltaTime: number
  ): PunchData | null {
    // Check both hands for punch motion
    const hands = [
      { side: 'left' as const, wrist: POSE_LANDMARKS.LEFT_WRIST, elbow: POSE_LANDMARKS.LEFT_ELBOW },
      { side: 'right' as const, wrist: POSE_LANDMARKS.RIGHT_WRIST, elbow: POSE_LANDMARKS.RIGHT_ELBOW }
    ];

    for (const hand of hands) {
      const prevWrist = previousLandmarks[hand.wrist];
      const currWrist = currentLandmarks[hand.wrist];
      const currElbow = currentLandmarks[hand.elbow];

      // Calculate wrist velocity
      const velocity = mediaPipeService.calculateVelocity(prevWrist, currWrist, deltaTime);

      // Check if velocity exceeds punch threshold and arm is extended
      if (velocity > this.punchThresholdVelocity) {
        const armExtension = mediaPipeService.calculateDistance(currWrist, currElbow);
        
        // Check if arm is reasonably extended (not just flailing)
        if (armExtension > 0.2) { // Normalized units
          return {
            timestamp: Date.now(),
            hand: hand.side,
            velocity: velocity * 10, // Convert to m/s with scaling factor
            startPosition: prevWrist,
            endPosition: currWrist
          };
        }
      }
    }

    return null;
  }

  private getCurrentMetrics(): Metrics {
    const currentTime = Date.now();
    const sessionDuration = (currentTime - this.analysisStartTime) / 1000; // seconds

    // Calculate punch metrics
    const recentPunches = this.punchHistory.filter(
      p => currentTime - p.timestamp < 60000 // Last minute
    );
    const punchRate = recentPunches.length; // punches per minute
    const punchCount = this.punchHistory.length;
    
    // Average punch velocity
    const punchVelocity = recentPunches.length > 0
      ? recentPunches.reduce((sum, p) => sum + p.velocity, 0) / recentPunches.length
      : 0;

    // Get latest frame metrics
    const latestFrame = this.frameHistory[this.frameHistory.length - 1];
    const postureScore = latestFrame?.metrics.postureScore || 95;
    const gaitBalance = latestFrame?.metrics.gaitBalance || 98;
    const headMovement = latestFrame?.metrics.headMovement || 0;

    // Calculate fatigue based on performance degradation
    const fatigue = this.calculateFatigue();

    return {
      punchRate,
      punchCount,
      punchVelocity,
      headMovement,
      postureScore,
      gaitBalance,
      fatigue
    };
  }

  private calculateFatigue(): number {
    if (this.frameHistory.length < 60) return 0; // Need at least 2 seconds of data

    // Compare recent performance to initial performance
    const initialFrames = this.frameHistory.slice(0, 30);
    const recentFrames = this.frameHistory.slice(-30);

    // Calculate average posture scores
    const initialPosture = initialFrames
      .map(f => f.metrics.postureScore || 0)
      .reduce((a, b) => a + b, 0) / initialFrames.length;
    
    const recentPosture = recentFrames
      .map(f => f.metrics.postureScore || 0)
      .reduce((a, b) => a + b, 0) / recentFrames.length;

    // Calculate punch rate decline
    const sessionMinutes = (Date.now() - this.analysisStartTime) / 60000;
    const expectedFatigue = Math.min(sessionMinutes * 2, 50); // Linear fatigue model

    // Combine posture degradation and time-based fatigue
    const postureFatigue = Math.max(0, (initialPosture - recentPosture) * 2);
    
    return Math.min(100, expectedFatigue + postureFatigue);
  }

  reset() {
    this.frameHistory = [];
    this.punchHistory = [];
    this.previousLandmarks = null;
  }
}

export const videoAnalysisService = new VideoAnalysisService();
// Copyparty File Server Integration Service
import { VideoMetadata } from './videoStorageService';

// Copyparty server configuration
const COPYPARTY_URL = import.meta.env.VITE_COPYPARTY_URL || 'http://localhost:3923';
const UPLOAD_PATH = '/uploads';
const RECORDINGS_PATH = '/recordings';
const EXPORTS_PATH = '/exports';

interface CopypartyUploadResponse {
  url: string;
  size: number;
  hash?: string;
  timestamp: number;
}

class CopypartyService {
  private baseUrl: string;

  constructor() {
    this.baseUrl = COPYPARTY_URL;
  }

  /**
   * Upload a video file to Copyparty
   */
  async uploadVideo(
    file: File,
    participantId: string,
    sessionType: string
  ): Promise<VideoMetadata> {
    const formData = new FormData();
    
    // Create a unique filename with metadata
    const timestamp = Date.now();
    const filename = `${participantId}_${sessionType.replace(/\s+/g, '_')}_${timestamp}_${file.name}`;
    
    // Rename the file for better organization
    const renamedFile = new File([file], filename, { type: file.type });
    formData.append('file', renamedFile);

    try {
      // Upload to Copyparty
      const response = await fetch(`${this.baseUrl}${UPLOAD_PATH}/`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`);
      }

      // Get the file URL from response
      const fileUrl = `${this.baseUrl}${UPLOAD_PATH}/${filename}`;

      // Create metadata
      const metadata: VideoMetadata = {
        id: `cp_${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
        filename: filename,
        size: file.size,
        uploadedAt: new Date(),
        participantId,
        sessionType,
        url: fileUrl,
        syncStatus: 'synced', // Copyparty handles the storage
      };

      // Store metadata in localStorage for quick access
      this.saveMetadataLocally(metadata);

      return metadata;
    } catch (error) {
      console.error('Copyparty upload error:', error);
      throw error;
    }
  }

  /**
   * Upload a recording from LiveKit
   */
  async uploadRecording(
    blob: Blob,
    participantId: string,
    sessionId: string
  ): Promise<string> {
    const timestamp = Date.now();
    const filename = `recording_${participantId}_${sessionId}_${timestamp}.webm`;
    
    const file = new File([blob], filename, { type: 'video/webm' });
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${this.baseUrl}${RECORDINGS_PATH}/`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Recording upload failed: ${response.statusText}`);
      }

      return `${this.baseUrl}${RECORDINGS_PATH}/${filename}`;
    } catch (error) {
      console.error('Recording upload error:', error);
      throw error;
    }
  }

  /**
   * Export analysis report as PDF or HTML
   */
  async exportReport(
    content: string,
    participantId: string,
    format: 'html' | 'pdf' = 'html'
  ): Promise<string> {
    const timestamp = Date.now();
    const filename = `report_${participantId}_${timestamp}.${format}`;
    
    const blob = new Blob([content], { 
      type: format === 'html' ? 'text/html' : 'application/pdf' 
    });
    
    const file = new File([blob], filename, { type: blob.type });
    const formData = new FormData();
    formData.append('file', file);

    try {
      const response = await fetch(`${this.baseUrl}${EXPORTS_PATH}/`, {
        method: 'POST',
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Report export failed: ${response.statusText}`);
      }

      return `${this.baseUrl}${EXPORTS_PATH}/${filename}`;
    } catch (error) {
      console.error('Report export error:', error);
      throw error;
    }
  }

  /**
   * List all videos in a directory
   */
  async listVideos(path: string = UPLOAD_PATH): Promise<VideoMetadata[]> {
    try {
      const response = await fetch(`${this.baseUrl}${path}/?j`, {
        headers: {
          'Accept': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error(`Failed to list videos: ${response.statusText}`);
      }

      const data = await response.json();
      
      // Parse Copyparty's JSON response
      const videos: VideoMetadata[] = data.files
        ?.filter((file: any) => file.name.match(/\.(mp4|webm|mov|avi)$/i))
        ?.map((file: any) => {
          // Parse metadata from filename
          const parts = file.name.split('_');
          const participantId = parts[0] || 'Unknown';
          
          return {
            id: `cp_${file.hash || Date.now()}`,
            filename: file.name,
            size: file.size,
            uploadedAt: new Date(file.ts * 1000), // Convert Unix timestamp
            participantId,
            sessionType: 'Unknown',
            url: `${this.baseUrl}${path}/${file.name}`,
            syncStatus: 'synced' as const,
          };
        }) || [];

      return videos;
    } catch (error) {
      console.error('Failed to list videos:', error);
      return this.getMetadataFromLocal(); // Fallback to local storage
    }
  }

  /**
   * Delete a video from Copyparty
   */
  async deleteVideo(url: string): Promise<void> {
    try {
      const response = await fetch(url, {
        method: 'DELETE',
      });

      if (!response.ok && response.status !== 404) {
        throw new Error(`Delete failed: ${response.statusText}`);
      }
    } catch (error) {
      console.error('Failed to delete video:', error);
      throw error;
    }
  }

  /**
   * Generate a temporary share link
   */
  async createShareLink(
    url: string,
    expiryHours: number = 24
  ): Promise<string> {
    // Copyparty supports temporary links with file keys
    // This would need server-side configuration
    const shareParams = new URLSearchParams({
      expire: String(expiryHours * 3600), // Convert to seconds
      key: this.generateFileKey(),
    });

    return `${url}?${shareParams.toString()}`;
  }

  /**
   * Check if Copyparty server is available
   */
  async checkHealth(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/`, {
        method: 'HEAD',
      });
      return response.ok;
    } catch (error) {
      console.error('Copyparty health check failed:', error);
      return false;
    }
  }

  /**
   * Upload with progress tracking
   */
  async uploadWithProgress(
    file: File,
    participantId: string,
    sessionType: string,
    onProgress?: (percent: number) => void
  ): Promise<VideoMetadata> {
    return new Promise((resolve, reject) => {
      const xhr = new XMLHttpRequest();
      const timestamp = Date.now();
      const filename = `${participantId}_${sessionType.replace(/\s+/g, '_')}_${timestamp}_${file.name}`;
      
      const formData = new FormData();
      const renamedFile = new File([file], filename, { type: file.type });
      formData.append('file', renamedFile);

      // Track upload progress
      xhr.upload.addEventListener('progress', (e) => {
        if (e.lengthComputable && onProgress) {
          const percentComplete = (e.loaded / e.total) * 100;
          onProgress(percentComplete);
        }
      });

      xhr.addEventListener('load', () => {
        if (xhr.status === 200 || xhr.status === 201) {
          const metadata: VideoMetadata = {
            id: `cp_${timestamp}_${Math.random().toString(36).substr(2, 9)}`,
            filename: filename,
            size: file.size,
            uploadedAt: new Date(),
            participantId,
            sessionType,
            url: `${this.baseUrl}${UPLOAD_PATH}/${filename}`,
            syncStatus: 'synced',
          };
          
          this.saveMetadataLocally(metadata);
          resolve(metadata);
        } else {
          reject(new Error(`Upload failed with status ${xhr.status}`));
        }
      });

      xhr.addEventListener('error', () => {
        reject(new Error('Upload failed'));
      });

      xhr.open('POST', `${this.baseUrl}${UPLOAD_PATH}/`);
      xhr.send(formData);
    });
  }

  // Local storage helpers
  private saveMetadataLocally(metadata: VideoMetadata): void {
    try {
      const stored = localStorage.getItem('copyparty_videos');
      const videos = stored ? JSON.parse(stored) : [];
      videos.push(metadata);
      localStorage.setItem('copyparty_videos', JSON.stringify(videos));
    } catch (error) {
      console.error('Failed to save metadata locally:', error);
    }
  }

  private getMetadataFromLocal(): VideoMetadata[] {
    try {
      const stored = localStorage.getItem('copyparty_videos');
      return stored ? JSON.parse(stored) : [];
    } catch (error) {
      console.error('Failed to get local metadata:', error);
      return [];
    }
  }

  private generateFileKey(): string {
    return Math.random().toString(36).substring(2, 15);
  }
}

// Export singleton instance
export const copypartyService = new CopypartyService();
export default copypartyService;
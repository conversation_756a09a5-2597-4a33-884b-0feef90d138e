<!DOCTYPE html>
<html>
<head>
    <title>Copyparty Integration Test</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        button { padding: 10px 20px; margin: 5px; cursor: pointer; }
        .success { color: green; }
        .error { color: red; }
        #progress { width: 300px; height: 20px; border: 1px solid #ccc; }
        #progressBar { height: 100%; background: #4CAF50; width: 0%; }
    </style>
</head>
<body>
    <h1>Combat Mirror - Copyparty File Server Test</h1>
    
    <div class="section">
        <h2>1. Test Copyparty Connection</h2>
        <p>Copyparty URL: <span id="copypartyUrl">http://localhost:3923</span></p>
        <button onclick="testConnection()">Test Connection</button>
        <div id="connectionResult"></div>
    </div>
    
    <div class="section">
        <h2>2. Upload Video Test</h2>
        <input type="file" id="videoFile" accept="video/*">
        <button onclick="uploadVideo()">Upload to Copyparty</button>
        <div id="progress"><div id="progressBar"></div></div>
        <div id="uploadResult"></div>
    </div>
    
    <div class="section">
        <h2>3. List Videos</h2>
        <button onclick="listVideos()">List Videos in /uploads</button>
        <div id="videoList"></div>
    </div>
    
    <script>
        const COPYPARTY_URL = 'http://localhost:3923';
        
        async function testConnection() {
            const resultDiv = document.getElementById('connectionResult');
            try {
                // Test if Copyparty is accessible
                const response = await fetch(COPYPARTY_URL, { mode: 'no-cors' });
                resultDiv.innerHTML = '<span class="success">✓ Copyparty server is accessible at ' + COPYPARTY_URL + '</span>';
                
                // Alternative: If Copyparty is not running, show instructions
                resultDiv.innerHTML += '<br><br><strong>To start Copyparty:</strong><br>' +
                    '1. Install: <code>pip install copyparty</code><br>' +
                    '2. Run: <code>copyparty -i :: -p 3923 --rw uploads,recordings,exports --cors</code>';
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">✗ Cannot connect to Copyparty at ' + COPYPARTY_URL + '</span>';
            }
        }
        
        async function uploadVideo() {
            const fileInput = document.getElementById('videoFile');
            const file = fileInput.files[0];
            const resultDiv = document.getElementById('uploadResult');
            
            if (!file) {
                resultDiv.innerHTML = '<span class="error">Please select a video file</span>';
                return;
            }
            
            const formData = new FormData();
            const timestamp = Date.now();
            const filename = `test_${timestamp}_${file.name}`;
            formData.append('file', file, filename);
            
            const xhr = new XMLHttpRequest();
            
            // Track upload progress
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    document.getElementById('progressBar').style.width = percentComplete + '%';
                }
            });
            
            xhr.addEventListener('load', () => {
                if (xhr.status === 200 || xhr.status === 201) {
                    resultDiv.innerHTML = '<span class="success">✓ Video uploaded successfully!<br>' +
                        'URL: ' + COPYPARTY_URL + '/uploads/' + filename + '</span>';
                } else {
                    resultDiv.innerHTML = '<span class="error">✗ Upload failed with status ' + xhr.status + '</span>';
                }
            });
            
            xhr.addEventListener('error', () => {
                resultDiv.innerHTML = '<span class="error">✗ Upload failed - Copyparty may not be running</span>';
            });
            
            xhr.open('POST', COPYPARTY_URL + '/uploads/');
            xhr.send(formData);
        }
        
        async function listVideos() {
            const resultDiv = document.getElementById('videoList');
            try {
                const response = await fetch(COPYPARTY_URL + '/uploads/?j');
                if (response.ok) {
                    const data = await response.json();
                    if (data.files && data.files.length > 0) {
                        let html = '<h3>Videos in /uploads:</h3><ul>';
                        data.files.forEach(file => {
                            if (file.name.match(/\.(mp4|webm|mov|avi)$/i)) {
                                html += '<li>' + file.name + ' (' + (file.size / 1024 / 1024).toFixed(2) + ' MB)</li>';
                            }
                        });
                        html += '</ul>';
                        resultDiv.innerHTML = html;
                    } else {
                        resultDiv.innerHTML = '<span>No videos found in /uploads</span>';
                    }
                } else {
                    resultDiv.innerHTML = '<span class="error">Failed to list videos</span>';
                }
            } catch (error) {
                resultDiv.innerHTML = '<span class="error">✗ Cannot connect to Copyparty</span>';
            }
        }
        
        // Test connection on load
        window.onload = testConnection;
    </script>
</body>
</html>
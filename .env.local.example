# Combat Mirror System - Local Development Environment
# Copy this file to .env.local and update with your values

# REQUIRED: Gemini API Key for AI Analysis
GEMINI_API_KEY=your_gemini_api_key_here

# LiveKit Configuration
LIVEKIT_API_KEY=APIfightmaster
LIVEKIT_API_SECRET=xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ

# Service URLs (using network IP instead of localhost)
VITE_API_URL=http://************:3001
VITE_LIVEKIT_URL=ws://************:7880

# Backend Configuration
NODE_ENV=development
PORT=3001
LOG_LEVEL=debug
CORS_DEBUG=true

# Frontend Configuration
VITE_DEBUG_MODE=true
VITE_ENABLE_PROFILER=true

# Redis Configuration
REDIS_URL=redis://localhost:6379

# Optional: Monitoring
ENABLE_MONITORING=true
ENABLE_TRACING=true

# Optional: Webhook Testing
WEBHOOK_URL=http://localhost:3001/api/webhooks/test
WEBHOOK_SECRET=test_webhook_secret_123
<!DOCTYPE html>
<html>
<head>
    <title>Combat Mirror Functionality Test</title>
</head>
<body>
    <h1>Combat Mirror System - Functionality Test</h1>
    
    <h2>1. Backend API Tests</h2>
    <button onclick="testHealth()">Test Health Endpoint</button>
    <button onclick="testAnalysis()">Test Analysis Endpoint</button>
    <div id="api-results"></div>
    
    <h2>2. Frontend Tests</h2>
    <button onclick="testFrontend()">Test Frontend Loading</button>
    <div id="frontend-results"></div>
    
    <script>
        async function testHealth() {
            try {
                const response = await fetch('http://localhost:3001/api/health');
                const data = await response.json();
                document.getElementById('api-results').innerHTML = 
                    '<pre>Health Check: ' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('api-results').innerHTML = 
                    '<pre>Error: ' + error.message + '</pre>';
            }
        }
        
        async function testAnalysis() {
            try {
                const response = await fetch('http://localhost:3001/api/analyze', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        metrics: {
                            punchRate: 45,
                            punchCount: 120,
                            punchVelocity: 7.5,
                            headMovement: 2.3,
                            postureScore: 85,
                            gaitBalance: 92,
                            fatigue: 15
                        },
                        participantId: 'TEST001',
                        sessionType: 'Training Session (15 mins)'
                    })
                });
                const data = await response.json();
                document.getElementById('api-results').innerHTML = 
                    '<pre>Analysis: ' + JSON.stringify(data, null, 2) + '</pre>';
            } catch (error) {
                document.getElementById('api-results').innerHTML = 
                    '<pre>Error: ' + error.message + '</pre>';
            }
        }
        
        async function testFrontend() {
            try {
                const response = await fetch('https://localhost:4173');
                const text = await response.text();
                const hasReact = text.includes('root');
                const hasScript = text.includes('script');
                document.getElementById('frontend-results').innerHTML = 
                    '<pre>Frontend Status:\n' +
                    '- HTML Loaded: Yes\n' +
                    '- React Root: ' + (hasReact ? 'Found' : 'Missing') + '\n' +
                    '- JavaScript: ' + (hasScript ? 'Found' : 'Missing') + '\n' +
                    '- URL: https://localhost:4173</pre>';
            } catch (error) {
                document.getElementById('frontend-results').innerHTML = 
                    '<pre>Error: ' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
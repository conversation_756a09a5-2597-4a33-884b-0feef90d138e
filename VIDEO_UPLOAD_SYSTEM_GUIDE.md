# Combat Mirror Video Upload & Library System Guide

## 🎯 Overview

The Combat Mirror System now includes a comprehensive video upload and management solution with:

- **Mobile App**: Real-time video recording and upload capability
- **CopyParty Integration**: High-performance file server for video storage
- **Web Interface**: Video library with player and analysis tools
- **Automated Processing**: Video analysis triggers on upload

## 📱 Mobile Client Features

### Real-Time Video Recording
- **Camera Control**: Front/back camera switching
- **HD Recording**: 1080p video capture up to 5 minutes
- **Live Preview**: Real-time camera feed with controls
- **Recording Indicators**: Visual feedback during recording

### Upload Capabilities
- **Direct Upload**: Record and upload videos instantly
- **File Selection**: Upload existing videos from device
- **Progress Tracking**: Real-time upload progress with percentage
- **Resume Support**: Automatic resume on connection issues
- **Category Management**: Organize uploads by type

### Mobile UI Components
- **Navigation**: Home screen with training options
- **Camera Interface**: Full-screen recording with overlay controls
- **Upload History**: Track all uploaded videos with metadata
- **Error Handling**: User-friendly error messages and retry options

## 🖥️ Server System Features

### Video Library Interface
- **Grid View**: Thumbnail grid of all videos with metadata
- **Category Filtering**: Filter by uploads, recordings, exports
- **Smart Sorting**: Sort by date, name, size with toggle order
- **Search & Filter**: Find videos quickly across categories
- **Batch Operations**: Multi-select for bulk actions

### Video Player
- **Full Controls**: Play, pause, seek, volume, playback speed
- **Fullscreen Mode**: Immersive viewing experience
- **Keyboard Shortcuts**: Space (play/pause), arrows (seek), F (fullscreen)
- **Download Support**: Direct download links for videos
- **Analysis Integration**: One-click video analysis from player

### CopyParty Integration
- **Multi-Protocol**: HTTP, WebDAV, FTP, TFTP support
- **High Performance**: Parallel chunk uploads, 5x faster than alternatives
- **File Deduplication**: Automatic duplicate detection and storage optimization
- **Resume Support**: Automatic upload resume on failures
- **Health Monitoring**: Real-time server status checking

## 🏗️ Architecture

### Mobile App Structure
```
combat-mirror-mobile/
├── src/
│   ├── screens/
│   │   └── VideoRecordingScreen.tsx    # Main recording interface
│   └── services/
│       └── VideoUploadService.ts       # CopyParty API integration
├── App.tsx                             # Navigation and home screen
└── package.json                        # Dependencies with Expo Camera
```

### Server Components
```
combat-mirror-system/
├── src/components/
│   ├── VideoLibrary.tsx               # Main video library interface
│   ├── VideoPlayer.tsx                # Full-featured video player
│   ├── VideoLibrary.css               # Library styling
│   └── VideoPlayer.css                # Player styling
├── server/
│   ├── index.js                       # API endpoints for file management
│   └── services/
│       └── fileServerService.js       # CopyParty integration service
└── docker-compose.yml                 # CopyParty container configuration
```

## 🚀 Installation & Setup

### 1. Server Setup

#### Start CopyParty File Server
```bash
cd combat-mirror-system
docker-compose up copyparty -d
```

#### Install Server Dependencies
```bash
cd server
npm install
```

#### Start Combat Mirror API
```bash
npm run dev
```

### 2. Mobile App Setup

#### Install Dependencies
```bash
cd combat-mirror-mobile
npm install
```

#### Configure Server URL
Update `VideoUploadService.ts` with your server IP:
```typescript
this.baseUrl = 'http://YOUR_SERVER_IP:3001';
```

#### Start Development
```bash
npm start
```

#### Build for iOS Production
```bash
npm run build:ios:production
```

### 3. Web Interface

Open your browser to:
- **Combat Mirror Dashboard**: http://localhost:5176
- **Video Library**: Click "📹 Video Library" tab
- **CopyParty Direct Access**: http://localhost:3923

## 📋 Usage Workflow

### Recording & Upload (Mobile)
1. Open Combat Mirror mobile app
2. Tap "📹 Record & Upload Videos"
3. Grant camera permissions
4. Record video or select existing file
5. Upload automatically syncs to server
6. View upload progress and history

### Video Management (Web)
1. Open Combat Mirror web interface
2. Switch to "📹 Video Library" tab
3. Browse uploaded videos by category
4. Click video to open in full player
5. Use "🔍 Analyze" for AI analysis
6. Download or delete videos as needed

### Analysis Workflow
1. Select video from library
2. Click "🔍 Analyze" button
3. AI generates comprehensive biomechanical report
4. View results in analysis modal
5. Export or save analysis reports

## 🔧 Configuration

### CopyParty Settings
Located in `copyparty-config/copyparty.conf`:
```ini
[global]
e2dsa    # file deduplication
e2ts     # file indexing
j        # enable uploads
cors     # CORS for API access

[/uploads]
accs: rw    # read/write access
flags: dupe move    # allow duplicates & moving
```

### Docker Configuration
```yaml
copyparty:
  image: ghcr.io/9001/copyparty:latest
  ports:
    - "3923:3923"  # HTTP/WebDAV
    - "3990:3990"  # FTP
    - "1033:1033"  # TFTP
  volumes:
    - ./uploads:/srv/uploads
    - ./recordings:/srv/recordings
    - ./exports:/srv/exports
```

### Mobile App Configuration
Update network IP in `VideoUploadService.ts`:
```typescript
// Replace with your server's network IP
private baseUrl = 'http://************:3001';
```

## 🎯 Key Features

### Performance Optimizations
- **Parallel Uploads**: Multiple chunks uploaded simultaneously
- **Resume Support**: Automatic recovery from interruptions
- **Compression**: Efficient video encoding for faster uploads
- **Caching**: Smart caching for better user experience

### User Experience
- **Progress Indicators**: Real-time upload progress
- **Error Recovery**: Automatic retry on failures
- **Offline Support**: Queue uploads when offline
- **Responsive Design**: Works on all devices

### Security Features
- **File Validation**: Server-side file type checking
- **Size Limits**: Configurable upload size restrictions
- **Access Control**: Category-based permissions
- **Secure Transfer**: HTTPS support for production

## 🔍 Testing

### Test CopyParty Integration
```bash
node test-copyparty.js
```

### Verify Mobile Upload
1. Record test video on mobile
2. Check upload progress completes
3. Verify video appears in web library
4. Test video playback and analysis

### End-to-End Workflow
1. **Mobile**: Record → Upload → Confirm success
2. **Server**: Verify file in CopyParty at http://localhost:3923
3. **Web**: View in library → Play → Analyze → Download

## 📈 Monitoring

### Server Logs
```bash
# View API server logs
tail -f logs/combat-mirror-api.log

# Check CopyParty logs
docker logs combat-mirror-fileserver
```

### Health Checks
- **API Health**: GET /api/health
- **File Server Health**: GET /api/files/health
- **CopyParty Direct**: http://localhost:3923

## 🚨 Troubleshooting

### Common Issues

#### Mobile Upload Fails
- Check network connectivity
- Verify server IP address in VideoUploadService
- Ensure CopyParty container is running
- Check server logs for errors

#### Videos Not Appearing in Library
- Verify CopyParty integration is working
- Check file permissions on upload directories
- Restart API server if needed
- Clear browser cache

#### Video Player Issues
- Ensure video format is supported (MP4 recommended)
- Check browser video codec support
- Verify file is accessible via direct URL
- Try different video quality settings

### Performance Tuning

#### Large File Uploads
- Increase nginx/proxy timeout settings
- Configure CopyParty chunk size settings
- Optimize network settings for video streaming
- Consider video compression settings

## 🏆 Success Metrics

- **Upload Success Rate**: >95% successful uploads
- **Video Processing Time**: <30 seconds for analysis
- **User Experience**: <5 second load times
- **Storage Efficiency**: Deduplication saves 20-40% space
- **Mobile Performance**: 30+ FPS recording, <10MB memory usage

---

**The Combat Mirror Video Upload & Library System provides a complete end-to-end solution for video-based combat sports analysis, from mobile recording to AI-powered insights.**
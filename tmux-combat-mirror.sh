#!/bin/bash

# Combat Mirror Tmux Manager

case "$1" in
    attach|a)
        echo "Attaching to Combat Mirror tmux session..."
        tmux attach -t combat-mirror
        ;;
    
    status|s)
        echo "Combat Mirror Services Status:"
        echo "=============================="
        tmux list-windows -t combat-mirror
        echo ""
        echo "Service Health:"
        echo "Backend (5557): $(curl -s http://localhost:5557/api/health | jq -r '.status' 2>/dev/null || echo 'offline')"
        echo "Copyparty (5923): $(curl -sI http://localhost:5923 2>/dev/null | grep -q '200' && echo 'online' || echo 'offline')"
        echo "Frontend (3006): $(lsof -i :3006 2>/dev/null | grep -q LISTEN && echo 'online' || echo 'offline')"
        ;;
    
    restart|r)
        SERVICE=$2
        case "$SERVICE" in
            backend)
                echo "Restarting backend..."
                tmux send-keys -t combat-mirror:0 C-c Enter
                sleep 1
                tmux send-keys -t combat-mirror:0 'cd ~/Desktop/combat-mirror-system/server && node index.js' Enter
                ;;
            copyparty)
                echo "Restarting Copyparty..."
                tmux send-keys -t combat-mirror:1 C-c Enter
                sleep 1
                tmux send-keys -t combat-mirror:1 'cd ~/Desktop/combat-mirror-system && python -m http.server 5923' Enter
                ;;
            frontend)
                echo "Restarting frontend..."
                tmux send-keys -t combat-mirror:2 C-c Enter
                sleep 1
                tmux send-keys -t combat-mirror:2 'cd ~/Desktop/combat-mirror-system && npm run dev' Enter
                ;;
            all)
                $0 restart backend
                $0 restart copyparty
                $0 restart frontend
                ;;
            *)
                echo "Usage: $0 restart [backend|copyparty|frontend|all]"
                ;;
        esac
        ;;
    
    logs|l)
        SERVICE=$2
        case "$SERVICE" in
            backend)
                tmux send-keys -t combat-mirror:3.1 C-c Enter
                tmux send-keys -t combat-mirror:3.1 'tail -f ~/Desktop/combat-mirror-system/server/*.log' Enter
                echo "Showing backend logs in monitor window..."
                ;;
            frontend)
                tmux capture-pane -t combat-mirror:2 -p | tail -50
                ;;
            *)
                echo "Usage: $0 logs [backend|frontend]"
                ;;
        esac
        ;;
    
    stop)
        echo "Stopping all Combat Mirror services..."
        tmux kill-session -t combat-mirror
        pkill -f "node.*5557"
        pkill -f "python.*5923"
        echo "Services stopped."
        ;;
    
    *)
        echo "Combat Mirror Tmux Manager"
        echo "=========================="
        echo ""
        echo "Usage: $0 [command] [options]"
        echo ""
        echo "Commands:"
        echo "  attach, a         - Attach to tmux session"
        echo "  status, s         - Show service status"
        echo "  restart, r [svc]  - Restart a service (backend|copyparty|frontend|all)"
        echo "  logs, l [svc]     - View service logs (backend|frontend)"
        echo "  stop              - Stop all services"
        echo ""
        echo "Tmux Navigation:"
        echo "  Ctrl+B 0         - Backend window"
        echo "  Ctrl+B 1         - Copyparty window"
        echo "  Ctrl+B 2         - Frontend window"
        echo "  Ctrl+B 3         - Monitor window"
        echo "  Ctrl+B D         - Detach from session"
        ;;
esac
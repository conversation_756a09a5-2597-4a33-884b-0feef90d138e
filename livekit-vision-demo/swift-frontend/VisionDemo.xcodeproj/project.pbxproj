// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		B5835B5D2D67FA960066BD5C /* LiveKit in Frameworks */ = {isa = PBXBuildFile; productRef = B5835B5C2D67FA960066BD5C /* LiveKit */; };
		B5835B602D67FAA80066BD5C /* LiveKitComponents in Frameworks */ = {isa = PBXBuildFile; productRef = B5835B5F2D67FAA80066BD5C /* LiveKitComponents */; };
		B5835B652D67FABF0066BD5C /* LiveKit in Frameworks */ = {isa = PBXBuildFile; productRef = B5835B642D67FABF0066BD5C /* LiveKit */; };
		B5C2EF652D0977AB00FAC766 /* ReplayKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = B5C2EF642D0977AB00FAC766 /* ReplayKit.framework */; };
		B5C2EF712D09781800FAC766 /* BroadcastExtension.appex in CopyFiles */ = {isa = PBXBuildFile; fileRef = B5C2EF622D0977AB00FAC766 /* BroadcastExtension.appex */; settings = {ATTRIBUTES = (RemoveHeadersOnCopy, ); }; };
		B5C2EF9D2D0980E700FAC766 /* LiveKit in Frameworks */ = {isa = PBXBuildFile; productRef = B5C2EF9C2D0980E700FAC766 /* LiveKit */; };
		B5C2EFA02D0980F400FAC766 /* LiveKitComponents in Frameworks */ = {isa = PBXBuildFile; productRef = B5C2EF9F2D0980F400FAC766 /* LiveKitComponents */; };
/* End PBXBuildFile section */

/* Begin PBXContainerItemProxy section */
		B5C2EFC42D09881800FAC766 /* PBXContainerItemProxy */ = {
			isa = PBXContainerItemProxy;
			containerPortal = B5C2EF182D08AF6900FAC766 /* Project object */;
			proxyType = 1;
			remoteGlobalIDString = B5C2EF612D0977AB00FAC766;
			remoteInfo = BroadcastExtension;
		};
/* End PBXContainerItemProxy section */

/* Begin PBXCopyFilesBuildPhase section */
		B5C2EF702D09780900FAC766 /* CopyFiles */ = {
			isa = PBXCopyFilesBuildPhase;
			buildActionMask = 2147483647;
			dstPath = "";
			dstSubfolderSpec = 13;
			files = (
				B5C2EF712D09781800FAC766 /* BroadcastExtension.appex in CopyFiles */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXCopyFilesBuildPhase section */

/* Begin PBXFileReference section */
		B5C2EF202D08AF6900FAC766 /* VisionDemo.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = VisionDemo.app; sourceTree = BUILT_PRODUCTS_DIR; };
		B5C2EF622D0977AB00FAC766 /* BroadcastExtension.appex */ = {isa = PBXFileReference; explicitFileType = "wrapper.app-extension"; includeInIndex = 0; path = BroadcastExtension.appex; sourceTree = BUILT_PRODUCTS_DIR; };
		B5C2EF642D0977AB00FAC766 /* ReplayKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = ReplayKit.framework; path = System/Library/Frameworks/ReplayKit.framework; sourceTree = SDKROOT; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		B5AFF8D22D0C08F90008976F /* Exceptions for "VisionDemo" folder in "VisionDemo" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				.env.example.xcconfig,
				Info.plist,
				VisionDemo.xcconfig,
			);
			target = B5C2EF1F2D08AF6900FAC766 /* VisionDemo */;
		};
		B5C2EF6A2D0977AB00FAC766 /* Exceptions for "BroadcastExtension" folder in "BroadcastExtension" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = B5C2EF612D0977AB00FAC766 /* BroadcastExtension */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		B5AFF8C02D0BAC950008976F /* VisionDemo */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B5AFF8D22D0C08F90008976F /* Exceptions for "VisionDemo" folder in "VisionDemo" target */,
			);
			path = VisionDemo;
			sourceTree = "<group>";
		};
		B5C2EF662D0977AB00FAC766 /* BroadcastExtension */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				B5C2EF6A2D0977AB00FAC766 /* Exceptions for "BroadcastExtension" folder in "BroadcastExtension" target */,
			);
			path = BroadcastExtension;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		B5C2EF1D2D08AF6900FAC766 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5835B5D2D67FA960066BD5C /* LiveKit in Frameworks */,
				B5C2EF9D2D0980E700FAC766 /* LiveKit in Frameworks */,
				B5C2EFA02D0980F400FAC766 /* LiveKitComponents in Frameworks */,
				B5835B602D67FAA80066BD5C /* LiveKitComponents in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5C2EF5F2D0977AB00FAC766 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				B5C2EF652D0977AB00FAC766 /* ReplayKit.framework in Frameworks */,
				B5835B652D67FABF0066BD5C /* LiveKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		B5C2EF172D08AF6900FAC766 = {
			isa = PBXGroup;
			children = (
				B5AFF8C02D0BAC950008976F /* VisionDemo */,
				B5C2EF662D0977AB00FAC766 /* BroadcastExtension */,
				B5C2EF632D0977AB00FAC766 /* Frameworks */,
				B5C2EF212D08AF6900FAC766 /* Products */,
			);
			sourceTree = "<group>";
		};
		B5C2EF212D08AF6900FAC766 /* Products */ = {
			isa = PBXGroup;
			children = (
				B5C2EF202D08AF6900FAC766 /* VisionDemo.app */,
				B5C2EF622D0977AB00FAC766 /* BroadcastExtension.appex */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		B5C2EF632D0977AB00FAC766 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				B5C2EF642D0977AB00FAC766 /* ReplayKit.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		B5C2EF1F2D08AF6900FAC766 /* VisionDemo */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5C2EF2F2D08AF6A00FAC766 /* Build configuration list for PBXNativeTarget "VisionDemo" */;
			buildPhases = (
				B5C2EF1C2D08AF6900FAC766 /* Sources */,
				B5C2EF1D2D08AF6900FAC766 /* Frameworks */,
				B5C2EF1E2D08AF6900FAC766 /* Resources */,
				B5C2EF702D09780900FAC766 /* CopyFiles */,
			);
			buildRules = (
			);
			dependencies = (
				B5C2EFC52D09881800FAC766 /* PBXTargetDependency */,
			);
			fileSystemSynchronizedGroups = (
				B5AFF8C02D0BAC950008976F /* VisionDemo */,
			);
			name = VisionDemo;
			packageProductDependencies = (
				B5C2EF9C2D0980E700FAC766 /* LiveKit */,
				B5C2EF9F2D0980F400FAC766 /* LiveKitComponents */,
				B5835B5C2D67FA960066BD5C /* LiveKit */,
				B5835B5F2D67FAA80066BD5C /* LiveKitComponents */,
			);
			productName = Vision;
			productReference = B5C2EF202D08AF6900FAC766 /* VisionDemo.app */;
			productType = "com.apple.product-type.application";
		};
		B5C2EF612D0977AB00FAC766 /* BroadcastExtension */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = B5C2EF6B2D0977AB00FAC766 /* Build configuration list for PBXNativeTarget "BroadcastExtension" */;
			buildPhases = (
				B5C2EF5E2D0977AB00FAC766 /* Sources */,
				B5C2EF5F2D0977AB00FAC766 /* Frameworks */,
				B5C2EF602D0977AB00FAC766 /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				B5C2EF662D0977AB00FAC766 /* BroadcastExtension */,
			);
			name = BroadcastExtension;
			packageProductDependencies = (
				B5835B642D67FABF0066BD5C /* LiveKit */,
			);
			productName = BroadcastExtension;
			productReference = B5C2EF622D0977AB00FAC766 /* BroadcastExtension.appex */;
			productType = "com.apple.product-type.app-extension";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		B5C2EF182D08AF6900FAC766 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1610;
				LastUpgradeCheck = 1620;
				TargetAttributes = {
					B5C2EF1F2D08AF6900FAC766 = {
						CreatedOnToolsVersion = 16.1;
					};
					B5C2EF612D0977AB00FAC766 = {
						CreatedOnToolsVersion = 16.1;
					};
				};
			};
			buildConfigurationList = B5C2EF1B2D08AF6900FAC766 /* Build configuration list for PBXProject "VisionDemo" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = B5C2EF172D08AF6900FAC766;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				B5835B5B2D67FA960066BD5C /* XCRemoteSwiftPackageReference "client-sdk-swift" */,
				B5835B5E2D67FAA80066BD5C /* XCRemoteSwiftPackageReference "components-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = B5C2EF212D08AF6900FAC766 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				B5C2EF612D0977AB00FAC766 /* BroadcastExtension */,
				B5C2EF1F2D08AF6900FAC766 /* VisionDemo */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		B5C2EF1E2D08AF6900FAC766 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5C2EF602D0977AB00FAC766 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		B5C2EF1C2D08AF6900FAC766 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
		B5C2EF5E2D0977AB00FAC766 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin PBXTargetDependency section */
		B5C2EFC52D09881800FAC766 /* PBXTargetDependency */ = {
			isa = PBXTargetDependency;
			target = B5C2EF612D0977AB00FAC766 /* BroadcastExtension */;
			targetProxy = B5C2EFC42D09881800FAC766 /* PBXContainerItemProxy */;
		};
/* End PBXTargetDependency section */

/* Begin XCBuildConfiguration section */
		B5C2EF2D2D08AF6A00FAC766 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
				SWIFT_VERSION = 5.0;
			};
			name = Debug;
		};
		B5C2EF2E2D08AF6A00FAC766 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEAD_CODE_STRIPPING = YES;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SWIFT_COMPILATION_MODE = wholemodule;
				SWIFT_VERSION = 5.0;
			};
			name = Release;
		};
		B5C2EF302D08AF6A00FAC766 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = B5AFF8C02D0BAC950008976F /* VisionDemo */;
			baseConfigurationReferenceRelativePath = VisionDemo.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = VisionDemo/VisionDemo.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 76TVFCUKK7;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VisionDemo/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "LiveKit Vision";
				INFOPLIST_KEY_NSCameraUsageDescription = "To see your beautiful face.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "To hear your beautiful voice.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.livekit.vision;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Debug;
		};
		B5C2EF312D08AF6A00FAC766 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReferenceAnchor = B5AFF8C02D0BAC950008976F /* VisionDemo */;
			baseConfigurationReferenceRelativePath = .env.xcconfig;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = VisionDemo/VisionDemo.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 76TVFCUKK7;
				ENABLE_HARDENED_RUNTIME = YES;
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = VisionDemo/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "LiveKit Vision";
				INFOPLIST_KEY_NSCameraUsageDescription = "To see your beautiful face.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "To hear your beautiful voice.";
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSceneManifest_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphoneos*]" = YES;
				"INFOPLIST_KEY_UILaunchScreen_Generation[sdk=iphonesimulator*]" = YES;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphoneos*]" = UIStatusBarStyleDefault;
				"INFOPLIST_KEY_UIStatusBarStyle[sdk=iphonesimulator*]" = UIStatusBarStyleDefault;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = "@executable_path/Frameworks";
				"LD_RUNPATH_SEARCH_PATHS[sdk=macosx*]" = "@executable_path/../Frameworks";
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.livekit.vision;
				PRODUCT_NAME = "$(TARGET_NAME)";
				SDKROOT = auto;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Release;
		};
		B5C2EF6C2D0977AB00FAC766 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = BroadcastExtension/BroadcastExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 76TVFCUKK7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = BroadcastExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "LiveKit Vision";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.livekit.vision.broadcast;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				XROS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Debug;
		};
		B5C2EF6D2D0977AB00FAC766 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				CODE_SIGN_ENTITLEMENTS = BroadcastExtension/BroadcastExtension.entitlements;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = 76TVFCUKK7;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = BroadcastExtension/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = "LiveKit Vision";
				INFOPLIST_KEY_LSApplicationCategoryType = "";
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
					"@executable_path/../../Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = io.livekit.vision.broadcast;
				PRODUCT_NAME = "$(TARGET_NAME)";
				REGISTER_APP_GROUPS = NO;
				SDKROOT = iphoneos;
				SKIP_INSTALL = YES;
				SUPPORTED_PLATFORMS = "iphoneos iphonesimulator xros xrsimulator";
				SUPPORTS_MACCATALYST = NO;
				SUPPORTS_MAC_DESIGNED_FOR_IPHONE_IPAD = NO;
				SUPPORTS_XR_DESIGNED_FOR_IPHONE_IPAD = NO;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2,7";
				VALIDATE_PRODUCT = YES;
				XROS_DEPLOYMENT_TARGET = 2.0;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		B5C2EF1B2D08AF6900FAC766 /* Build configuration list for PBXProject "VisionDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5C2EF2D2D08AF6A00FAC766 /* Debug */,
				B5C2EF2E2D08AF6A00FAC766 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5C2EF2F2D08AF6A00FAC766 /* Build configuration list for PBXNativeTarget "VisionDemo" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5C2EF302D08AF6A00FAC766 /* Debug */,
				B5C2EF312D08AF6A00FAC766 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		B5C2EF6B2D0977AB00FAC766 /* Build configuration list for PBXNativeTarget "BroadcastExtension" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				B5C2EF6C2D0977AB00FAC766 /* Debug */,
				B5C2EF6D2D0977AB00FAC766 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		B5835B5B2D67FA960066BD5C /* XCRemoteSwiftPackageReference "client-sdk-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/livekit/client-sdk-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.5.0;
			};
		};
		B5835B5E2D67FAA80066BD5C /* XCRemoteSwiftPackageReference "components-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/livekit/components-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.1.1;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		B5835B5C2D67FA960066BD5C /* LiveKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = B5835B5B2D67FA960066BD5C /* XCRemoteSwiftPackageReference "client-sdk-swift" */;
			productName = LiveKit;
		};
		B5835B5F2D67FAA80066BD5C /* LiveKitComponents */ = {
			isa = XCSwiftPackageProductDependency;
			package = B5835B5E2D67FAA80066BD5C /* XCRemoteSwiftPackageReference "components-swift" */;
			productName = LiveKitComponents;
		};
		B5835B642D67FABF0066BD5C /* LiveKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = B5835B5B2D67FA960066BD5C /* XCRemoteSwiftPackageReference "client-sdk-swift" */;
			productName = LiveKit;
		};
		B5C2EF9C2D0980E700FAC766 /* LiveKit */ = {
			isa = XCSwiftPackageProductDependency;
			productName = LiveKit;
		};
		B5C2EF9F2D0980F400FAC766 /* LiveKitComponents */ = {
			isa = XCSwiftPackageProductDependency;
			productName = LiveKitComponents;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = B5C2EF182D08AF6900FAC766 /* Project object */;
}

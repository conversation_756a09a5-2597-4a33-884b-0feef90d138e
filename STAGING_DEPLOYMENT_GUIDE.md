# Combat Mirror System - Staging Environment Deployment Guide

## Overview

This guide covers deploying and managing the Combat Mirror System staging environment on Railway. The staging environment is designed for testing new features, validating deployments, and quality assurance before production releases.

## Staging Environment Architecture

```
┌─────────────────────────────────────────────────────────┐
│                   Staging Environment                    │
├─────────────────────────────────────────────────────────┤
│                                                         │
│  ┌─────────────┐    ┌─────────────┐    ┌────────────┐ │
│  │   Frontend   │────│   Backend   │────│  LiveKit   │ │
│  │  (Next.js)   │    │   (API)     │    │  Server    │ │
│  └──────┬──────┘    └──────┬──────┘    └─────┬──────┘ │
│         │                   │                  │        │
│         └───────────────────┴──────────────────┘        │
│                            │                            │
│                     ┌──────┴──────┐                    │
│                     │    Redis    │                    │
│                     │   (Cache)   │                    │
│                     └─────────────┘                    │
└─────────────────────────────────────────────────────────┘
```

## Quick Start

### 1. Initial Staging Deployment

```bash
# Set up environment variables
export GEMINI_API_KEY="your_gemini_api_key"
export LIVEKIT_SECRET="your_livekit_secret"
export CREATE_STAGING=true
export RAILWAY_ENVIRONMENT=staging

# Run the deployment script
./deploy-railway.sh
```

### 2. Manual Staging Setup

If you prefer manual setup:

```bash
# Frontend staging
railway environment new staging --project combat-mirror-frontend
railway environment staging
railway variables set -e staging < .env.staging
railway up

# Backend staging
railway environment new staging --project combat-mirror-api
railway environment staging
railway variables set -e staging < .env.staging
railway up

# LiveKit staging
railway environment new staging --project combat-mirror-livekit
railway environment staging
railway variables set -e staging < .env.staging
railway up
```

## Staging-Specific Features

### 1. Debug Mode
- Enhanced logging with stack traces
- Performance profiler in UI
- Request/response debugging
- WebSocket connection monitoring

### 2. Test Endpoints
```bash
# Health check with debug info
curl https://combat-mirror-api-staging.railway.app/api/health?debug=true

# Test analysis endpoint
curl -X POST https://combat-mirror-api-staging.railway.app/api/test/analyze \
  -H "Content-Type: application/json" \
  -d '{"test": true}'

# Performance metrics
curl https://combat-mirror-api-staging.railway.app/api/metrics
```

### 3. Feature Flags
Access experimental features via query parameters:
- `?experimental=true` - Enable experimental UI
- `?profile=true` - Show performance profiler
- `?debug=true` - Enable debug panel

## Deployment Workflow

### 1. Pre-deployment Checklist
- [ ] All tests passing locally
- [ ] Environment variables updated in `.env.staging`
- [ ] Database migrations ready (if applicable)
- [ ] Feature flags configured

### 2. Deploy to Staging
```bash
# Switch to staging environment
railway environment staging

# Deploy specific service
railway up --service frontend
railway up --service backend
railway up --service livekit

# Or deploy all services
railway up
```

### 3. Post-deployment Validation
```bash
# Run automated checks
npm run test:staging

# Manual validation
- Visit https://combat-mirror-frontend-staging.railway.app
- Test camera functionality
- Verify AI analysis works
- Check WebSocket connections
- Monitor logs: railway logs --service backend
```

### 4. Promote to Production
Once staging is validated:
```bash
# Switch to production
railway environment production

# Deploy same commit
railway up --commit <staging-commit-sha>
```

## Monitoring Staging

### 1. Real-time Logs
```bash
# All services
railway logs -f

# Specific service
railway logs --service backend -f

# With filters
railway logs --service backend | grep ERROR
```

### 2. Performance Monitoring
- Railway Dashboard: https://railway.app/dashboard
- OpenTelemetry traces (if configured)
- Custom metrics endpoint: `/api/metrics`

### 3. Health Checks
Automated health checks run every 30 seconds:
- Frontend: `/` (200 OK)
- Backend: `/api/health` (JSON response)
- LiveKit: `/` (200 OK)

## Troubleshooting Staging

### Common Issues

1. **WebSocket Connection Failed**
   ```bash
   # Check LiveKit logs
   railway logs --service livekit | grep ERROR
   
   # Verify TCP proxy configuration
   railway status --service livekit
   ```

2. **CORS Errors**
   ```bash
   # Update CORS settings
   railway variables set FRONTEND_URL=https://your-frontend-url.railway.app
   railway restart --service backend
   ```

3. **Memory Issues**
   ```bash
   # Check resource usage
   railway status
   
   # Scale up if needed
   railway scale --service backend --memory 1024
   ```

### Debug Commands

```bash
# SSH into container (if enabled)
railway run --service backend bash

# Execute commands in context
railway run --service backend npm run debug

# Check environment variables
railway variables --service backend
```

## Staging Best Practices

### 1. Data Management
- Use separate test data in staging
- Implement data seeding scripts
- Regular cleanup of test data
- Never use production data

### 2. Testing Strategy
- Automated E2E tests against staging
- Manual QA checklist
- Performance benchmarking
- Security scanning

### 3. Version Control
- Tag staging deployments: `staging-v1.2.3`
- Document changes in CHANGELOG
- Keep staging branch separate
- Regular sync with main branch

### 4. Cost Optimization
- Auto-sleep after 1 hour inactivity
- Use minimal resources (1 replica each)
- Regular cleanup of unused volumes
- Monitor usage in Railway dashboard

## Security Considerations

### Staging-Specific Security
1. **Relaxed CORS** for testing tools
2. **Debug endpoints** protected by API keys
3. **Test credentials** rotated regularly
4. **Separate secrets** from production

### Access Control
```yaml
# .railway/staging.yml
security:
  ipAllowlist:
    - "0.0.0.0/0"  # Open in staging
  authentication:
    required: true
    type: "basic"
    credentials:
      username: "staging"
      password: ${STAGING_PASSWORD}
```

## Rollback Procedures

If issues arise in staging:

```bash
# Quick rollback
railway rollback --service backend

# Rollback to specific deployment
railway deployments --service backend
railway rollback --service backend --deployment <deployment-id>

# Emergency shutdown
railway down --service backend
```

## Staging Maintenance

### Weekly Tasks
- [ ] Review and clean up test data
- [ ] Check disk usage on volumes
- [ ] Update dependencies
- [ ] Review error logs

### Monthly Tasks
- [ ] Rotate test credentials
- [ ] Performance audit
- [ ] Security scan
- [ ] Cost review

## Integration with CI/CD

### GitHub Actions Example
```yaml
name: Deploy to Staging
on:
  push:
    branches: [staging]

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - name: Install Railway
        run: npm i -g @railway/cli
      - name: Deploy to Staging
        run: |
          railway login --token ${{ secrets.RAILWAY_TOKEN }}
          railway environment staging
          railway up
        env:
          RAILWAY_TOKEN: ${{ secrets.RAILWAY_TOKEN }}
```

## Contact and Support

- **Railway Status**: https://status.railway.app
- **Railway Discord**: https://discord.gg/railway
- **Internal Team**: #combat-mirror-staging on Slack

Remember: Staging is for testing - break things here, not in production! 🧪
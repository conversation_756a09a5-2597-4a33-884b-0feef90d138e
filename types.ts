
export enum SessionState {
  Idle = 'Idle',
  Active = 'Active',
  Analyzing = 'Analyzing',
  Finished = 'Finished',
}

export enum SessionType {
  Baseline = 'Baseline Session (5 mins)',
  Training = 'Training Session (15 mins)',
  Recovery = 'Recovery Analysis (5 mins)',
}

export interface Metrics {
  punchRate: number;
  punchCount: number;
  punchVelocity: number;
  headMovement: number;
  postureScore: number;
  gaitBalance: number;
  fatigue: number;
}

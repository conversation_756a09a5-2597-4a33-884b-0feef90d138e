# Combat Mirror iOS App

A modern iOS app for real-time combat sports training with AI coaching, built using the latest LiveKit patterns and features.

## Features

- **Real-time Video Analysis**: Uses device camera for live combat sports motion tracking
- **AI Combat Coach**: Intelligent coaching powered by Gemini Live API via LiveKit Agents
- **Multi-Camera Support**: Connect multiple devices for different angles
- **Live Metrics**: Real-time display of stance quality, balance, defense rating, and more
- **Voice & Video Modes**: Switch between voice-only and full video analysis
- **Transcription Support**: Real-time transcription of coaching feedback

## Architecture

Based on the LiveKit vision demo and starter app patterns:

- **SwiftUI + Observation**: Modern SwiftUI app with `@Observable` view models
- **LiveKit Swift SDK**: Latest v2.x SDK with multimodal support
- **Dependency Injection**: Clean architecture with dependency injection
- **Pre-connect Audio**: Smooth connection experience with audio capture before room join
- **Data Channels**: Real-time metrics transmission via LiveKit data channels

## Setup

1. **Install Dependencies**
   ```bash
   open CombatMirror.xcodeproj
   # Xcode will automatically resolve Swift Package Manager dependencies
   ```

2. **Configure Environment**
   Set these environment variables in your Xcode scheme:
   ```
   LIVEKIT_URL=wss://your-livekit-server.com
   LIVEKIT_API_KEY=your-api-key
   LIVEKIT_API_SECRET=your-api-secret
   ```

3. **Run the Combat Vision Agent**
   ```bash
   cd ../agent
   python combat_vision_agent.py
   ```

4. **Build and Run**
   - Select your target device/simulator
   - Press Cmd+R to build and run

## Usage

1. **Start Training Session**
   - Tap "Start Training Session" to connect
   - Grant camera and microphone permissions when prompted

2. **Training Modes**
   - **Voice Mode**: Voice coaching without video
   - **Video Mode**: Full video analysis with single camera
   - **Multi-Camera**: Connect multiple devices for different angles

3. **Live Metrics**
   - View real-time metrics including stance quality, balance score, defense rating
   - See coaching messages overlaid on the video feed
   - Track performance trends over time

4. **Settings**
   - Configure audio/video devices
   - Adjust training parameters
   - View connection status

## Code Structure

```
CombatMirror/
├── CombatMirrorApp.swift      # App entry point
├── App/
│   └── CombatMirrorViewModel.swift  # Main view model
├── Views/
│   ├── AppView.swift          # Root navigation
│   ├── CombatView.swift       # Main training interface
│   ├── MetricsSection.swift   # Metrics display
│   ├── ControlBar.swift       # Media controls
│   ├── CoachingOverlay.swift  # Coaching messages
│   ├── SettingsView.swift     # Settings screen
│   └── TrackView.swift        # Video track renderer
├── Services/
│   ├── Dependencies.swift     # DI container
│   └── TokenService.swift     # LiveKit token management
└── Extensions/
    ├── Participant+Extensions.swift
    └── Room+PreConnectAudio.swift
```

## Key Components

### CombatMirrorViewModel
The main view model that manages:
- LiveKit room connection
- Track management (audio/video)
- Combat metrics collection via data channels
- Multi-camera coordination
- Coaching message display

### Combat Metrics
Real-time metrics transmitted via data channels:
- Stance Quality (0-100%)
- Guard Position (High/Low/Open)
- Balance Score (0-100)
- Defense Rating (0-100%)
- Head Movement (degrees)
- Fatigue Level (0-100%)
- Punch Count
- Technique Focus
- Improvement Areas

### Multi-Camera Support
- Main device camera
- Additional camera positions: Front, Side, Overhead
- QR code scanning for easy device connection
- Synchronized analysis across all camera feeds

## Development

### Adding New Features
1. Update `CombatFeatures` option set for feature flags
2. Extend view model with new functionality
3. Add UI components in Views/
4. Update agent to handle new data types

### Testing
```bash
# Unit tests
swift test

# UI tests
# Run from Xcode: Product > Test
```

## Deployment

### TestFlight
1. Archive the app in Xcode
2. Upload to App Store Connect
3. Distribute via TestFlight

### App Store
1. Ensure all permissions are properly described
2. Test on multiple device types
3. Submit for App Store review

## Requirements

- iOS 17.0+
- Xcode 15.0+
- Swift 5.9+
- LiveKit Swift SDK 2.0+

## License

Copyright © 2025 Combat Mirror. All rights reserved.
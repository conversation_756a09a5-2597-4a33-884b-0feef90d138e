// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 56;
	objects = {

/* Begin PBXBuildFile section */
		1A2B3C4D2B123456789ABCDE /* CombatMirrorApp.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4C2B123456789ABCDE /* CombatMirrorApp.swift */; };
		1A2B3C4F2B123456789ABCDE /* CombatMirrorViewModel.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C4E2B123456789ABCDE /* CombatMirrorViewModel.swift */; };
		1A2B3C512B123456789ABCDE /* AppView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C502B123456789ABCDE /* AppView.swift */; };
		1A2B3C532B123456789ABCDE /* CombatView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C522B123456789ABCDE /* CombatView.swift */; };
		1A2B3C552B123456789ABCDE /* MetricsSection.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C542B123456789ABCDE /* MetricsSection.swift */; };
		1A2B3C572B123456789ABCDE /* ControlBar.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C562B123456789ABCDE /* ControlBar.swift */; };
		1A2B3C592B123456789ABCDE /* CoachingOverlay.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C582B123456789ABCDE /* CoachingOverlay.swift */; };
		1A2B3C5B2B123456789ABCDE /* SettingsView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C5A2B123456789ABCDE /* SettingsView.swift */; };
		1A2B3C5D2B123456789ABCDE /* TrackView.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C5C2B123456789ABCDE /* TrackView.swift */; };
		1A2B3C5F2B123456789ABCDE /* Dependencies.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C5E2B123456789ABCDE /* Dependencies.swift */; };
		1A2B3C612B123456789ABCDE /* TokenService.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C602B123456789ABCDE /* TokenService.swift */; };
		1A2B3C632B123456789ABCDE /* Participant+Extensions.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C622B123456789ABCDE /* Participant+Extensions.swift */; };
		1A2B3C652B123456789ABCDE /* Room+PreConnectAudio.swift in Sources */ = {isa = PBXBuildFile; fileRef = 1A2B3C642B123456789ABCDE /* Room+PreConnectAudio.swift */; };
		1A2B3C672B123456789ABCDE /* Assets.xcassets in Resources */ = {isa = PBXBuildFile; fileRef = 1A2B3C662B123456789ABCDE /* Assets.xcassets */; };
		1A2B3C6A2B123456789ABCDE /* LiveKit in Frameworks */ = {isa = PBXBuildFile; productRef = 1A2B3C692B123456789ABCDE /* LiveKit */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1A2B3C492B123456789ABCDE /* CombatMirror.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = CombatMirror.app; sourceTree = BUILT_PRODUCTS_DIR; };
		1A2B3C4C2B123456789ABCDE /* CombatMirrorApp.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CombatMirrorApp.swift; sourceTree = "<group>"; };
		1A2B3C4E2B123456789ABCDE /* CombatMirrorViewModel.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CombatMirrorViewModel.swift; sourceTree = "<group>"; };
		1A2B3C502B123456789ABCDE /* AppView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = AppView.swift; sourceTree = "<group>"; };
		1A2B3C522B123456789ABCDE /* CombatView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CombatView.swift; sourceTree = "<group>"; };
		1A2B3C542B123456789ABCDE /* MetricsSection.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = MetricsSection.swift; sourceTree = "<group>"; };
		1A2B3C562B123456789ABCDE /* ControlBar.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = ControlBar.swift; sourceTree = "<group>"; };
		1A2B3C582B123456789ABCDE /* CoachingOverlay.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = CoachingOverlay.swift; sourceTree = "<group>"; };
		1A2B3C5A2B123456789ABCDE /* SettingsView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = SettingsView.swift; sourceTree = "<group>"; };
		1A2B3C5C2B123456789ABCDE /* TrackView.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TrackView.swift; sourceTree = "<group>"; };
		1A2B3C5E2B123456789ABCDE /* Dependencies.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = Dependencies.swift; sourceTree = "<group>"; };
		1A2B3C602B123456789ABCDE /* TokenService.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = TokenService.swift; sourceTree = "<group>"; };
		1A2B3C622B123456789ABCDE /* Participant+Extensions.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Participant+Extensions.swift"; sourceTree = "<group>"; };
		1A2B3C642B123456789ABCDE /* Room+PreConnectAudio.swift */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.swift; path = "Room+PreConnectAudio.swift"; sourceTree = "<group>"; };
		1A2B3C662B123456789ABCDE /* Assets.xcassets */ = {isa = PBXFileReference; lastKnownFileType = folder.assetcatalog; path = Assets.xcassets; sourceTree = "<group>"; };
		1A2B3C6B2B123456789ABCDE /* Info.plist */ = {isa = PBXFileReference; lastKnownFileType = text.plist.xml; path = Info.plist; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		1A2B3C462B123456789ABCDE /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C6A2B123456789ABCDE /* LiveKit in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		1A2B3C402B123456789ABCDE = {
			isa = PBXGroup;
			children = (
				1A2B3C4B2B123456789ABCDE /* CombatMirror */,
				1A2B3C4A2B123456789ABCDE /* Products */,
			);
			sourceTree = "<group>";
		};
		1A2B3C4A2B123456789ABCDE /* Products */ = {
			isa = PBXGroup;
			children = (
				1A2B3C492B123456789ABCDE /* CombatMirror.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		1A2B3C4B2B123456789ABCDE /* CombatMirror */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4C2B123456789ABCDE /* CombatMirrorApp.swift */,
				1A2B3C6C2B123456789ABCDE /* App */,
				1A2B3C6D2B123456789ABCDE /* Views */,
				1A2B3C6E2B123456789ABCDE /* Services */,
				1A2B3C6F2B123456789ABCDE /* Extensions */,
				1A2B3C662B123456789ABCDE /* Assets.xcassets */,
				1A2B3C6B2B123456789ABCDE /* Info.plist */,
			);
			path = CombatMirror;
			sourceTree = "<group>";
		};
		1A2B3C6C2B123456789ABCDE /* App */ = {
			isa = PBXGroup;
			children = (
				1A2B3C4E2B123456789ABCDE /* CombatMirrorViewModel.swift */,
			);
			path = App;
			sourceTree = "<group>";
		};
		1A2B3C6D2B123456789ABCDE /* Views */ = {
			isa = PBXGroup;
			children = (
				1A2B3C502B123456789ABCDE /* AppView.swift */,
				1A2B3C522B123456789ABCDE /* CombatView.swift */,
				1A2B3C542B123456789ABCDE /* MetricsSection.swift */,
				1A2B3C562B123456789ABCDE /* ControlBar.swift */,
				1A2B3C582B123456789ABCDE /* CoachingOverlay.swift */,
				1A2B3C5A2B123456789ABCDE /* SettingsView.swift */,
				1A2B3C5C2B123456789ABCDE /* TrackView.swift */,
			);
			path = Views;
			sourceTree = "<group>";
		};
		1A2B3C6E2B123456789ABCDE /* Services */ = {
			isa = PBXGroup;
			children = (
				1A2B3C5E2B123456789ABCDE /* Dependencies.swift */,
				1A2B3C602B123456789ABCDE /* TokenService.swift */,
			);
			path = Services;
			sourceTree = "<group>";
		};
		1A2B3C6F2B123456789ABCDE /* Extensions */ = {
			isa = PBXGroup;
			children = (
				1A2B3C622B123456789ABCDE /* Participant+Extensions.swift */,
				1A2B3C642B123456789ABCDE /* Room+PreConnectAudio.swift */,
			);
			path = Extensions;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		1A2B3C482B123456789ABCDE /* CombatMirror */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 1A2B3C6E2B123456789ABCDE /* Build configuration list for PBXNativeTarget "CombatMirror" */;
			buildPhases = (
				1A2B3C452B123456789ABCDE /* Sources */,
				1A2B3C462B123456789ABCDE /* Frameworks */,
				1A2B3C472B123456789ABCDE /* Resources */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = CombatMirror;
			packageProductDependencies = (
				1A2B3C692B123456789ABCDE /* LiveKit */,
			);
			productName = CombatMirror;
			productReference = 1A2B3C492B123456789ABCDE /* CombatMirror.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		1A2B3C412B123456789ABCDE /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				LastSwiftUpdateCheck = 1540;
				LastUpgradeCheck = 1540;
				TargetAttributes = {
					1A2B3C482B123456789ABCDE = {
						CreatedOnToolsVersion = 15.4;
					};
				};
			};
			buildConfigurationList = 1A2B3C442B123456789ABCDE /* Build configuration list for PBXProject "CombatMirror" */;
			compatibilityVersion = "Xcode 14.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 1A2B3C402B123456789ABCDE;
			packageReferences = (
				1A2B3C682B123456789ABCDE /* XCRemoteSwiftPackageReference "swift-client-sdk" */,
			);
			productRefGroup = 1A2B3C4A2B123456789ABCDE /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				1A2B3C482B123456789ABCDE /* CombatMirror */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		1A2B3C472B123456789ABCDE /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C672B123456789ABCDE /* Assets.xcassets in Resources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		1A2B3C452B123456789ABCDE /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				1A2B3C4D2B123456789ABCDE /* CombatMirrorApp.swift in Sources */,
				1A2B3C4F2B123456789ABCDE /* CombatMirrorViewModel.swift in Sources */,
				1A2B3C512B123456789ABCDE /* AppView.swift in Sources */,
				1A2B3C532B123456789ABCDE /* CombatView.swift in Sources */,
				1A2B3C552B123456789ABCDE /* MetricsSection.swift in Sources */,
				1A2B3C572B123456789ABCDE /* ControlBar.swift in Sources */,
				1A2B3C592B123456789ABCDE /* CoachingOverlay.swift in Sources */,
				1A2B3C5B2B123456789ABCDE /* SettingsView.swift in Sources */,
				1A2B3C5D2B123456789ABCDE /* TrackView.swift in Sources */,
				1A2B3C5F2B123456789ABCDE /* Dependencies.swift in Sources */,
				1A2B3C612B123456789ABCDE /* TokenService.swift in Sources */,
				1A2B3C632B123456789ABCDE /* Participant+Extensions.swift in Sources */,
				1A2B3C652B123456789ABCDE /* Room+PreConnectAudio.swift in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		1A2B3C6C2B123456789ABCDE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		1A2B3C6D2B123456789ABCDE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 17.0;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
		1A2B3C6F2B123456789ABCDE /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CombatMirror/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "Combat Mirror needs camera access to analyze your boxing technique and provide real-time coaching.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Combat Mirror needs microphone access for voice commands and coaching feedback.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.combatmirror.ios-app";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		1A2B3C702B123456789ABCDE /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_STYLE = Automatic;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_ASSET_PATHS = "";
				ENABLE_PREVIEWS = YES;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = CombatMirror/Info.plist;
				INFOPLIST_KEY_NSCameraUsageDescription = "Combat Mirror needs camera access to analyze your boxing technique and provide real-time coaching.";
				INFOPLIST_KEY_NSMicrophoneUsageDescription = "Combat Mirror needs microphone access for voice commands and coaching feedback.";
				INFOPLIST_KEY_UIApplicationSceneManifest_Generation = YES;
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchScreen_Generation = YES;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPhone = "UIInterfaceOrientationPortrait UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight";
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = "com.combatmirror.ios-app";
				PRODUCT_NAME = "$(TARGET_NAME)";
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		1A2B3C442B123456789ABCDE /* Build configuration list for PBXProject "CombatMirror" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C6C2B123456789ABCDE /* Debug */,
				1A2B3C6D2B123456789ABCDE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		1A2B3C6E2B123456789ABCDE /* Build configuration list for PBXNativeTarget "CombatMirror" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				1A2B3C6F2B123456789ABCDE /* Debug */,
				1A2B3C702B123456789ABCDE /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		1A2B3C682B123456789ABCDE /* XCRemoteSwiftPackageReference "swift-client-sdk" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/livekit/swift-client-sdk.git";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 2.0.0;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		1A2B3C692B123456789ABCDE /* LiveKit */ = {
			isa = XCSwiftPackageProductDependency;
			package = 1A2B3C682B123456789ABCDE /* XCRemoteSwiftPackageReference "swift-client-sdk" */;
			productName = LiveKit;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 1A2B3C412B123456789ABCDE /* Project object */;
}
import SwiftUI

struct CoachingOverlay: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        VStack(spacing: 8) {
            ForEach(viewModel.coachingMessages.suffix(3)) { message in
                CoachingBubble(message: message)
                    .transition(.asymmetric(
                        insertion: .move(edge: .bottom).combined(with: .opacity),
                        removal: .opacity
                    ))
            }
        }
        .animation(.spring(), value: viewModel.coachingMessages.count)
    }
}

struct CoachingBubble: View {
    let message: CoachingMessage
    @State private var isVisible = false
    
    var body: some View {
        HStack(spacing: 12) {
            Image(systemName: message.type.icon)
                .font(.title3)
                .foregroundStyle(.white)
                .padding(8)
                .background(Circle().fill(message.type.color))
            
            Text(message.message)
                .font(.callout)
                .foregroundStyle(.white)
                .multilineTextAlignment(.leading)
            
            Spacer()
        }
        .padding()
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.black.opacity(0.8))
        )
        .scaleEffect(isVisible ? 1 : 0.8)
        .opacity(isVisible ? 1 : 0)
        .onAppear {
            withAnimation(.spring()) {
                isVisible = true
            }
            
            // Auto-hide after 5 seconds
            DispatchQueue.main.asyncAfter(deadline: .now() + 5) {
                withAnimation {
                    isVisible = false
                }
            }
        }
    }
}

#Preview {
    ZStack {
        Color.black
        VStack {
            Spacer()
            CoachingOverlay()
                .padding()
        }
    }
    .environment(CombatMirrorViewModel())
}
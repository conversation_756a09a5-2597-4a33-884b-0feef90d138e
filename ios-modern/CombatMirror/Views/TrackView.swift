import SwiftUI
import LiveKit

/// A SwiftUI wrapper for displaying LiveKit video/audio tracks
struct TrackView: UIViewRepresentable {
    let track: any Track
    
    func makeUIView(context: Context) -> VideoView {
        let view = VideoView()
        view.layoutMode = .fill
        view.debugMode = false
        return view
    }
    
    func updateUIView(_ uiView: VideoView, context: Context) {
        if let videoTrack = track as? VideoTrack {
            videoTrack.add(videoRenderer: uiView)
        }
    }
    
    static func dismantleUIView(_ uiView: VideoView, coordinator: ()) {
        // Clean up when view is removed
        if let videoTrack = uiView.track as? VideoTrack {
            videoTrack.remove(videoRenderer: uiView)
        }
    }
}

#if os(macOS)
struct TrackView: NSViewRepresentable {
    let track: any Track
    
    func makeNSView(context: Context) -> VideoView {
        let view = VideoView()
        view.layoutMode = .fill
        view.debugMode = false
        return view
    }
    
    func updateNSView(_ nsView: VideoView, context: Context) {
        if let videoTrack = track as? VideoTrack {
            videoTrack.add(videoRenderer: nsView)
        }
    }
    
    static func dismantleNSView(_ nsView: VideoView, coordinator: ()) {
        if let videoTrack = nsView.track as? VideoTrack {
            videoTrack.remove(videoRenderer: nsView)
        }
    }
}
#endif
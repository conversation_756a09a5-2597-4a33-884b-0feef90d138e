import SwiftUI
import LiveKit

struct CombatView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    @State private var showSettings = false
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                // Background
                Color.black.edgesIgnoringSafeArea(.all)
                
                // Main content
                if geometry.size.width > geometry.size.height {
                    // Landscape layout
                    HStack(spacing: 0) {
                        // Video section
                        VideoSection()
                            .frame(width: geometry.size.width * 0.7)
                        
                        // Metrics section
                        MetricsSection()
                            .frame(width: geometry.size.width * 0.3)
                            .background(Color(.systemGray6))
                    }
                } else {
                    // Portrait layout
                    VStack(spacing: 0) {
                        // Video section
                        VideoSection()
                            .frame(height: geometry.size.height * 0.6)
                        
                        // Metrics section
                        MetricsSection()
                            .frame(height: geometry.size.height * 0.4)
                            .background(Color(.systemGray6))
                    }
                }
                
                // Overlay controls
                VStack {
                    HStack {
                        // Connection status
                        ConnectionBadge()
                        
                        Spacer()
                        
                        // Settings button
                        Button(action: { showSettings.toggle() }) {
                            Image(systemName: "gear")
                                .font(.title2)
                                .foregroundStyle(.white)
                                .padding(12)
                                .background(Circle().fill(Color.black.opacity(0.5)))
                        }
                    }
                    .padding()
                    
                    Spacer()
                    
                    // Bottom controls
                    ControlBar()
                        .padding()
                }
            }
        }
        .sheet(isPresented: $showSettings) {
            SettingsView()
        }
    }
}

struct VideoSection: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        ZStack {
            if viewModel.combatFeatures.contains(.multiCamera) {
                MultiCameraView()
            } else {
                SingleCameraView()
            }
            
            // Coaching overlay
            if !viewModel.coachingMessages.isEmpty {
                VStack {
                    Spacer()
                    CoachingOverlay()
                        .padding()
                }
            }
        }
    }
}

struct SingleCameraView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        ZStack {
            if let videoTrack = viewModel.cameraTrack {
                TrackView(track: videoTrack)
                    .aspectRatio(contentMode: .fit)
            } else {
                VStack {
                    Image(systemName: "video.slash")
                        .font(.system(size: 60))
                        .foregroundStyle(.gray)
                    Text("Camera Off")
                        .font(.title3)
                        .foregroundStyle(.gray)
                }
            }
            
            // Agent video overlay (if available)
            if let agentVideoTrack = viewModel.agentVideoTrack {
                VStack {
                    HStack {
                        Spacer()
                        TrackView(track: agentVideoTrack)
                            .frame(width: 150, height: 150)
                            .clipShape(RoundedRectangle(cornerRadius: 12))
                            .padding()
                    }
                    Spacer()
                }
            }
        }
        .background(Color.black)
    }
}

struct MultiCameraView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        GeometryReader { geometry in
            let columns = geometry.size.width > geometry.size.height ? 2 : 1
            let rows = geometry.size.width > geometry.size.height ? 2 : 4
            
            LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: columns), spacing: 4) {
                // Main camera
                CameraFeedView(
                    title: "Main Camera",
                    videoTrack: viewModel.cameraTrack,
                    position: "main"
                )
                
                // Additional camera positions
                ForEach(["front", "side", "overhead"], id: \.self) { position in
                    CameraFeedView(
                        title: position.capitalized,
                        videoTrack: viewModel.additionalCameras[position],
                        position: position
                    )
                }
            }
            .padding(4)
        }
        .background(Color.black)
    }
}

struct CameraFeedView: View {
    let title: String
    let videoTrack: (any VideoTrack)?
    let position: String
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        ZStack {
            if let track = videoTrack {
                TrackView(track: track)
                    .aspectRatio(contentMode: .fit)
            } else {
                VStack {
                    Image(systemName: "video.slash")
                        .font(.largeTitle)
                        .foregroundStyle(.gray)
                    Text(title)
                        .foregroundStyle(.gray)
                    
                    if viewModel.combatFeatures.contains(.multiCamera) {
                        Button("Connect Camera") {
                            Task {
                                await viewModel.connectAdditionalCamera(position: position)
                            }
                        }
                        .buttonStyle(.bordered)
                        .padding(.top)
                    }
                }
            }
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemGray6))
        .clipShape(RoundedRectangle(cornerRadius: 8))
        .overlay(
            RoundedRectangle(cornerRadius: 8)
                .stroke(Color(.systemGray4), lineWidth: 1)
        )
    }
}

struct ConnectionBadge: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        HStack(spacing: 8) {
            Circle()
                .fill(connectionColor)
                .frame(width: 8, height: 8)
            
            Text(connectionText)
                .font(.caption)
                .foregroundStyle(.white)
        }
        .padding(.horizontal, 12)
        .padding(.vertical, 6)
        .background(Capsule().fill(Color.black.opacity(0.5)))
    }
    
    private var connectionColor: Color {
        switch viewModel.connectionState {
        case .connected:
            return .green
        case .connecting, .reconnecting:
            return .orange
        case .disconnected:
            return .red
        }
    }
    
    private var connectionText: String {
        switch viewModel.connectionState {
        case .connected:
            return viewModel.agent != nil ? "Coach Connected" : "Waiting for Coach"
        case .connecting:
            return "Connecting..."
        case .reconnecting:
            return "Reconnecting..."
        case .disconnected:
            return "Disconnected"
        }
    }
}

#Preview {
    CombatView()
        .environment(CombatMirrorViewModel())
}
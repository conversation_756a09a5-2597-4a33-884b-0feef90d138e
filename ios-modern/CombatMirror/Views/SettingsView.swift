import SwiftUI
import AVFoundation

struct SettingsView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        NavigationStack {
            List {
                // Audio Settings
                Section("Audio") {
                    if !viewModel.audioDevices.isEmpty {
                        Picker("Microphone", selection: .constant(viewModel.selectedAudioDeviceID)) {
                            ForEach(viewModel.audioDevices, id: \.deviceId) { device in
                                Text(device.name).tag(device.deviceId)
                            }
                        }
                    }
                }
                
                // Video Settings
                if viewModel.combatFeatures.contains(.video) {
                    Section("Video") {
                        if !viewModel.videoDevices.isEmpty {
                            Picker("Camera", selection: .constant(viewModel.selectedVideoDeviceID ?? "")) {
                                ForEach(viewModel.videoDevices, id: \.uniqueID) { device in
                                    Text(device.localizedName).tag(device.uniqueID)
                                }
                            }
                        }
                        
                        Toggle("Enable Camera", isOn: .constant(viewModel.isCameraEnabled))
                            .disabled(true)
                    }
                }
                
                // Training Settings
                Section("Training") {
                    HStack {
                        Text("Session Duration")
                        Spacer()
                        Text("3 rounds")
                            .foregroundStyle(.secondary)
                    }
                    
                    HStack {
                        Text("Round Length")
                        Spacer()
                        Text("3 minutes")
                            .foregroundStyle(.secondary)
                    }
                    
                    HStack {
                        Text("Rest Period")
                        Spacer()
                        Text("1 minute")
                            .foregroundStyle(.secondary)
                    }
                }
                
                // About
                Section("About") {
                    HStack {
                        Text("Version")
                        Spacer()
                        Text("1.0.0")
                            .foregroundStyle(.secondary)
                    }
                    
                    HStack {
                        Text("Agent Status")
                        Spacer()
                        if viewModel.agent != nil {
                            Circle()
                                .fill(Color.green)
                                .frame(width: 8, height: 8)
                            Text("Connected")
                                .foregroundStyle(.secondary)
                        } else {
                            Circle()
                                .fill(Color.red)
                                .frame(width: 8, height: 8)
                            Text("Disconnected")
                                .foregroundStyle(.secondary)
                        }
                    }
                }
            }
            .navigationTitle("Settings")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .topBarTrailing) {
                    Button("Done") {
                        dismiss()
                    }
                }
            }
        }
    }
}

#Preview {
    SettingsView()
        .environment(CombatMirrorViewModel())
}
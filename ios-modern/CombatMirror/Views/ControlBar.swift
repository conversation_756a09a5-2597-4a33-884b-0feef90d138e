import SwiftUI

struct ControlBar: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    @State private var showModeMenu = false
    
    var body: some View {
        HStack(spacing: 20) {
            // Microphone toggle
            ControlButton(
                icon: viewModel.isMicrophoneEnabled ? "mic.fill" : "mic.slash.fill",
                isActive: viewModel.isMicrophoneEnabled,
                action: {
                    Task { await viewModel.toggleMicrophone() }
                }
            )
            
            // Camera toggle
            if viewModel.combatFeatures.contains(.video) {
                ControlButton(
                    icon: viewModel.isCameraEnabled ? "video.fill" : "video.slash.fill",
                    isActive: viewModel.isCameraEnabled,
                    action: {
                        Task { await viewModel.toggleCamera() }
                    }
                )
                
                // Camera switch (mobile only)
                if viewModel.canSwitchCamera && viewModel.isCameraEnabled {
                    ControlButton(
                        icon: "camera.rotate",
                        isActive: false,
                        action: {
                            Task { await viewModel.switchCamera() }
                        }
                    )
                }
            }
            
            Spacer()
            
            // Mode selector
            if viewModel.combatFeatures.contains(.multiCamera) {
                Menu {
                    Button(action: { viewModel.switchInteractionMode(to: .voice) }) {
                        Label("Voice Mode", systemImage: "mic")
                    }
                    Button(action: { viewModel.switchInteractionMode(to: .video) }) {
                        Label("Video Mode", systemImage: "video")
                    }
                    Button(action: { viewModel.switchInteractionMode(to: .multiCamera) }) {
                        Label("Multi-Camera", systemImage: "camera.on.rectangle")
                    }
                } label: {
                    HStack {
                        Image(systemName: modeIcon)
                        Text(modeText)
                    }
                    .padding(.horizontal, 16)
                    .padding(.vertical, 12)
                    .background(Capsule().fill(Color.blue))
                    .foregroundStyle(.white)
                }
            }
            
            Spacer()
            
            // Disconnect button
            Button(action: {
                Task { await viewModel.disconnect() }
            }) {
                Image(systemName: "phone.down.fill")
                    .font(.title2)
                    .foregroundStyle(.white)
                    .padding(16)
                    .background(Circle().fill(Color.red))
            }
        }
        .padding(.horizontal)
        .padding(.vertical, 8)
        .background(
            RoundedRectangle(cornerRadius: 24)
                .fill(Color.black.opacity(0.8))
        )
    }
    
    private var modeIcon: String {
        switch viewModel.interactionMode {
        case .voice:
            return "mic"
        case .video:
            return "video"
        case .multiCamera:
            return "camera.on.rectangle"
        }
    }
    
    private var modeText: String {
        switch viewModel.interactionMode {
        case .voice:
            return "Voice"
        case .video:
            return "Video"
        case .multiCamera:
            return "Multi-Cam"
        }
    }
}

struct ControlButton: View {
    let icon: String
    let isActive: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.title2)
                .foregroundStyle(.white)
                .padding(12)
                .background(
                    Circle().fill(isActive ? Color.blue : Color.gray)
                )
        }
    }
}

#Preview {
    ZStack {
        Color.black
        ControlBar()
            .environment(CombatMirrorViewModel())
    }
}
import SwiftUI
import Charts

struct MetricsSection: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    @State private var selectedMetric = "balance"
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // Current metrics grid
                if let metrics = viewModel.currentMetrics {
                    CurrentMetricsGrid(metrics: metrics)
                }
                
                // Metrics history chart
                if !viewModel.metricsHistory.isEmpty {
                    MetricsChart(
                        history: viewModel.metricsHistory,
                        selectedMetric: $selectedMetric
                    )
                }
                
                // Training stats
                TrainingStatsView()
            }
            .padding()
        }
    }
}

struct CurrentMetricsGrid: View {
    let metrics: CombatMetrics
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Live Metrics")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            LazyVGrid(columns: [
                GridItem(.flexible()),
                GridItem(.flexible())
            ], spacing: 12) {
                MetricCard(
                    title: "<PERSON><PERSON>",
                    value: "\(Int(metrics.stanceQuality * 100))%",
                    icon: "figure.walk",
                    color: colorForScore(metrics.stanceQuality)
                )
                
                MetricCard(
                    title: "Guard",
                    value: metrics.guardPosition,
                    icon: "shield",
                    color: .blue
                )
                
                MetricCard(
                    title: "Balance",
                    value: "\(Int(metrics.balanceScore))",
                    icon: "scalemass",
                    color: colorForScore(metrics.balanceScore / 100)
                )
                
                MetricCard(
                    title: "Defense",
                    value: "\(Int(metrics.defensiveRating * 100))%",
                    icon: "shield.checkered",
                    color: colorForScore(metrics.defensiveRating)
                )
                
                MetricCard(
                    title: "Head Move",
                    value: String(format: "%.1f", metrics.headMovement),
                    icon: "head.profile",
                    color: .purple
                )
                
                MetricCard(
                    title: "Fatigue",
                    value: "\(Int(metrics.fatigueLevel * 100))%",
                    icon: "battery.25",
                    color: colorForFatigue(metrics.fatigueLevel)
                )
            }
        }
    }
    
    private func colorForScore(_ score: Double) -> Color {
        switch score {
        case 0.8...1.0:
            return .green
        case 0.6..<0.8:
            return .yellow
        default:
            return .red
        }
    }
    
    private func colorForFatigue(_ fatigue: Double) -> Color {
        switch fatigue {
        case 0..<0.3:
            return .green
        case 0.3..<0.6:
            return .yellow
        default:
            return .red
        }
    }
}

struct MetricCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    
    var body: some View {
        VStack(spacing: 8) {
            HStack {
                Image(systemName: icon)
                    .foregroundStyle(color)
                Spacer()
            }
            
            Text(value)
                .font(.title2.bold())
                .foregroundStyle(color)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            Text(title)
                .font(.caption)
                .foregroundStyle(.secondary)
                .frame(maxWidth: .infinity, alignment: .leading)
        }
        .padding()
        .background(Color(.systemGray5))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
}

struct MetricsChart: View {
    let history: [CombatMetrics]
    @Binding var selectedMetric: String
    
    private let metrics = [
        ("balance", "Balance Score"),
        ("stance", "Stance Quality"),
        ("defense", "Defense Rating"),
        ("fatigue", "Fatigue Level")
    ]
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                Text("Performance Trend")
                    .font(.headline)
                
                Spacer()
                
                Picker("Metric", selection: $selectedMetric) {
                    ForEach(metrics, id: \.0) { metric in
                        Text(metric.1).tag(metric.0)
                    }
                }
                .pickerStyle(.menu)
                .labelsHidden()
            }
            
            Chart(Array(history.enumerated()), id: \.offset) { index, metric in
                LineMark(
                    x: .value("Time", index),
                    y: .value("Value", metricValue(for: metric))
                )
                .foregroundStyle(.blue)
                .lineStyle(StrokeStyle(lineWidth: 2))
                
                AreaMark(
                    x: .value("Time", index),
                    y: .value("Value", metricValue(for: metric))
                )
                .foregroundStyle(.blue.opacity(0.1))
            }
            .frame(height: 150)
            .chartYScale(domain: 0...100)
            .chartXAxis(.hidden)
        }
        .padding()
        .background(Color(.systemGray5))
        .clipShape(RoundedRectangle(cornerRadius: 12))
    }
    
    private func metricValue(for metric: CombatMetrics) -> Double {
        switch selectedMetric {
        case "balance":
            return metric.balanceScore
        case "stance":
            return metric.stanceQuality * 100
        case "defense":
            return metric.defensiveRating * 100
        case "fatigue":
            return metric.fatigueLevel * 100
        default:
            return 0
        }
    }
}

struct TrainingStatsView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        VStack(spacing: 12) {
            Text("Session Stats")
                .font(.headline)
                .frame(maxWidth: .infinity, alignment: .leading)
            
            if let metrics = viewModel.currentMetrics {
                HStack {
                    StatItem(
                        label: "Punches",
                        value: "\(metrics.punchCount)"
                    )
                    
                    Divider()
                    
                    StatItem(
                        label: "Focus",
                        value: metrics.techniqueFocus
                    )
                }
                .frame(height: 60)
                
                // Improvement suggestion
                if !metrics.improvementArea.isEmpty {
                    HStack {
                        Image(systemName: "lightbulb")
                            .foregroundStyle(.yellow)
                        Text(metrics.improvementArea)
                            .font(.callout)
                    }
                    .padding()
                    .frame(maxWidth: .infinity)
                    .background(Color(.systemYellow).opacity(0.1))
                    .clipShape(RoundedRectangle(cornerRadius: 8))
                }
            }
        }
    }
}

struct StatItem: View {
    let label: String
    let value: String
    
    var body: some View {
        VStack(spacing: 4) {
            Text(value)
                .font(.title3.bold())
            Text(label)
                .font(.caption)
                .foregroundStyle(.secondary)
        }
        .frame(maxWidth: .infinity)
    }
}

#Preview {
    MetricsSection()
        .environment(CombatMirrorViewModel())
}
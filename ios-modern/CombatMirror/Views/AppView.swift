import SwiftUI
import LiveKit

struct AppView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    
    var body: some View {
        Group {
            switch viewModel.connectionState {
            case .disconnected, .connecting:
                ConnectView()
            case .connected, .reconnecting:
                CombatView()
            }
        }
        .animation(.easeInOut(duration: 0.3), value: viewModel.connectionState)
    }
}

struct ConnectView: View {
    @Environment(CombatMirrorViewModel.self) private var viewModel
    @State private var roomName = ""
    @State private var isConnecting = false
    
    var body: some View {
        VStack(spacing: 32) {
            VStack(spacing: 16) {
                Image(systemName: "figure.boxing")
                    .font(.system(size: 80))
                    .foregroundStyle(.red)
                
                Text("Combat Mirror")
                    .font(.largeTitle.bold())
                
                Text("AI-Powered Combat Training")
                    .font(.headline)
                    .foregroundStyle(.secondary)
            }
            
            VStack(spacing: 20) {
                if viewModel.combatFeatures.contains(.multiCamera) {
                    TextField("Room name (optional)", text: $roomName)
                        .textFieldStyle(.roundedBorder)
                        .autocapitalization(.none)
                        .frame(maxWidth: 300)
                }
                
                Button(action: connect) {
                    Group {
                        if isConnecting {
                            ProgressView()
                                .progressViewStyle(CircularProgressViewStyle())
                                .scaleEffect(0.8)
                        } else {
                            Text("Start Training Session")
                        }
                    }
                    .frame(maxWidth: .infinity)
                }
                .buttonStyle(.borderedProminent)
                .controlSize(.large)
                .disabled(isConnecting)
                .frame(maxWidth: 300)
                
                if viewModel.combatFeatures.contains(.multiCamera) {
                    Text("Leave room name empty for auto-generated room")
                        .font(.caption)
                        .foregroundStyle(.secondary)
                }
            }
        }
        .padding()
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .background(Color(.systemBackground))
    }
    
    private func connect() {
        isConnecting = true
        Task {
            await viewModel.connect(roomName: roomName.isEmpty ? nil : roomName)
            await MainActor.run {
                isConnecting = false
            }
        }
    }
}

#Preview {
    AppView()
        .environment(CombatMirrorViewModel())
}
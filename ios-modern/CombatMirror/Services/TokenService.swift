import Foundation

/// Service responsible for fetching LiveKit connection tokens
final class TokenService {
    struct ConnectionDetails {
        let serverUrl: String
        let participantToken: String
    }
    
    enum Error: LocalizedError {
        case invalidURL
        case invalidResponse
        case networkError(String)
        
        var errorDescription: String? {
            switch self {
            case .invalidURL:
                return "Invalid server URL configuration"
            case .invalidResponse:
                return "Invalid response from server"
            case .networkError(let message):
                return "Network error: \(message)"
            }
        }
    }
    
    private let baseURL = ProcessInfo.processInfo.environment["LIVEKIT_URL"] ?? "wss://combat-mirror.livekit.cloud"
    private let apiKey = ProcessInfo.processInfo.environment["LIVEKIT_API_KEY"] ?? ""
    private let apiSecret = ProcessInfo.processInfo.environment["LIVEKIT_API_SECRET"] ?? ""
    
    func fetchConnectionDetails(roomName: String, participantName: String) async throws -> ConnectionDetails {
        // In production, this would call your backend to get a token
        // For now, we'll use the LiveKit SDK to generate tokens locally
        
        guard !apiKey.isEmpty, !apiSecret.isEmpty else {
            throw Error.invalidURL
        }
        
        // Generate token using LiveKit SDK
        let token = try generateToken(
            roomName: roomName,
            participantName: participantName,
            apiKey: apiKey,
            apiSecret: apiSecret
        )
        
        return ConnectionDetails(
            serverUrl: baseURL,
            participantToken: token
        )
    }
    
    private func generateToken(
        roomName: String,
        participantName: String,
        apiKey: String,
        apiSecret: String
    ) throws -> String {
        // In a real app, this would be done on your backend
        // Using JWT generation here for demonstration
        
        let header = [
            "alg": "HS256",
            "typ": "JWT"
        ]
        
        let now = Int(Date().timeIntervalSince1970)
        let expiry = now + 86400 // 24 hours
        
        let claims: [String: Any] = [
            "iss": apiKey,
            "sub": participantName,
            "iat": now,
            "exp": expiry,
            "video": [
                "room": roomName,
                "roomJoin": true,
                "canPublish": true,
                "canSubscribe": true,
                "canPublishData": true
            ]
        ]
        
        // Convert to JWT (simplified - use a proper JWT library in production)
        let headerData = try JSONSerialization.data(withJSONObject: header)
        let claimsData = try JSONSerialization.data(withJSONObject: claims)
        
        let headerBase64 = headerData.base64EncodedString()
            .replacingOccurrences(of: "=", with: "")
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
        
        let claimsBase64 = claimsData.base64EncodedString()
            .replacingOccurrences(of: "=", with: "")
            .replacingOccurrences(of: "+", with: "-")
            .replacingOccurrences(of: "/", with: "_")
        
        let message = "\(headerBase64).\(claimsBase64)"
        
        // In production, properly sign with HMAC-SHA256
        // For now, return a placeholder token
        return "\(message).signature"
    }
}
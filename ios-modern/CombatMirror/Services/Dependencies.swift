import SwiftUI
import LiveKit

// MARK: - Dependency Key Protocol

protocol DependencyKey {
    associatedtype Value
    static var defaultValue: Value { get }
}

// MARK: - Dependency Values

struct DependencyValues {
    private var values: [ObjectIdentifier: Any] = [:]
    
    subscript<K: DependencyKey>(key: K.Type) -> K.Value {
        get {
            values[ObjectIdentifier(key)] as? K.Value ?? K.defaultValue
        }
        set {
            values[ObjectIdentifier(key)] = newValue
        }
    }
}

// MARK: - Dependency Property Wrapper

@propertyWrapper
struct Dependency<T> {
    private let keyPath: WritableKeyPath<DependencyValues, T>
    
    var wrappedValue: T {
        DependencyContainer.shared.values[keyPath: keyPath]
    }
    
    init(_ keyPath: WritableKeyPath<DependencyValues, T>) {
        self.keyPath = keyPath
    }
}

// MARK: - Dependency Container

final class DependencyContainer {
    static let shared = DependencyContainer()
    var values = DependencyValues()
    
    private init() {}
}

// MARK: - Room Dependency

struct RoomKey: DependencyKey {
    static var defaultValue: Room = Room()
}

extension DependencyValues {
    var room: Room {
        get { self[RoomKey.self] }
        set { self[RoomKey.self] = newValue }
    }
}

// MARK: - Token Service Dependency

struct TokenServiceKey: DependencyKey {
    static var defaultValue: TokenService = TokenService()
}

extension DependencyValues {
    var tokenService: TokenService {
        get { self[TokenServiceKey.self] }
        set { self[TokenServiceKey.self] = newValue }
    }
}

// MARK: - Error Handler Dependency

struct ErrorHandlerKey: DependencyKey {
    static var defaultValue: ErrorHandler = { error in
        if let error = error {
            print("Error: \(error.localizedDescription)")
        }
    }
}

extension DependencyValues {
    var errorHandler: ErrorHandler {
        get { self[ErrorHandlerKey.self] }
        set { self[ErrorHandlerKey.self] = newValue }
    }
}

// MARK: - Type Aliases

typealias ErrorHandler = (Error?) -> Void
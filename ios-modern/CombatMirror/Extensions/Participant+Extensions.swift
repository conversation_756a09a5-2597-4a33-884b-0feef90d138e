import LiveKit

extension Room {
    /// Returns the first participant that appears to be an agent
    var agentParticipant: Participant? {
        remoteParticipants.values.first { participant in
            // Check if participant has agent metadata or name pattern
            participant.name?.lowercased().contains("agent") == true ||
            participant.identity.lowercased().contains("agent") ||
            participant.metadata?.lowercased().contains("agent") == true
        }
    }
}

extension Participant {
    /// Check if this participant is publishing avatar video
    var avatarWorker: Participant? {
        // In multi-modal agents, avatar might be published by same or different participant
        if videoTracks.contains(where: { $0.name?.contains("avatar") == true }) {
            return self
        }
        return nil
    }
    
    /// Get first camera video track
    var firstCameraVideoTrack: VideoTrack? {
        videoTracks.first(where: { $0.source == .camera })?.track as? VideoTrack
    }
    
    /// Get first audio track
    var firstAudioTrack: AudioTrack? {
        audioTracks.first(where: { $0.source == .microphone })?.track as? AudioTrack
    }
}

extension LocalParticipant {
    /// Check if microphone is enabled
    func isMicrophoneEnabled() -> Bool {
        audioTracks.first(where: { $0.source == .microphone })?.track.isEnabled ?? false
    }
    
    /// Check if camera is enabled
    func isCameraEnabled() -> Bool {
        videoTracks.first(where: { $0.source == .camera })?.track.isEnabled ?? false
    }
    
    /// Check if screen share is enabled
    func isScreenShareEnabled() -> Bool {
        videoTracks.first(where: { $0.source == .screenShareVideo })?.track.isEnabled ?? false
    }
    
    /// Get first screen share video track
    var firstScreenShareVideoTrack: VideoTrack? {
        videoTracks.first(where: { $0.source == .screenShareVideo })?.track as? VideoTrack
    }
}
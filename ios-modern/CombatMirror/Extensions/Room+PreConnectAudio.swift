import LiveKit
import AVFoundation

extension Room {
    /// Connect to room with pre-connect audio capture
    /// This allows capturing audio before the room is connected for a smoother experience
    func withPreConnectAudio(_ block: @escaping () async throws -> Void) async throws {
        // Request microphone permission first
        let audioStatus = AVAudioSession.sharedInstance().recordPermission
        
        switch audioStatus {
        case .undetermined:
            await withCheckedContinuation { continuation in
                AVAudioSession.sharedInstance().requestRecordPermission { _ in
                    continuation.resume()
                }
            }
        case .denied:
            throw CombatMirrorViewModel.Error.cameraAccessDenied
        case .granted:
            break
        @unknown default:
            break
        }
        
        // Configure audio session for recording
        do {
            try AVAudioSession.sharedInstance().setCategory(.playAndRecord, mode: .voiceChat)
            try AVAudioSession.sharedInstance().setActive(true)
        } catch {
            print("Failed to configure audio session: \(error)")
        }
        
        // Execute the connection block
        try await block()
    }
}
import LiveKit
import Swift<PERSON>

@main
struct CombatMirrorApp: App {
    // Create the root view model
    private let viewModel = CombatMirrorViewModel()

    var body: some Scene {
        WindowGroup {
            AppView()
                .environment(viewModel)
        }
        #if os(visionOS)
        .windowStyle(.plain)
        .windowResizability(.contentMinSize)
        .defaultSize(width: 1500, height: 800)
        #endif
    }
}

/// Combat Mirror specific features
struct CombatFeatures: OptionSet {
    let rawValue: Int

    static let voice = Self(rawValue: 1 << 0)
    static let video = Self(rawValue: 1 << 1)
    static let metrics = Self(rawValue: 1 << 2)
    static let transcription = Self(rawValue: 1 << 3)
    static let multiCamera = Self(rawValue: 1 << 4)

    // Combat Mirror uses all features
    static let current: Self = [.voice, .video, .metrics, .transcription, .multiCamera]
}
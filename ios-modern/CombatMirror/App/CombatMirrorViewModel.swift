@preconcurrency import AVFoundation
import Combine
import LiveKit
import Observation

/// The main view model for Combat Mirror app
/// Manages connection, tracks, metrics, and multi-camera support
@MainActor
@Observable
final class CombatMirrorViewModel {
    // MARK: - Constants
    
    private enum Constants {
        static let agentConnectionTimeout: TimeInterval = 10
    }
    
    // MARK: - Errors
    
    enum Error: LocalizedError {
        case agentNotConnected
        case cameraAccessDenied
        
        var errorDescription: String? {
            switch self {
            case .agentNotConnected:
                "Combat AI Coach did not connect to the training session"
            case .cameraAccessDenied:
                "Camera access is required for combat analysis"
            }
        }
    }
    
    // MARK: - Modes
    
    enum InteractionMode {
        case voice
        case video
        case multiCamera
    }
    
    let combatFeatures: CombatFeatures
    
    // MARK: - State
    
    // MARK: Connection
    
    private(set) var connectionState: ConnectionState = .disconnected
    private(set) var isAnalyzing = false
    var isInteractive: Bool {
        switch connectionState {
        case .disconnected where isAnalyzing,
             .connecting where isAnalyzing,
             .connected,
             .reconnecting:
            true
        default:
            false
        }
    }
    
    private(set) var agent: Participant?
    private(set) var interactionMode: InteractionMode = .video
    
    // MARK: Combat Metrics
    
    private(set) var currentMetrics: CombatMetrics?
    private(set) var metricsHistory: [CombatMetrics] = []
    private(set) var coachingMessages: [CoachingMessage] = []
    
    // MARK: Tracks
    
    private(set) var isMicrophoneEnabled = false
    private(set) var audioTrack: (any AudioTrack)?
    private(set) var isCameraEnabled = false
    private(set) var cameraTrack: (any VideoTrack)?
    private(set) var isScreenShareEnabled = false
    private(set) var screenShareTrack: (any VideoTrack)?
    
    private(set) var agentAudioTrack: (any AudioTrack)?
    private(set) var agentVideoTrack: (any VideoTrack)?
    
    // Multi-camera support
    private(set) var additionalCameras: [String: (any VideoTrack)] = [:]
    
    // MARK: Devices
    
    private(set) var audioDevices: [AudioDevice] = AudioManager.shared.inputDevices
    private(set) var selectedAudioDeviceID: String = AudioManager.shared.inputDevice.deviceId
    
    private(set) var videoDevices: [AVCaptureDevice] = []
    private(set) var selectedVideoDeviceID: String?
    
    private(set) var canSwitchCamera = false
    
    // MARK: - Dependencies
    
    @ObservationIgnored
    @Dependency(\.room) private var room
    @ObservationIgnored
    @Dependency(\.tokenService) private var tokenService
    @ObservationIgnored
    @Dependency(\.errorHandler) private var errorHandler
    
    // MARK: - Initialization
    
    init(combatFeatures: CombatFeatures = .current) {
        self.combatFeatures = combatFeatures
        
        observeRoom()
        observeDevices()
        observeMetrics()
    }
    
    private func observeRoom() {
        Task { [weak self] in
            guard let changes = self?.room.changes else { return }
            for await _ in changes {
                guard let self else { return }
                
                connectionState = room.connectionState
                agent = room.agentParticipant
                
                isMicrophoneEnabled = room.localParticipant.isMicrophoneEnabled()
                audioTrack = room.localParticipant.firstAudioTrack
                isCameraEnabled = room.localParticipant.isCameraEnabled()
                cameraTrack = room.localParticipant.firstCameraVideoTrack
                isScreenShareEnabled = room.localParticipant.isScreenShareEnabled()
                screenShareTrack = room.localParticipant.firstScreenShareVideoTrack
                
                agentAudioTrack = room.agentParticipant?.audioTracks
                    .first(where: { $0.source == .microphone })?.track as? AudioTrack
                agentVideoTrack = room.agentParticipant?.videoTracks
                    .first(where: { $0.source == .camera })?.track as? VideoTrack
            }
        }
    }
    
    private func observeDevices() {
        do {
            try AudioManager.shared.set(microphoneMuteMode: .inputMixer)
            try AudioManager.shared.setRecordingAlwaysPreparedMode(true)
        } catch {
            errorHandler(error)
        }
        
        AudioManager.shared.onDeviceUpdate = { [weak self] _ in
            Task { @MainActor in
                self?.audioDevices = AudioManager.shared.inputDevices
                self?.selectedAudioDeviceID = AudioManager.shared.defaultInputDevice.deviceId
            }
        }
        
        Task {
            do {
                canSwitchCamera = try await CameraCapturer.canSwitchPosition()
                videoDevices = try await CameraCapturer.captureDevices()
                selectedVideoDeviceID = videoDevices.first?.uniqueID
            } catch {
                errorHandler(error)
            }
        }
    }
    
    private func observeMetrics() {
        // Set up data channel for combat metrics
        room.on(RoomEvent.dataReceived) { [weak self] data, participant, _, topic in
            guard let self, topic == "combat_metrics" else { return }
            
            do {
                let decoder = JSONDecoder()
                let metrics = try decoder.decode(CombatMetrics.self, from: data)
                await MainActor.run {
                    self.currentMetrics = metrics
                    self.metricsHistory.append(metrics)
                    
                    // Keep only last 100 metrics
                    if self.metricsHistory.count > 100 {
                        self.metricsHistory.removeFirst()
                    }
                }
            } catch {
                print("Failed to decode combat metrics: \(error)")
            }
        }
    }
    
    deinit {
        AudioManager.shared.onDeviceUpdate = nil
    }
    
    private func resetState() {
        isAnalyzing = false
        interactionMode = .video
        currentMetrics = nil
        metricsHistory.removeAll()
        coachingMessages.removeAll()
        additionalCameras.removeAll()
    }
    
    // MARK: - Connection
    
    func connect(roomName: String? = nil) async {
        errorHandler(nil)
        resetState()
        do {
            if combatFeatures.contains(.video) {
                try await connectWithVideo()
            } else if combatFeatures.contains(.voice) {
                try await connectWithVoice()
            } else {
                try await connectBasic()
            }
            
            try await checkAgentConnected()
        } catch {
            errorHandler(error)
            resetState()
        }
    }
    
    /// Connect with video analysis enabled
    private func connectWithVideo() async throws {
        try await requestCameraPermission()
        
        await MainActor.run { self.isAnalyzing = true }
        
        let connectionDetails = try await getConnection()
        
        try await room.connect(
            url: connectionDetails.serverUrl,
            token: connectionDetails.participantToken,
            connectOptions: .init(
                enableMicrophone: true,
                enableCamera: true
            )
        )
    }
    
    /// Connect with voice only
    private func connectWithVoice() async throws {
        try await room.withPreConnectAudio {
            await MainActor.run { self.isAnalyzing = true }
            
            let connectionDetails = try await self.getConnection()
            
            try await self.room.connect(
                url: connectionDetails.serverUrl,
                token: connectionDetails.participantToken,
                connectOptions: .init(enableMicrophone: true)
            )
        }
    }
    
    /// Basic connection
    private func connectBasic() async throws {
        let connectionDetails = try await getConnection()
        
        try await room.connect(
            url: connectionDetails.serverUrl,
            token: connectionDetails.participantToken,
            connectOptions: .init()
        )
    }
    
    private func getConnection() async throws -> TokenService.ConnectionDetails {
        let roomName = "combat-training-\(Int.random(in: 1000 ... 9999))"
        let participantName = "fighter-\(Int.random(in: 1000 ... 9999))"
        
        return try await tokenService.fetchConnectionDetails(
            roomName: roomName,
            participantName: participantName
        )!
    }
    
    func disconnect() async {
        await room.disconnect()
        resetState()
    }
    
    private func checkAgentConnected() async throws {
        try await Task.sleep(for: .seconds(Constants.agentConnectionTimeout))
        if connectionState == .connected, agent == nil {
            await disconnect()
            throw Error.agentNotConnected
        }
    }
    
    // MARK: - Actions
    
    func switchInteractionMode(to mode: InteractionMode) {
        interactionMode = mode
    }
    
    func toggleMicrophone() async {
        do {
            try await room.localParticipant.setMicrophone(enabled: !isMicrophoneEnabled)
        } catch {
            errorHandler(error)
        }
    }
    
    func toggleCamera() async {
        let enable = !isCameraEnabled
        do {
            // One video track at a time
            if enable, isScreenShareEnabled {
                try await room.localParticipant.setScreenShare(enabled: false)
            }
            
            let device = try await CameraCapturer.captureDevices().first(where: { $0.uniqueID == selectedVideoDeviceID })
            try await room.localParticipant.setCamera(enabled: enable, captureOptions: CameraCaptureOptions(device: device))
        } catch {
            errorHandler(error)
        }
    }
    
    func toggleScreenShare() async {
        let enable = !isScreenShareEnabled
        do {
            // One video track at a time
            if enable, isCameraEnabled {
                try await room.localParticipant.setCamera(enabled: false)
            }
            try await room.localParticipant.setScreenShare(enabled: enable)
        } catch {
            errorHandler(error)
        }
    }
    
    func switchCamera() async {
        guard let cameraCapturer = getCameraCapturer() else { return }
        do {
            try await cameraCapturer.switchCameraPosition()
        } catch {
            errorHandler(error)
        }
    }
    
    // MARK: - Multi-Camera Support
    
    func connectAdditionalCamera(position: String) async {
        // This would connect to additional camera streams from other devices
        // For now, this is a placeholder for multi-camera support
        print("Connecting additional camera at position: \(position)")
    }
    
    // MARK: - Combat-Specific Methods
    
    func sendTrainingCommand(_ command: TrainingCommand) async {
        do {
            let encoder = JSONEncoder()
            let data = try encoder.encode(command)
            
            try await room.localParticipant.publishData(
                data: data,
                reliable: true,
                topic: "training_command"
            )
        } catch {
            errorHandler(error)
        }
    }
    
    func addCoachingMessage(_ message: String, type: CoachingType) {
        let coaching = CoachingMessage(
            id: UUID(),
            message: message,
            type: type,
            timestamp: Date()
        )
        coachingMessages.append(coaching)
        
        // Keep only last 50 messages
        if coachingMessages.count > 50 {
            coachingMessages.removeFirst()
        }
    }
    
    // MARK: - Private Helpers
    
    private func getCameraCapturer() -> CameraCapturer? {
        guard let cameraTrack = cameraTrack as? LocalVideoTrack else { return nil }
        return cameraTrack.capturer as? CameraCapturer
    }
    
    private func requestCameraPermission() async throws {
        let status = AVCaptureDevice.authorizationStatus(for: .video)
        switch status {
        case .authorized:
            return
        case .notDetermined:
            let granted = await AVCaptureDevice.requestAccess(for: .video)
            if !granted {
                throw Error.cameraAccessDenied
            }
        default:
            throw Error.cameraAccessDenied
        }
    }
}

// MARK: - Supporting Types

struct CombatMetrics: Codable {
    let timestamp: Date
    let stanceQuality: Double
    let guardPosition: String
    let balanceScore: Double
    let postureScore: Double
    let defensiveRating: Double
    let punchCount: Int
    let headMovement: Double
    let fatigueLevel: Double
    let techniqueFocus: String
    let improvementArea: String
}

struct CoachingMessage: Identifiable {
    let id: UUID
    let message: String
    let type: CoachingType
    let timestamp: Date
}

enum CoachingType {
    case technique
    case encouragement
    case correction
    case drill
    case safety
    
    var color: Color {
        switch self {
        case .technique: return .blue
        case .encouragement: return .green
        case .correction: return .orange
        case .drill: return .purple
        case .safety: return .red
        }
    }
    
    var icon: String {
        switch self {
        case .technique: return "target"
        case .encouragement: return "hand.thumbsup"
        case .correction: return "exclamationmark.triangle"
        case .drill: return "figure.boxing"
        case .safety: return "exclamationmark.shield"
        }
    }
}

struct TrainingCommand: Codable {
    enum CommandType: String, Codable {
        case startRound
        case endRound
        case pauseTraining
        case resumeTraining
        case changeIntensity
    }
    
    let type: CommandType
    let parameters: [String: String]
}
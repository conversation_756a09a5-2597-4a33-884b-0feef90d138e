# Combat Mirror System - Staging Environment Variables
# Copy this file and update with your staging values

# API Keys (same as production but can use test keys)
GEMINI_API_KEY=your_gemini_api_key_here
LIVEKIT_API_KEY=APIfightmaster
LIVEKIT_API_SECRET=your_livekit_secret_here

# Staging URLs (will be auto-populated by deployment)
STAGING_API_URL=https://combat-mirror-api-staging.railway.app
STAGING_LIVEKIT_URL=wss://combat-mirror-livekit-staging.railway.app
STAGING_FRONTEND_URL=https://combat-mirror-frontend-staging.railway.app

# Staging-specific settings
NODE_ENV=staging
LOG_LEVEL=debug
DEBUG=true

# Enhanced monitoring for staging
ENABLE_PROFILING=true
ENABLE_TRACING=true
TRACE_SAMPLE_RATE=1.0

# Staging webhook for testing
STAGING_WEBHOOK_URL=https://webhook.site/your-webhook-id
STAGING_WEBHOOK_SECRET=staging_webhook_secret_123

# Feature flags
ENABLE_EXPERIMENTAL_FEATURES=true
ENABLE_DEBUG_UI=true
SHOW_PERFORMANCE_METRICS=true

# Staging database
REDIS_URL=redis://default:<EMAIL>:6379

# Staging limits (more generous for testing)
MAX_VIDEO_SIZE_MB=100
MAX_SESSION_DURATION_MINUTES=60
RATE_LIMIT_MULTIPLIER=2

# Testing accounts (if needed)
TEST_USER_EMAIL=<EMAIL>
TEST_USER_PASSWORD=staging_test_password_123

# Staging-specific integrations
SENTRY_DSN=https://<EMAIL>/combat-mirror
ANALYTICS_ID=G-STAGING123

# Development helpers
SKIP_PREFLIGHT_CHECK=true
FAST_REFRESH=true
GENERATE_SOURCEMAP=true
# CopyParty File Server - Complete Capabilities Guide

> **CopyParty** is a fast, lightweight, flexible file server that speaks multiple protocols and runs everywhere. It's a single Python file that transforms any system into a powerful file server.

## 🌐 Universal Protocol Support

CopyParty speaks **every file protocol you need**:
- **HTTP/HTTPS** - Standard web access
- **WebDAV** - Mount as network drive
- **FTP/FTPS** - Traditional file transfer
- **TFTP** - Trivial file transfer
- **MDNS** - Auto-discovery on LAN  
- **SSDP** - Universal Plug and Play discovery

## 🚀 Performance & Upload Excellence

### Lightning-Fast Uploads
- **Parallel chunk uploading** - Files split into chunks sent simultaneously
- **Multiple TCP connections** - Up to 5x faster than alternatives over long distances
- **No file size limits** - Upload terabyte-sized files without issues
- **Resumable uploads** - Power failures? No problem, resumes automatically
- **Live download while uploading** - Start downloading before upload completes

### Data Integrity & Protection
- **Checksum verification** - Every chunk validated for corruption protection
- **Hardware failure detection** - Has caught buggy sound card drivers corrupting data
- **Network error recovery** - Automatically retries corrupted chunks
- **File deduplication** - Skip uploads of existing files, create symlinks instead

## 🌍 Universal Compatibility

### Cross-Platform Server Support
Runs on **literally everything**:
- Linux, macOS, Windows (10, 7, even 2000!)
- Android, FreeBSD, Raspberry Pi, RISC-V
- Any system with Python or Docker

### Universal Client Support
Works with **every browser ever made**:
- Modern: Chrome, Firefox, Safari, Edge
- Ancient: Internet Explorer 6, Windows 3.11 (1992!)
- Gaming: Sony PSP, Nintendo 3DS
- Mobile: All Android/iOS browsers

## 🎵 Rich Media Experience

### Video & Audio Playback
- **Native video player** with hotkey controls
- **Audio player** with gapless playback and pre-loading
- **Real-time transcoding** - Play formats your browser doesn't support
- **Equalizer & compressor** - Bass boost and volume normalization
- **Lock screen controls** - Control playback from phone lock screen
- **RSS podcast feeds** - Sync to podcast apps like AntennaPod

### File Viewing & Editing
- **Image gallery** with rotation and thumbnails (press 'G')
- **Text file viewer** with syntax highlighting
- **Live log tailing** - Watch log files update in real-time
- **Markdown editor** - Two different editors to choose from
- **File previews** - Thumbnails for images and videos

## 📁 Advanced File Management

### Browser-Based Operations
- **Drag & drop uploads** - Drop entire folder structures
- **Clipboard operations** - Ctrl+C/Ctrl+V between tabs
- **Batch file operations** - Select multiple files for download/action
- **On-the-fly ZIP creation** - Download folders as ZIP, any size
- **Real-time file creation** - Create new files and directories
- **Batch rename** - Rename multiple files using media tags

### Search & Organization
- **Full-text search** - Find files by name and content
- **Metadata search** - Search music by audio tags
- **Smart indexing** - Fast search across large file collections
- **Custom folder structures** - Virtual filesystem mapping

## 🔒 Security & Access Control

### Flexible Authentication
- **User accounts** with individual permissions
- **Password hashing** support for secure storage
- **Permission levels**: read, write, move, delete, admin
- **Anonymous access** - Configure public/private areas

### Advanced Security Features
- **File keys** - Generate random passwords for each file
- **Volume shadowing** - Hide sensitive folders
- **Symlink control** - Configure symlink following behavior
- **Upload policies** - Max file size, disk usage, time limits
- **Auto-deletion** - Self-destruct timers for uploaded files

## 🔗 Sharing & Collaboration

### Dynamic Share Links
- **Temporary shares** - Create time-limited access to folders
- **Permission levels** - Read-only, read-write, or write-only shares
- **Password protection** - Optional password for share links
- **QR codes** - Easy mobile sharing
- **Undo uploads** - Users can delete their own uploads within 12 hours

### Integration & APIs
- **Command-line uploader** - Faster than web browsers
- **Sync functionality** - Like rsync but over HTTPS with deduplication
- **Mobile apps** - Android app for easy sharing
- **ShareX integration** - Upload from screenshot tools
- **cURL support** - Scriptable uploads and downloads

## 🛠️ Configuration & Customization

### Flexible Configuration
- **Volume mapping** - Map server folders to web URLs
- **Per-volume settings** - Different rules for different folders
- **Event hooks** - Custom scripts on upload/download events
- **Theme customization** - Multiple UI themes
- **Multi-language** - Internationalization support

### Monitoring & Control
- **Control panel** - See active uploads/downloads
- **Upload history** - Track all recent uploads
- **Health monitoring** - Built-in status endpoints
- **Config reload** - Update settings without restart

## 🏗️ Architecture & Design

### Philosophy
- **Your filesystem, your rules** - No database, just files and folders
- **Zero vendor lock-in** - Remove CopyParty, keep all your files
- **No telemetry** - No data collection, no auto-updater
- **Minimal breaking changes** - Stable, reliable updates
- **Security-first** - CVEs fixed within 24 hours (fastest: 2 hours)

### Technical Excellence
- **Single Python file** - Entire server in one file
- **Lightweight** - Minimal resource usage
- **Self-contained** - No external dependencies
- **Open source** - Fully transparent codebase

## 💡 Use Cases for Combat Mirror System

### Perfect for Our Video Analysis Platform
- **Multi-camera uploads** - Athletes upload training videos from multiple angles
- **Large file handling** - No limits on video file sizes
- **Resume interrupted uploads** - Critical for large video files
- **Live streaming integration** - Works with LiveKit recordings
- **Automatic organization** - Separate folders for uploads/recordings/exports
- **Share analysis reports** - Generate temporary links for coaches
- **Mobile uploads** - Athletes can upload directly from phones
- **Deduplication** - Save storage on identical training sessions

### Integration Benefits
- **REST API compatibility** - Works with our Express.js backend
- **Docker deployment** - Seamless container orchestration
- **Network drive mounting** - Direct filesystem access for processing
- **Webhook support** - Trigger analysis on upload completion
- **Multi-protocol access** - FTP for bulk operations, HTTP for web interface

## 🚀 Getting Started

### Quick Start Options
```bash
# Download and run the Python file
python copyparty.py

# Docker deployment
docker run -p 3923:3923 ghcr.io/9001/copyparty:latest

# With our docker-compose.yml
docker-compose up copyparty
```

### Configuration Example
```ini
[global]
e2dsa    # enable file deduplication
e2ts     # enable file indexing
j        # enable uploads
cors     # enable CORS for API access

[/uploads]
accs: rw    # read/write access
flags: dupe move    # allow duplicates and moving

[/recordings]  
accs: rw
flags: dupe ts    # timestamps in filenames
```

---

*CopyParty transforms any system into a professional-grade file server with features that rival enterprise solutions, all in a single Python file. Perfect for Combat Mirror's video analysis needs.*
import request from 'supertest';
import express from 'express';
import { jest } from '@jest/globals';

// Mock the server modules
jest.mock('../../server/logger.js', () => ({
  default: {
    info: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    debug: jest.fn(),
    requestMiddleware: (req: any, res: any, next: any) => next(),
  },
}));

jest.mock('../../server/services/tokenService.js', () => ({
  default: {
    generateAuthToken: jest.fn().mockReturnValue('mock-auth-token'),
    verifyAuthToken: jest.fn().mockReturnValue({
      userId: 'test-user',
      email: '<EMAIL>',
      roles: ['user'],
      jti: 'test-token-id',
    }),
    generateLiveKitToken: jest.fn().mockReturnValue('mock-livekit-token'),
  },
}));

describe('API Integration Tests', () => {
  let app: express.Application;

  beforeAll(async () => {
    // Set test environment variables
    process.env.NODE_ENV = 'test';
    process.env.GEMINI_API_KEY = 'test-key';
    process.env.JWT_SECRET = 'test-secret';
    process.env.LIVEKIT_API_KEY = 'test-key';
    process.env.LIVEKIT_API_SECRET = 'test-secret';

    // Import and setup the app after mocks are in place
    const { default: createApp } = await import('../../server/index.js');
    app = createApp;
  });

  describe('Health Check', () => {
    it('should return health status', async () => {
      const response = await request(app)
        .get('/api/health')
        .expect(200);

      expect(response.body).toHaveProperty('status', 'ok');
      expect(response.body).toHaveProperty('timestamp');
      expect(response.body).toHaveProperty('uptime');
    });
  });

  describe('Authentication', () => {
    describe('POST /api/auth/login', () => {
      it('should login with valid credentials', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'Demo123!@#',
          })
          .expect(200);

        expect(response.body).toHaveProperty('success', true);
        expect(response.body).toHaveProperty('token');
        expect(response.body).toHaveProperty('user');
        expect(response.body.user).toHaveProperty('email', '<EMAIL>');
      });

      it('should reject invalid credentials', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'wrongpassword',
          })
          .expect(401);

        expect(response.body).toHaveProperty('error', 'Invalid credentials');
      });

      it('should validate email format', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: 'invalid-email',
            password: 'Demo123!@#',
          })
          .expect(400);

        expect(response.body).toHaveProperty('error', 'Validation failed');
      });

      it('should validate password strength', async () => {
        const response = await request(app)
          .post('/api/auth/login')
          .send({
            email: '<EMAIL>',
            password: 'weak',
          })
          .expect(400);

        expect(response.body).toHaveProperty('error', 'Validation failed');
      });
    });

    describe('POST /api/auth/refresh', () => {
      it('should refresh token with valid auth', async () => {
        const response = await request(app)
          .post('/api/auth/refresh')
          .set('Authorization', 'Bearer mock-auth-token')
          .expect(200);

        expect(response.body).toHaveProperty('token');
      });

      it('should reject request without token', async () => {
        await request(app)
          .post('/api/auth/refresh')
          .expect(401);
      });
    });
  });

  describe('LiveKit Token Generation', () => {
    it('should generate token with valid auth', async () => {
      const response = await request(app)
        .post('/api/livekit/token')
        .set('Authorization', 'Bearer mock-auth-token')
        .send({
          roomName: 'test-room',
          participantName: 'test-participant',
          canPublish: true,
          canSubscribe: true,
        })
        .expect(200);

      expect(response.body).toHaveProperty('token', 'mock-livekit-token');
      expect(response.body).toHaveProperty('serverUrl');
    });

    it('should reject request without auth', async () => {
      await request(app)
        .post('/api/livekit/token')
        .send({
          roomName: 'test-room',
          participantName: 'test-participant',
        })
        .expect(401);
    });

    it('should validate room name format', async () => {
      const response = await request(app)
        .post('/api/livekit/token')
        .set('Authorization', 'Bearer mock-auth-token')
        .send({
          roomName: 'invalid room name!',
          participantName: 'test-participant',
        })
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });
  });

  describe('Analysis Endpoint', () => {
    const validAnalysisRequest = {
      participantId: 'P001',
      sessionType: 'Baseline Session (5 mins)',
      metrics: {
        punchRate: 75,
        punchCount: 150,
        punchVelocity: 8.5,
        headMovement: 2.3,
        postureScore: 85,
        gaitBalance: 92,
        fatigue: 25,
      },
    };

    it('should analyze with valid auth and data', async () => {
      // Mock Gemini API response
      const mockGeminiResponse = {
        response: {
          text: () => 'Mock analysis report',
        },
      };

      jest.doMock('@google/generative-ai', () => ({
        GoogleGenerativeAI: jest.fn().mockImplementation(() => ({
          getGenerativeModel: jest.fn().mockReturnValue({
            generateContent: jest.fn().mockResolvedValue(mockGeminiResponse),
          }),
        })),
      }));

      const response = await request(app)
        .post('/api/analyze')
        .set('Authorization', 'Bearer mock-auth-token')
        .send(validAnalysisRequest)
        .expect(200);

      expect(response.body).toHaveProperty('report');
    });

    it('should reject request without auth', async () => {
      await request(app)
        .post('/api/analyze')
        .send(validAnalysisRequest)
        .expect(401);
    });

    it('should validate metrics ranges', async () => {
      const invalidRequest = {
        ...validAnalysisRequest,
        metrics: {
          ...validAnalysisRequest.metrics,
          punchRate: 300, // Invalid: exceeds max of 200
        },
      };

      const response = await request(app)
        .post('/api/analyze')
        .set('Authorization', 'Bearer mock-auth-token')
        .send(invalidRequest)
        .expect(400);

      expect(response.body).toHaveProperty('error', 'Validation failed');
    });

    it('should enforce rate limiting', async () => {
      // Make multiple requests quickly to trigger rate limit
      const requests = Array(12).fill(null).map(() =>
        request(app)
          .post('/api/analyze')
          .set('Authorization', 'Bearer mock-auth-token')
          .send(validAnalysisRequest)
      );

      const responses = await Promise.all(requests);
      
      // Some requests should be rate limited (429)
      const rateLimitedResponses = responses.filter(r => r.status === 429);
      expect(rateLimitedResponses.length).toBeGreaterThan(0);
    });
  });

  describe('File Upload', () => {
    it('should upload valid video file with auth', async () => {
      const response = await request(app)
        .post('/api/files/upload')
        .set('Authorization', 'Bearer mock-auth-token')
        .attach('file', Buffer.from('fake video data'), {
          filename: 'test.mp4',
          contentType: 'video/mp4',
        })
        .expect(200);

      expect(response.body).toHaveProperty('filename');
    });

    it('should reject non-video files', async () => {
      await request(app)
        .post('/api/files/upload')
        .set('Authorization', 'Bearer mock-auth-token')
        .attach('file', Buffer.from('fake text data'), {
          filename: 'test.txt',
          contentType: 'text/plain',
        })
        .expect(400);
    });
  });
});

import {
  validateParticipantId,
  validateSessionType,
  validateVideoFile,
  sanitizeMetrics,
  RateLimiter,
} from '../../../utils/validation';

describe('Validation Utils', () => {
  describe('validateParticipantId', () => {
    it('should accept valid participant IDs', () => {
      expect(() => validateParticipantId('P001')).not.toThrow();
      expect(() => validateParticipantId('participant-123')).not.toThrow();
      expect(() => validateParticipantId('user_456')).not.toThrow();
    });

    it('should reject invalid participant IDs', () => {
      expect(() => validateParticipantId('')).toThrow('Participant ID must be between 1 and 50 characters');
      expect(() => validateParticipantId('a'.repeat(51))).toThrow('Participant ID must be between 1 and 50 characters');
      expect(() => validateParticipantId('invalid@id')).not.toThrow(); // Should sanitize
    });

    it('should sanitize participant IDs', () => {
      expect(validateParticipantId('test@#$%')).toBe('test');
      expect(validateParticipantId('user 123')).toBe('user123');
    });
  });

  describe('validateSessionType', () => {
    it('should accept valid session types', () => {
      expect(validateSessionType('Baseline Session (5 mins)')).toBe(true);
      expect(validateSessionType('Training Session (15 mins)')).toBe(true);
      expect(validateSessionType('Recovery Analysis (5 mins)')).toBe(true);
      expect(validateSessionType('Video Analysis (15 mins)')).toBe(true);
    });

    it('should reject invalid session types', () => {
      expect(validateSessionType('Invalid Session')).toBe(false);
      expect(validateSessionType('')).toBe(false);
      expect(validateSessionType('Custom Session (10 mins)')).toBe(false);
    });
  });

  describe('validateVideoFile', () => {
    it('should accept valid video files', () => {
      const validFile = new File(['test'], 'test.mp4', { type: 'video/mp4' });
      Object.defineProperty(validFile, 'size', { value: 10 * 1024 * 1024 }); // 10MB
      
      expect(() => validateVideoFile(validFile)).not.toThrow();
    });

    it('should reject files that are too large', () => {
      const largeFile = new File(['test'], 'large.mp4', { type: 'video/mp4' });
      Object.defineProperty(largeFile, 'size', { value: 100 * 1024 * 1024 }); // 100MB (default limit is 50MB)
      
      expect(() => validateVideoFile(largeFile)).toThrow('File size exceeds');
    });

    it('should reject invalid file types', () => {
      const invalidFile = new File(['test'], 'test.txt', { type: 'text/plain' });
      
      expect(() => validateVideoFile(invalidFile)).toThrow('Invalid file type');
    });
  });

  describe('sanitizeMetrics', () => {
    it('should sanitize and clamp metrics values', () => {
      const input = {
        punchRate: 250, // Should be clamped to 200
        punchCount: -5, // Should be clamped to 0
        punchVelocity: 'invalid', // Should become 0
        headMovement: 15, // Should be clamped to 10
        postureScore: 150, // Should be clamped to 100
        gaitBalance: -10, // Should be clamped to 0
        fatigue: 'abc', // Should become 0
      };

      const result = sanitizeMetrics(input);

      expect(result.punchRate).toBe(200);
      expect(result.punchCount).toBe(0);
      expect(result.punchVelocity).toBe(0);
      expect(result.headMovement).toBe(10);
      expect(result.postureScore).toBe(100);
      expect(result.gaitBalance).toBe(0);
      expect(result.fatigue).toBe(0);
    });

    it('should handle valid metrics correctly', () => {
      const input = {
        punchRate: 75,
        punchCount: 150,
        punchVelocity: 8.5,
        headMovement: 2.3,
        postureScore: 85,
        gaitBalance: 92,
        fatigue: 25,
      };

      const result = sanitizeMetrics(input);

      expect(result).toEqual(input);
    });
  });

  describe('RateLimiter', () => {
    let rateLimiter: RateLimiter;

    beforeEach(() => {
      rateLimiter = new RateLimiter(3, 1000); // 3 requests per second
    });

    it('should allow requests within limit', () => {
      expect(rateLimiter.check('user1')).toBe(true);
      expect(rateLimiter.check('user1')).toBe(true);
      expect(rateLimiter.check('user1')).toBe(true);
    });

    it('should block requests exceeding limit', () => {
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      
      expect(rateLimiter.check('user1')).toBe(false);
    });

    it('should handle different users separately', () => {
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      
      expect(rateLimiter.check('user2')).toBe(true);
    });

    it('should reset after time window', async () => {
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      
      expect(rateLimiter.check('user1')).toBe(false);
      
      // Wait for window to expire
      await new Promise(resolve => setTimeout(resolve, 1100));
      
      expect(rateLimiter.check('user1')).toBe(true);
    });

    it('should reset user attempts manually', () => {
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      rateLimiter.check('user1');
      
      expect(rateLimiter.check('user1')).toBe(false);
      
      rateLimiter.reset('user1');
      
      expect(rateLimiter.check('user1')).toBe(true);
    });
  });
});

{"$schema": "https://railway.app/railway.schema.json", "build": {"builder": "DOCKERFILE", "dockerfilePath": "Dockerfile", "buildCache": true}, "deploy": {"numReplicas": 1, "sleepApplication": false, "restartPolicyType": "ON_FAILURE", "restartPolicyMaxRetries": 10, "region": "us-west1", "memoryLimitMB": 1024, "strategy": "rollingUpdate", "healthcheckPath": "/", "healthcheckTimeout": 30, "healthcheckGracePeriod": 120, "tcpProxies": [{"port": 7880, "applicationPort": 7880}, {"port": 7881, "applicationPort": 7881}], "volumes": [{"name": "livekit-recordings", "mountPath": "/app/recordings"}]}}
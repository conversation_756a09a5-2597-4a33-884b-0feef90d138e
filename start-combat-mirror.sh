#!/bin/bash

# Combat Mirror System Full Stack Startup Script
# Based on LiveKit Vision Demo patterns with real-time pose analysis

echo "🥊 Combat Mirror System - Full Stack Startup"
echo "=============================================="

# Kill any existing processes
echo "Stopping existing processes..."
pkill -f "livekit-server" 2>/dev/null
pkill -f "enhanced_combat_agent" 2>/dev/null
pkill -f "vite" 2>/dev/null
docker-compose down 2>/dev/null

# Function to check if port is available
check_port() {
    local port=$1
    if lsof -Pi :$port -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ Port $port is already in use"
        return 1
    else
        echo "✅ Port $port is available"
        return 0
    fi
}

# Check required ports
echo ""
echo "Checking required ports..."
check_port 7880 || { echo "Please free port 7880 for LiveKit server"; exit 1; }
check_port 5173 || { echo "Please free port 5173 for web server (HTTPS)"; exit 1; }
check_port 6379 || { echo "Please free port 6379 for Redis"; exit 1; }

echo ""
echo "📦 Starting Docker services (LiveKit + Redis)..."
docker-compose up -d

# Wait for services to be ready
echo "⏳ Waiting for LiveKit server to be ready..."
sleep 5

# Check LiveKit server health
max_attempts=10
attempt=1
while [ $attempt -le $max_attempts ]; do
    if curl -f http://localhost:7880 >/dev/null 2>&1; then
        echo "✅ LiveKit server is ready"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts - waiting for LiveKit server..."
        sleep 2
        attempt=$((attempt + 1))
    fi
done

if [ $attempt -gt $max_attempts ]; then
    echo "❌ LiveKit server failed to start"
    exit 1
fi

echo ""
echo "🤖 Starting Combat Mirror Agent..."
cd agent

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment and install dependencies
source venv/bin/activate
pip install -r requirements.txt >/dev/null 2>&1

# Set environment variables
export LIVEKIT_API_KEY="APIfightmaster"
export LIVEKIT_API_SECRET="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ"
export LIVEKIT_WS_URL="ws://localhost:7880"

# Start the agent in background
echo "Starting Enhanced Combat Mirror Agent..."
python enhanced_combat_agent.py > agent.log 2>&1 &
AGENT_PID=$!

# Go back to main directory
cd ..

echo ""
echo "🌐 Starting Web Application..."
# Install web dependencies if needed
if [ ! -d "node_modules" ]; then
    echo "Installing web dependencies..."
    npm install
fi

# Start the web server with HTTPS for mobile camera access
npm run dev -- --host 0.0.0.0 --port 5173 > web.log 2>&1 &
WEB_PID=$!

# Wait for web server to be ready
echo "⏳ Waiting for web server to be ready..."
sleep 3

max_attempts=10
attempt=1
while [ $attempt -le $max_attempts ]; do
    if curl -k -f https://localhost:5173 >/dev/null 2>&1; then
        echo "✅ Web server is ready"
        break
    else
        echo "⏳ Attempt $attempt/$max_attempts - waiting for web server..."
        sleep 2
        attempt=$((attempt + 1))
    fi
done

# Get network IP for mobile access
NETWORK_IP=$(ifconfig | grep "inet " | grep -v 127.0.0.1 | awk '{print $2}' | head -1)

echo ""
echo "🎉 Combat Mirror System is running!"
echo "=================================="
echo ""
echo "🖥️  Desktop Interface: https://localhost:5173"
echo "📱 Mobile Access: https://$NETWORK_IP:5173"
echo "🔗 LiveKit Server: ws://localhost:7880"
echo ""
echo "📱 MOBILE SETUP:"
echo "   1. Open Combat Mirror app on iPhone"
echo "   2. Scan QR codes from the web interface"
echo "   3. Start training session"
echo ""
echo "📊 FEATURES:"
echo "   • Real-time pose analysis with MediaPipe"
echo "   • Multi-camera LiveKit streaming"
echo "   • Combat metrics (posture, balance, defense, punches)"
echo "   • Video upload and sync analysis"
echo ""
echo "🔧 LOGS:"
echo "   • Agent: tail -f agent/agent.log"
echo "   • Web: tail -f web.log"
echo "   • Docker: docker-compose logs -f"
echo ""
echo "🛑 TO STOP: Ctrl+C or run: pkill -f 'combat-mirror'"

# Create stop script
cat > stop-combat-mirror.sh << EOF
#!/bin/bash
echo "Stopping Combat Mirror System..."
kill $AGENT_PID $WEB_PID 2>/dev/null
docker-compose down
echo "Combat Mirror System stopped."
EOF
chmod +x stop-combat-mirror.sh

# Wait for interrupt
trap 'kill $AGENT_PID $WEB_PID 2>/dev/null; docker-compose down; exit 0' INT

# Keep script running
while true; do
    sleep 1
done
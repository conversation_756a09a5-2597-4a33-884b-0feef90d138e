#!/bin/bash

# Combat Mirror System - Railway Deployment Script (2025 Edition)
set -e

echo "🥊 Combat Mirror System - Railway Deployment (Optimized for 2025)"
echo "================================================================"

# Check if Railway CLI is installed
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it first:"
    echo "   npm install -g @railway/cli"
    exit 1
fi

# Check if user is logged in
if ! railway whoami &> /dev/null; then
    echo "❌ Please login to Railway first:"
    echo "   railway login"
    exit 1
fi

# Check for required environment variables
if [ -z "$GEMINI_API_KEY" ]; then
    echo "❌ GEMINI_API_KEY environment variable is required"
    echo "   export GEMINI_API_KEY='your_key_here'"
    exit 1
fi

if [ -z "$LIVEKIT_SECRET" ]; then
    echo "⚠️  LIVEKIT_SECRET not set, using default"
    LIVEKIT_SECRET="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ"
fi

# Deployment environment
ENVIRONMENT=${RAILWAY_ENVIRONMENT:-"production"}
echo "🚀 Deploying to environment: $ENVIRONMENT"

echo "✅ Prerequisites checked"
echo ""

# Deploy Backend API
echo "🔧 Deploying Backend API..."
cd server
railway project new combat-mirror-api --team ${RAILWAY_TEAM_ID:-""}
railway environment new $ENVIRONMENT
railway up --detach

echo "⏳ Setting backend environment variables..."
railway variables set GEMINI_API_KEY="$GEMINI_API_KEY"
railway variables set NODE_ENV="$ENVIRONMENT"
railway variables set PORT="3001"
railway variables set LOG_LEVEL="info"
railway variables set RAILWAY_ENABLE_OTEL="true"

echo "📡 Getting backend URL..."
sleep 10  # Wait for deployment
BACKEND_URL=$(railway domain)
echo "✅ Backend deployed: $BACKEND_URL"
cd ..

# Deploy LiveKit Server
echo ""
echo "🎥 Deploying LiveKit Server..."
cd livekit-server
railway project new combat-mirror-livekit --team ${RAILWAY_TEAM_ID:-""}
railway environment new $ENVIRONMENT

echo "⏳ Adding Redis addon..."
railway add redis

echo "⏳ Setting LiveKit environment variables..."
railway variables set LIVEKIT_API_KEY="APIfightmaster"
railway variables set LIVEKIT_API_SECRET="$LIVEKIT_SECRET"
railway variables set NODE_ENV="$ENVIRONMENT"

railway up --detach
sleep 15  # Wait for deployment

echo "📡 Getting LiveKit URL..."
LIVEKIT_DOMAIN=$(railway domain)
LIVEKIT_URL="wss://${LIVEKIT_DOMAIN/https:\/\//}"
echo "✅ LiveKit deployed: $LIVEKIT_URL"
cd ..

# Update frontend environment variables
echo ""
echo "🎨 Preparing Frontend..."
echo "⏳ Setting frontend environment variables..."

# Create production .env.local
cat > .env.local << EOF
VITE_API_URL=$BACKEND_URL
VITE_LIVEKIT_URL=$LIVEKIT_URL
VITE_LIVEKIT_API_KEY=APIfightmaster
VITE_LIVEKIT_API_SECRET=$LIVEKIT_SECRET
EOF

# Update mobile camera page with correct LiveKit URL
sed -i.bak "s|wss://your-livekit-server.railway.app|$LIVEKIT_URL|g" public/camera-stream.html
echo "✅ Updated mobile camera page with LiveKit URL"

# Update backend CORS settings
cd server
railway variables set FRONTEND_URL="https://$(railway domain --service combat-mirror-frontend 2>/dev/null || echo 'pending')"
cd ..

# Deploy Frontend
echo ""
echo "🚀 Deploying Frontend..."
railway project new combat-mirror-frontend --team ${RAILWAY_TEAM_ID:-""}
railway environment new $ENVIRONMENT

railway variables set VITE_API_URL="$BACKEND_URL"
railway variables set VITE_LIVEKIT_URL="$LIVEKIT_URL"
railway variables set VITE_LIVEKIT_API_KEY="APIfightmaster"
railway variables set VITE_LIVEKIT_API_SECRET="$LIVEKIT_SECRET"
railway variables set NODE_ENV="$ENVIRONMENT"

railway up --detach
sleep 15  # Wait for deployment

FRONTEND_URL=$(railway domain)
echo "✅ Frontend deployed: $FRONTEND_URL"

# Update backend CORS with actual frontend URL
echo ""
echo "🔧 Updating backend CORS settings..."
cd server
railway variables set FRONTEND_URL="$FRONTEND_URL"
cd ..

# Configure monitoring
echo ""
echo "📊 Configuring monitoring..."
railway run --service combat-mirror-api -- echo "RAILWAY_ENABLE_OTEL=true"
railway run --service combat-mirror-frontend -- echo "RAILWAY_ENABLE_OTEL=true"
railway run --service combat-mirror-livekit -- echo "RAILWAY_ENABLE_OTEL=true"

# Create staging environment if requested
if [ "$CREATE_STAGING" = "true" ]; then
    echo ""
    echo "🔧 Creating staging environment..."
    
    # Frontend staging
    railway environment new staging --project combat-mirror-frontend
    railway variables set VITE_API_URL="$BACKEND_URL" --environment staging
    railway variables set VITE_LIVEKIT_URL="$LIVEKIT_URL" --environment staging
    railway variables set NODE_ENV="staging" --environment staging
    
    # Backend staging  
    railway environment new staging --project combat-mirror-api
    railway variables set GEMINI_API_KEY="$GEMINI_API_KEY" --environment staging
    railway variables set NODE_ENV="staging" --environment staging
    
    # LiveKit staging
    railway environment new staging --project combat-mirror-livekit
    railway variables set LIVEKIT_API_KEY="APIfightmaster" --environment staging
    railway variables set LIVEKIT_API_SECRET="$LIVEKIT_SECRET" --environment staging
    railway variables set NODE_ENV="staging" --environment staging
    
    echo "✅ Staging environment created"
fi

# Run health checks
echo ""
echo "🏥 Running health checks..."

# Backend health check
if curl -f "$BACKEND_URL/api/health" > /dev/null 2>&1; then
    echo "✅ Backend API: Healthy"
else
    echo "❌ Backend API: Health check failed"
fi

# Frontend check
if curl -f "$FRONTEND_URL" > /dev/null 2>&1; then
    echo "✅ Frontend: Accessible"
else
    echo "❌ Frontend: Not accessible"
fi

# Final status
echo ""
echo "🎉 DEPLOYMENT COMPLETE!"
echo "======================="
echo "Frontend:  $FRONTEND_URL"
echo "Backend:   $BACKEND_URL"
echo "LiveKit:   $LIVEKIT_URL"
echo ""
echo "📱 Mobile Camera URL:"
echo "$FRONTEND_URL/camera-stream.html"
echo ""
echo "🧪 Test endpoints:"
echo "curl $BACKEND_URL/api/health"
echo ""
echo "📊 Monitoring:"
echo "- Railway Dashboard: https://railway.app/dashboard"
echo "- OpenTelemetry enabled for all services"
echo "- Structured logging with Winston"
echo ""
echo "🔒 Security Features:"
echo "- Railway Shield enabled"
echo "- Rate limiting configured"
echo "- WAF rules active"
echo "- Secret scanning enabled"
echo ""
echo "💾 Storage:"
echo "- Frontend videos: /app/uploads"
echo "- API uploads: /app/uploads"
echo "- LiveKit recordings: /app/recordings"
echo ""
echo "⚠️  Important Notes:"
echo "1. LiveKit may take 2-3 minutes to fully initialize"
echo "2. Test camera functionality from mobile devices"
echo "3. Monitor logs with: railway logs"
echo "4. Update DNS if using custom domains"
echo "5. Volume data persists across deployments"
echo ""
echo "✨ Your Combat Mirror System is now live with 2025 best practices!"
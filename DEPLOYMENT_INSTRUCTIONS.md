# 🚀 Combat Mirror System - Production Deployment Instructions

## Prerequisites Completed ✅
- [x] Secured API keys in environment variables
- [x] Fixed memory leaks in video storage service
- [x] Added input validation and XSS protection
- [x] Configured production build with optimizations
- [x] Set up backend server with security middleware
- [x] Tested production build locally

## Deployment Options

### Option 1: Railway Platform (Recommended)

1. **Login to Railway**
   ```bash
   railway login
   ```

2. **Create new project or link existing**
   ```bash
   railway link
   # Or create new:
   railway init
   ```

3. **Deploy to Production**
   ```bash
   ./deploy-production.sh
   ```

### Option 2: Docker Deployment

1. **Build Docker image**
   ```bash
   docker build -f Dockerfile.production -t combat-mirror:latest .
   ```

2. **Run locally**
   ```bash
   docker run -p 3001:3001 -p 4173:4173 \
     -e GEMINI_API_KEY=$GEMINI_API_KEY \
     combat-mirror:latest
   ```

### Option 3: Manual Deployment (Vercel/Netlify for Frontend + Cloud Run for Backend)

#### Frontend (Vercel)
1. Install Vercel CLI: `npm i -g vercel`
2. Deploy: `vercel --prod`
3. Set environment variable: `VITE_API_URL` to your backend URL

#### Backend (Google Cloud Run)
1. Build server image:
   ```bash
   cd server
   docker build -t gcr.io/YOUR_PROJECT/combat-mirror-api .
   docker push gcr.io/YOUR_PROJECT/combat-mirror-api
   ```

2. Deploy to Cloud Run:
   ```bash
   gcloud run deploy combat-mirror-api \
     --image gcr.io/YOUR_PROJECT/combat-mirror-api \
     --platform managed \
     --allow-unauthenticated \
     --set-env-vars GEMINI_API_KEY=$GEMINI_API_KEY
   ```

## Environment Variables Required

### Frontend (.env.production)
- `VITE_API_URL` - Backend API URL
- `VITE_LIVEKIT_URL` - LiveKit server URL (optional)
- `VITE_LIVEKIT_API_KEY` - LiveKit API key (optional)

### Backend (server/.env)
- `GEMINI_API_KEY` - Google Gemini API key (required)
- `PORT` - Server port (default: 3001)
- `CORS_ORIGIN` - Allowed origins for CORS
- `NODE_ENV` - Set to "production"
- `RATE_LIMIT_WINDOW_MS` - Rate limit window (default: 60000)
- `RATE_LIMIT_MAX_REQUESTS` - Max requests per window (default: 100)

## Post-Deployment Checklist

- [ ] Verify health endpoint: `https://YOUR_DOMAIN/api/health`
- [ ] Test video upload functionality
- [ ] Verify Gemini API integration
- [ ] Check browser console for errors
- [ ] Test on mobile devices
- [ ] Monitor server logs for errors
- [ ] Set up monitoring (Sentry/LogRocket)
- [ ] Configure CDN for static assets
- [ ] Set up SSL certificate
- [ ] Configure backup strategy

## Security Verification

- [ ] API keys not exposed in client code
- [ ] CORS properly configured
- [ ] Rate limiting active
- [ ] Input validation working
- [ ] XSS protection enabled
- [ ] HTTPS enforced
- [ ] CSP headers configured

## Performance Optimization

- [ ] Enable Gzip compression
- [ ] Configure caching headers
- [ ] Optimize images
- [ ] Enable HTTP/2
- [ ] Set up CDN
- [ ] Monitor Core Web Vitals

## Monitoring Setup

1. **Application Monitoring**
   - Set up Sentry for error tracking
   - Configure performance monitoring
   - Set up uptime monitoring

2. **Infrastructure Monitoring**
   - Monitor server resources
   - Set up log aggregation
   - Configure alerts

## Rollback Plan

If issues occur after deployment:

1. **Immediate Rollback**
   ```bash
   railway rollback
   # Or for Docker:
   docker stop [container_id]
   docker run [previous_image]
   ```

2. **Database Rollback** (if applicable)
   - Restore from backup
   - Run migration rollback scripts

## Support Contacts

- **Technical Issues**: Check server logs first
- **API Issues**: Verify Gemini API key and quotas
- **Performance Issues**: Check server resources and scaling

## Next Steps

1. Deploy to staging environment first
2. Run full test suite
3. Deploy to production
4. Monitor for 24 hours
5. Scale resources as needed

---

**Ready for Production! 🎉**

The application has been hardened with:
- ✅ Security fixes (XSS protection, input validation, rate limiting)
- ✅ Performance optimizations (memory leak fixes, code splitting)
- ✅ Production configuration (environment variables, build optimization)
- ✅ Error handling and logging
- ✅ Health checks and monitoring endpoints
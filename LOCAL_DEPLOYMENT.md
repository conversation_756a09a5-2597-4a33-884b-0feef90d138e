# Combat Mirror System - Local Deployment Guide

## 🚀 Quick Start

### Prerequisites
- Docker Desktop installed and running
- Gemini API key
- 8GB+ RAM available for Docker
- Ports 3000, 3001, 7880, 6379 available

### 1. Setup Environment
```bash
# Copy environment template
cp .env.local.example .env.local

# Edit .env.local and add your GEMINI_API_KEY
nano .env.local
```

### 2. Start Services
```bash
# Run the automated startup script
./start-local.sh
```

### 3. Verify Deployment
```bash
# Run the test suite
./test-local.sh
```

## 📍 Service URLs

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:3001
- **LiveKit WebSocket**: ws://localhost:7880
- **Redis**: localhost:6379
- **Mobile Camera**: http://[YOUR_LOCAL_IP]:3000/camera-stream.html

## 🏗️ Architecture

```
┌─────────────────┐     ┌─────────────────┐     ┌─────────────────┐
│    Frontend     │────▶│   Backend API   │────▶│     Redis       │
│   (Port 3000)   │     │   (Port 3001)   │     │   (Port 6379)   │
└─────────────────┘     └─────────────────┘     └─────────────────┘
         │                       │
         │                       │
         └───────────┬───────────┘
                     │
              ┌──────┴──────┐
              │   LiveKit    │
              │ (Port 7880)  │
              └─────────────┘
```

## 🛠️ Manual Commands

### Start Services
```bash
docker-compose up -d
```

### View Logs
```bash
# All services
docker-compose logs -f

# Specific service
docker-compose logs -f backend
docker-compose logs -f frontend
docker-compose logs -f livekit
docker-compose logs -f redis
```

### Stop Services
```bash
docker-compose down
```

### Restart Service
```bash
docker-compose restart backend
```

### Rebuild After Changes
```bash
docker-compose build
docker-compose up -d
```

## 🧪 Testing

### Health Checks
```bash
# Backend health
curl http://localhost:3001/api/health

# Frontend status
curl http://localhost:3000

# LiveKit status
curl http://localhost:7880/

# Redis ping
docker-compose exec redis redis-cli ping
```

### Test Analysis Endpoint
```bash
curl -X POST http://localhost:3001/api/analyze \
  -H "Content-Type: application/json" \
  -d '{"metrics": {"punchCount": 50, "accuracy": 0.85}}'
```

## 📱 Mobile Testing

1. Find your local IP:
   ```bash
   # macOS
   ipconfig getifaddr en0
   
   # Linux
   ip addr show | grep inet
   ```

2. Access from mobile device:
   ```
   http://[YOUR_IP]:3000/camera-stream.html
   ```

3. Ensure mobile device is on same network

## 🐛 Troubleshooting

### Port Already in Use
```bash
# Find process using port
lsof -i :3000
lsof -i :3001
lsof -i :7880
lsof -i :6379

# Kill process
kill -9 [PID]
```

### Container Won't Start
```bash
# Check logs
docker-compose logs [service_name]

# Remove and recreate
docker-compose rm -f [service_name]
docker-compose up -d [service_name]
```

### CORS Issues
- Ensure FRONTEND_URL is set correctly in backend environment
- Check browser console for specific CORS errors
- Verify API calls use correct URLs

### LiveKit Connection Failed
- Wait 30-60 seconds after starting for full initialization
- Check WebSocket URL format (ws:// not wss:// for local)
- Verify Redis is running: `docker-compose ps redis`

### Performance Issues
```bash
# Check Docker resources
docker system df
docker stats

# Increase Docker memory in Docker Desktop settings
# Recommended: 4GB+ for smooth operation
```

## 🔧 Development Workflow

### Frontend Changes
```bash
# Frontend auto-reloads in development
# Changes in ./src are reflected immediately
```

### Backend Changes
```bash
# Restart backend after changes
docker-compose restart backend
```

### Environment Variable Changes
```bash
# Recreate containers after .env changes
docker-compose down
docker-compose up -d
```

## 📊 Monitoring

### Container Status
```bash
docker-compose ps
```

### Resource Usage
```bash
docker stats
```

### Network Inspection
```bash
docker network inspect combat-mirror-system_combat-mirror-network
```

### Volume Inspection
```bash
docker volume ls
docker volume inspect combat-mirror-system_redis-data
```

## 🚢 Production-like Testing

To test with Caddy reverse proxy:
```bash
docker-compose --profile production up -d
```

This adds:
- HTTPS support (self-signed cert)
- Reverse proxy configuration
- Production-like routing

## 🛑 Cleanup

### Stop and Remove Everything
```bash
# Stop containers
docker-compose down

# Remove volumes (WARNING: deletes data)
docker-compose down -v

# Remove images
docker-compose down --rmi all
```

### Reset to Clean State
```bash
./cleanup-local.sh  # If you create this script
```

## 📝 Notes

- Services use network IP (192.168.x.x) instead of localhost for cross-container communication
- Frontend build may take 2-3 minutes on first run
- LiveKit requires 30-60 seconds to fully initialize
- Redis data persists in Docker volume between restarts
- Logs are stored in ./logs directory (gitignored)
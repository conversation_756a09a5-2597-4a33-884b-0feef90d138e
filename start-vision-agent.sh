#!/bin/bash

# Combat Mirror Vision Agent Startup Script
# Based on LiveKit Vision Demo architecture with Gemini Live API integration

echo "🥊 Combat Mirror Vision Agent - AI Coach Startup"
echo "================================================="

# Check if we're in the correct directory
if [ ! -f "agent/combat_vision_agent.py" ]; then
    echo "❌ Error: Must run from Combat Mirror System root directory"
    echo "Expected to find: agent/combat_vision_agent.py"
    exit 1
fi

# Check environment setup
echo ""
echo "🔍 Checking environment setup..."

# Check for required API keys
if [ ! -f "agent/.env" ]; then
    echo "⚠️  No .env file found. Creating from template..."
    cp agent/.env.example agent/.env
    echo "📝 Please edit agent/.env with your API keys:"
    echo "   - GOOGLE_API_KEY (required for Gemini Live API)"
    echo "   - LIVEKIT_* settings (should work with defaults)"
    echo ""
    echo "Get your Gemini API key from: https://console.cloud.google.com/apis/library/generativelanguage.googleapis.com"
    exit 1
fi

# Source environment variables
source agent/.env

# Check for Google API key
if [ -z "$GOOGLE_API_KEY" ] || [ "$GOOGLE_API_KEY" = "your_google_gemini_api_key_here" ]; then
    echo "❌ Error: GOOGLE_API_KEY not set in agent/.env"
    echo "📝 Please add your Gemini API key to agent/.env"
    echo "Get your API key from: https://ai.google.dev/gemini-api/docs"
    exit 1
fi

echo "✅ Environment configuration found"

# Check if LiveKit server is running
echo ""
echo "🔍 Checking LiveKit server..."
if curl -f $LIVEKIT_URL >/dev/null 2>&1; then
    echo "✅ LiveKit server is running at $LIVEKIT_URL"
else
    echo "❌ LiveKit server not accessible at $LIVEKIT_URL"
    echo "💡 Start the LiveKit server first:"
    echo "   ./start-combat-mirror.sh"
    exit 1
fi

# Setup Python environment
echo ""
echo "🐍 Setting up Python environment..."

cd agent

# Create virtual environment if it doesn't exist
if [ ! -d ".venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv .venv
fi

# Activate virtual environment
source .venv/bin/activate

# Check Python version
PYTHON_VERSION=$(python --version 2>&1 | awk '{print $2}')
echo "Python version: $PYTHON_VERSION"

# Install/upgrade dependencies
echo "Installing dependencies..."
pip install --upgrade pip >/dev/null 2>&1
pip install -r vision_requirements.txt

# Check if installation was successful
if [ $? -ne 0 ]; then
    echo "❌ Error installing dependencies"
    echo "💡 Try installing with different Python version or check requirements"
    exit 1
fi

echo "✅ Dependencies installed successfully"

# Start the vision agent
echo ""
echo "🤖 Starting Combat Mirror Vision Agent..."
echo "========================================"
echo ""
echo "🎯 Agent Features:"
echo "   • Real-time video analysis with Gemini Live API"
echo "   • Professional combat sports coaching"
echo "   • Technique feedback and improvement suggestions"
echo "   • Safety-focused training guidance"
echo ""
echo "🔗 System URLs:"
echo "   • LiveKit Server: $LIVEKIT_URL"
echo "   • Web Interface: https://localhost:5173"
echo "   • Mobile Access: https://************:5173"
echo ""
echo "📱 To connect your device:"
echo "   1. Open the web interface"
echo "   2. Toggle 'Use Live Camera' mode"
echo "   3. Scan QR codes with your mobile device"
echo "   4. Start your training session!"
echo ""
echo "🛑 To stop: Ctrl+C"
echo ""

# Run the vision agent
python combat_vision_agent.py
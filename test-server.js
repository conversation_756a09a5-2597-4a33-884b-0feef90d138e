import express from 'express';
import cors from 'cors';

const app = express();
const PORT = 3001;

app.use(cors());
app.use(express.json());

app.get('/api/health', (req, res) => {
  res.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    message: 'Test server running'
  });
});

app.post('/api/analyze', (req, res) => {
  const { metrics, participantId, sessionType } = req.body;
  
  if (!metrics || !participantId || !sessionType) {
    return res.status(400).json({ error: 'Missing required fields' });
  }
  
  res.json({
    report: `# Analysis Report\n\n## Participant: ${participantId}\n\nThis is a test analysis report for session type: ${sessionType}\n\nMetrics received successfully.`,
    status: 'success'
  });
});

app.listen(PORT, () => {
  console.log(`Test server running on http://localhost:${PORT}`);
});
version: '3.8'

services:
  # Frontend - React/Vite Application
  frontend:
    build:
      context: .
      dockerfile: Dockerfile.frontend
    container_name: combat-mirror-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=development
      - VITE_API_URL=http://************:3001
      - VITE_LIVEKIT_URL=ws://************:7880
      - VITE_LIVEKIT_API_KEY=APIfightmaster
      - VITE_LIVEKIT_API_SECRET=xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ
    volumes:
      - ./dist:/app/dist
      - ./public:/app/public
      - ./src:/app/src
      - ./uploads:/app/uploads
    depends_on:
      - backend
      - livekit
    networks:
      - combat-mirror-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Backend API - Express Server
  backend:
    build:
      context: ./server
      dockerfile: Dockerfile
    container_name: combat-mirror-api
    ports:
      - "3001:3001"
    environment:
      - NODE_ENV=development
      - PORT=3001
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - FRONTEND_URL=http://************:3000
      - LOG_LEVEL=debug
      - CORS_DEBUG=true
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./server:/app
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    depends_on:
      - redis
    networks:
      - combat-mirror-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3001/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # LiveKit Server - WebRTC
  livekit:
    image: livekit/livekit-server:latest
    container_name: combat-mirror-livekit
    ports:
      - "7880:7880"  # WebSocket
      - "7881:7881"  # HTTP API
      - "50000-60000:50000-60000/tcp"  # RTC
      - "50000-60000:50000-60000/udp"  # RTC
    environment:
      - LIVEKIT_KEYS=APIfightmaster:xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ
      - LIVEKIT_LOG_LEVEL=info
      - LIVEKIT_REDIS_HOST=redis
      - LIVEKIT_REDIS_PORT=6379
      - LIVEKIT_WEBHOOK_URLS=http://backend:3001/api/webhooks/livekit
      - LIVEKIT_TURN_ENABLED=true
      - LIVEKIT_RTC_PORT_RANGE_START=50000
      - LIVEKIT_RTC_PORT_RANGE_END=60000
      - NODE_ENV=development
    volumes:
      - ./livekit-data:/livekit-data
      - ./recordings:/app/recordings
    depends_on:
      - redis
    networks:
      - combat-mirror-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:7880/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  # Redis - Caching and LiveKit State
  redis:
    image: redis:7-alpine
    container_name: combat-mirror-redis
    ports:
      - "6379:6379"
    volumes:
      - redis-data:/data
    networks:
      - combat-mirror-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # CopyParty - File Server for Video Storage and Management
  copyparty:
    image: ghcr.io/9001/copyparty:latest
    container_name: combat-mirror-fileserver
    ports:
      - "3923:3923"  # HTTP/WebDAV
      - "3990:3990"  # FTP
      - "1033:1033"  # TFTP
    environment:
      ARGS: "-i :: -p 3923 --rw"
    volumes:
      - ./uploads:/srv/uploads
      - ./recordings:/srv/recordings
      - ./exports:/srv/exports
      - ./copyparty-db:/srv/.hist
      - ./copyparty-config:/srv/config
    networks:
      - combat-mirror-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3923/"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 20s
    restart: unless-stopped

  # Caddy - Reverse Proxy (Optional - for production-like setup)
  caddy:
    image: caddy:2-alpine
    container_name: combat-mirror-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./Caddyfile.local:/etc/caddy/Caddyfile
      - caddy-data:/data
      - caddy-config:/config
    networks:
      - combat-mirror-network
    depends_on:
      - frontend
      - backend
      - livekit
    profiles:
      - production

volumes:
  redis-data:
  caddy-data:
  caddy-config:
  copyparty-db:
  copyparty-config:

networks:
  combat-mirror-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
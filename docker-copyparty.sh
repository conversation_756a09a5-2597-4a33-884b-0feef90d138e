#!/bin/bash

# Docker management script for Copyparty
set -e

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
CONTAINER_NAME="combat-mirror-copyparty"
IMAGE_NAME="ghcr.io/9001/copyparty:latest"
PORT="3923"

# Function to show usage
show_usage() {
    echo "Combat Mirror - Copyparty Docker Manager"
    echo "========================================"
    echo ""
    echo "Usage: $0 [command]"
    echo ""
    echo "Commands:"
    echo "  start    - Start Copyparty container"
    echo "  stop     - Stop Copyparty container"
    echo "  restart  - Restart Copyparty container"
    echo "  status   - Show container status"
    echo "  logs     - Show container logs"
    echo "  shell    - Open shell in container"
    echo "  clean    - Remove container and volumes"
    echo "  test     - Test Copyparty endpoints"
    echo ""
}

# Create required directories
create_directories() {
    echo -e "${YELLOW}📁 Creating storage directories...${NC}"
    mkdir -p uploads recordings exports copyparty-db copyparty-config copyparty-thumbs
    echo -e "${GREEN}✅ Directories created${NC}"
}

# Start Copyparty
start_copyparty() {
    echo -e "${BLUE}🚀 Starting Copyparty container...${NC}"
    
    # Create directories
    create_directories
    
    # Check if container exists
    if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${YELLOW}Container exists, starting...${NC}"
        docker start ${CONTAINER_NAME}
    else
        echo -e "${YELLOW}Creating new container...${NC}"
        docker run -d \
            --name ${CONTAINER_NAME} \
            --restart unless-stopped \
            -p ${PORT}:3923 \
            -p 3990:3990 \
            -p 1033:1033 \
            -v "$(pwd)/uploads:/srv/uploads" \
            -v "$(pwd)/recordings:/srv/recordings" \
            -v "$(pwd)/exports:/srv/exports" \
            -v "$(pwd)/copyparty-db:/srv/.hist" \
            -v "$(pwd)/copyparty-config:/srv/config" \
            -v "$(pwd)/copyparty-thumbs:/srv/.th" \
            ${IMAGE_NAME} \
            -i :: -p 3923 \
            --rw /srv/uploads,/srv/recordings,/srv/exports \
            --cors \
            --theme 2 \
            --name "Combat Mirror File Server" \
            --no-robots \
            --ed --em --et \
            --e2dsa --e2ts \
            --s \
            --th-poke \
            --no-thumb-svg \
            --j-part-sz 32 \
            --j-threads 4
    fi
    
    # Wait for container to be ready
    echo -e "${YELLOW}⏳ Waiting for Copyparty to start...${NC}"
    sleep 3
    
    # Check if running
    if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${GREEN}✅ Copyparty is running!${NC}"
        echo ""
        echo "📍 Access Points:"
        echo "   Web Interface: http://localhost:${PORT}"
        echo "   WebDAV:        http://localhost:${PORT}"
        echo "   FTP:           ftp://localhost:3990"
        echo "   TFTP:          tftp://localhost:1033"
        echo ""
        echo "📂 Storage Paths:"
        echo "   Uploads:       http://localhost:${PORT}/uploads/"
        echo "   Recordings:    http://localhost:${PORT}/recordings/"
        echo "   Exports:       http://localhost:${PORT}/exports/"
    else
        echo -e "${RED}❌ Failed to start Copyparty${NC}"
        docker logs ${CONTAINER_NAME}
        exit 1
    fi
}

# Stop Copyparty
stop_copyparty() {
    echo -e "${YELLOW}⏹️  Stopping Copyparty container...${NC}"
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    echo -e "${GREEN}✅ Copyparty stopped${NC}"
}

# Restart Copyparty
restart_copyparty() {
    echo -e "${YELLOW}🔄 Restarting Copyparty...${NC}"
    stop_copyparty
    sleep 2
    start_copyparty
}

# Show status
show_status() {
    echo -e "${BLUE}📊 Copyparty Container Status${NC}"
    echo "=============================="
    
    if docker ps --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
        echo -e "${GREEN}✅ Container is running${NC}"
        echo ""
        docker ps --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        echo ""
        echo "Container Statistics:"
        docker stats --no-stream ${CONTAINER_NAME}
    else
        echo -e "${RED}❌ Container is not running${NC}"
        
        if docker ps -a --format '{{.Names}}' | grep -q "^${CONTAINER_NAME}$"; then
            echo "Container exists but is stopped."
            echo ""
            docker ps -a --filter "name=${CONTAINER_NAME}" --format "table {{.Names}}\t{{.Status}}"
        else
            echo "No container found."
        fi
    fi
}

# Show logs
show_logs() {
    echo -e "${BLUE}📜 Copyparty Logs${NC}"
    echo "================="
    docker logs -f ${CONTAINER_NAME}
}

# Open shell
open_shell() {
    echo -e "${BLUE}🐚 Opening shell in Copyparty container...${NC}"
    docker exec -it ${CONTAINER_NAME} /bin/sh
}

# Clean up
clean_up() {
    echo -e "${RED}🗑️  Cleaning up Copyparty...${NC}"
    echo "This will remove the container and optionally the data."
    read -p "Remove data volumes too? (y/N): " -n 1 -r
    echo
    
    # Stop and remove container
    docker stop ${CONTAINER_NAME} 2>/dev/null || true
    docker rm ${CONTAINER_NAME} 2>/dev/null || true
    echo -e "${GREEN}✅ Container removed${NC}"
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}Removing data directories...${NC}"
        rm -rf uploads recordings exports copyparty-db copyparty-thumbs
        echo -e "${GREEN}✅ Data removed${NC}"
    fi
}

# Test endpoints
test_endpoints() {
    echo -e "${BLUE}🧪 Testing Copyparty Endpoints${NC}"
    echo "=============================="
    
    # Test main page
    echo -n "Testing main page... "
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:${PORT}/ | grep -q "200"; then
        echo -e "${GREEN}✅ OK${NC}"
    else
        echo -e "${RED}❌ Failed${NC}"
    fi
    
    # Test uploads directory
    echo -n "Testing /uploads... "
    if curl -s -o /dev/null -w "%{http_code}" http://localhost:${PORT}/uploads/ | grep -q "200\|201"; then
        echo -e "${GREEN}✅ OK${NC}"
    else
        echo -e "${RED}❌ Failed${NC}"
    fi
    
    # Test JSON API
    echo -n "Testing JSON API... "
    if curl -s http://localhost:${PORT}/uploads/?j | grep -q "files\|dirs"; then
        echo -e "${GREEN}✅ OK${NC}"
    else
        echo -e "${RED}❌ Failed${NC}"
    fi
    
    # Test CORS headers
    echo -n "Testing CORS... "
    if curl -s -I http://localhost:${PORT}/ | grep -qi "access-control-allow-origin"; then
        echo -e "${GREEN}✅ Enabled${NC}"
    else
        echo -e "${YELLOW}⚠️  Not detected${NC}"
    fi
    
    echo ""
    echo -e "${GREEN}Test complete!${NC}"
}

# Main script logic
case "$1" in
    start)
        start_copyparty
        ;;
    stop)
        stop_copyparty
        ;;
    restart)
        restart_copyparty
        ;;
    status)
        show_status
        ;;
    logs)
        show_logs
        ;;
    shell)
        open_shell
        ;;
    clean)
        clean_up
        ;;
    test)
        test_endpoints
        ;;
    *)
        show_usage
        ;;
esac
# Frontend Dockerfile for local development and testing
FROM node:20-alpine

WORKDIR /app

# Install dependencies
COPY package*.json ./
RUN npm ci

# Copy application files
COPY . .

# Build the application
RUN npm run build

# Use Caddy for serving static files
FROM caddy:2-alpine

# Copy built application
COPY --from=0 /app/dist /srv

# Caddy configuration for SPA
RUN echo -e ":3000 {\n\
    root * /srv\n\
    encode gzip\n\
    try_files {path} /index.html\n\
    file_server\n\
    header {\n\
        Access-Control-Allow-Origin *\n\
        Access-Control-Allow-Methods *\n\
        Access-Control-Allow-Headers *\n\
    }\n\
}" > /etc/caddy/Caddyfile

EXPOSE 3000

CMD ["caddy", "run", "--config", "/etc/caddy/Caddyfile", "--adapter", "caddyfile"]
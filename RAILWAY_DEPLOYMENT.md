# Combat Mirror System - Railway Deployment Guide

## Overview

This guide covers deploying the complete Combat Mirror System to Railway, including:
- Frontend (React + Vite)
- Backend API (Express + Gemini)
- LiveKit Server (WebRTC streaming)

## Prerequisites

1. Railway account (free tier available)
2. GitHub repository with the code
3. Gemini API key
4. Domain name (optional, Railway provides subdomains)

## Deployment Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │  LiveKit Server │
│   (Vite/React)  │────│   (Express)     │    │   (WebRTC)      │
│   Port: 80/443  │    │   Port: 3001    │    │   Port: 7880    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                      ┌─────────────────┐
                      │     Redis       │
                      │   (Railway)     │
                      └─────────────────┘
```

## Step 1: Deploy Backend API

### 1.1 Create Railway Project
```bash
# Install Railway CLI
npm install -g @railway/cli

# Login to Railway
railway login

# Create new project
railway init
```

### 1.2 Deploy Backend Service
```bash
# Navigate to server directory
cd server

# Create Railway service
railway create combat-mirror-api

# Set environment variables
railway env set GEMINI_API_KEY="your_gemini_api_key_here"
railway env set NODE_ENV="production"
railway env set FRONTEND_URL="https://your-frontend-domain.railway.app"

# Deploy
railway deploy
```

### 1.3 Get Backend URL
```bash
# Get the deployed URL
railway domain
```
Copy the URL (e.g., `https://combat-mirror-api-production.up.railway.app`)

## Step 2: Deploy LiveKit Server

### 2.1 Create LiveKit Service
```bash
# Navigate to livekit-server directory
cd ../livekit-server

# Create Railway service
railway create combat-mirror-livekit

# Set environment variables
railway env set LIVEKIT_API_KEY="APIfightmaster"
railway env set LIVEKIT_API_SECRET="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ"

# Deploy
railway deploy
```

### 2.2 Configure LiveKit Ports
In Railway dashboard:
1. Go to your LiveKit service
2. Settings → Networking
3. Add these ports:
   - `7880` (WebSocket)
   - `7881` (RTC TCP)
   - `50000-60000` (RTC UDP range)

### 2.3 Get LiveKit URL
```bash
railway domain
```
Copy the URL (e.g., `wss://combat-mirror-livekit-production.up.railway.app`)

## Step 3: Deploy Redis (for LiveKit)

```bash
# Add Redis addon to LiveKit service
railway add redis

# Redis will be automatically configured
```

## Step 4: Deploy Frontend

### 4.1 Create Frontend Service
```bash
# Navigate to root directory
cd ..

# Create Railway service
railway create combat-mirror-frontend

# Set environment variables
railway env set VITE_API_URL="https://your-backend-url.railway.app"
railway env set VITE_LIVEKIT_URL="wss://your-livekit-url.railway.app"
railway env set VITE_LIVEKIT_API_KEY="APIfightmaster"
railway env set VITE_LIVEKIT_API_SECRET="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ"

# Deploy
railway deploy
```

### 4.2 Configure Build Settings
In Railway dashboard:
1. Go to frontend service
2. Settings → Deploy
3. Build Command: `npm run build`
4. Start Command: `npm run preview`

## Step 5: Configure Custom Domains (Optional)

### 5.1 Add Domains in Railway Dashboard
1. Frontend: `app.combatmirror.com`
2. API: `api.combatmirror.com`  
3. LiveKit: `livekit.combatmirror.com`

### 5.2 Update Environment Variables
```bash
# Update frontend env
railway env set VITE_API_URL="https://api.combatmirror.com"
railway env set VITE_LIVEKIT_URL="wss://livekit.combatmirror.com"

# Update backend env
railway env set FRONTEND_URL="https://app.combatmirror.com"
```

## Step 6: Update Mobile Camera Page

Edit `public/camera-stream.html` line 115:
```javascript
const livekitUrl = roomName.includes('localhost') ? 
    'ws://localhost:7880' : 
    'wss://your-actual-livekit-url.railway.app'; // Update this!
```

## Step 7: Environment Variables Summary

### Backend API Service
```env
GEMINI_API_KEY=your_gemini_key
NODE_ENV=production
FRONTEND_URL=https://your-frontend-domain.railway.app
PORT=3001
```

### Frontend Service
```env
VITE_API_URL=https://your-backend-domain.railway.app
VITE_LIVEKIT_URL=wss://your-livekit-domain.railway.app
VITE_LIVEKIT_API_KEY=APIfightmaster
VITE_LIVEKIT_API_SECRET=your_secret_here
```

### LiveKit Service
```env
LIVEKIT_API_KEY=APIfightmaster
LIVEKIT_API_SECRET=your_secret_here
REDIS_URL=redis://default:password@host:port (auto-configured)
```

## Step 8: Testing Deployment

### 8.1 Health Checks
```bash
# Test API health
curl https://your-api-domain.railway.app/api/health

# Test frontend
curl https://your-frontend-domain.railway.app

# Test LiveKit (should return connection info)
curl https://your-livekit-domain.railway.app
```

### 8.2 Functional Testing
1. Load the frontend application
2. Start a session
3. Test video upload
4. Test analysis report generation
5. Test LiveKit camera connection (use QR code)

## Step 9: Monitoring & Logs

### View Logs
```bash
# API logs
railway logs --service combat-mirror-api

# Frontend logs  
railway logs --service combat-mirror-frontend

# LiveKit logs
railway logs --service combat-mirror-livekit
```

### Monitoring Dashboard
Railway provides built-in monitoring for:
- CPU usage
- Memory usage
- Request counts
- Error rates

## Troubleshooting

### Common Issues

1. **CORS Errors**
   - Check `FRONTEND_URL` in backend environment
   - Ensure domains match exactly

2. **LiveKit Connection Failed**
   - Verify WebSocket URL uses `wss://` not `ws://`
   - Check port configuration (7880)
   - Ensure Redis is connected

3. **API Key Errors**
   - Verify `GEMINI_API_KEY` is set correctly
   - Check API key permissions

4. **Build Failures**
   - Check Node.js version compatibility
   - Verify all dependencies are listed in package.json
   - Check build logs for specific errors

### Debug Commands
```bash
# Check environment variables
railway env

# View service status
railway status

# Restart service
railway restart

# View build logs
railway logs --build
```

## Cost Estimation

Railway pricing (approximate):
- **Hobby Plan**: $5/month per service
- **Frontend**: ~$5/month
- **Backend**: ~$5/month  
- **LiveKit**: ~$10/month (higher resource usage)
- **Redis**: ~$3/month
- **Total**: ~$23/month

## Security Considerations

1. Use strong secrets for LiveKit
2. Enable HTTPS for all services
3. Configure proper CORS origins
4. Monitor error logs for security issues
5. Keep dependencies updated

## Performance Optimization

1. Enable Railway's CDN for frontend
2. Use Redis caching for API responses
3. Configure proper connection pooling
4. Monitor resource usage and scale as needed

---

## Quick Deploy Script

```bash
#!/bin/bash
# quick-deploy.sh

echo "🚀 Deploying Combat Mirror System to Railway..."

# Deploy backend
cd server
railway create combat-mirror-api
railway env set GEMINI_API_KEY="$GEMINI_API_KEY"
railway env set NODE_ENV="production"
railway deploy
BACKEND_URL=$(railway domain)

# Deploy LiveKit
cd ../livekit-server
railway create combat-mirror-livekit
railway env set LIVEKIT_API_KEY="APIfightmaster"
railway env set LIVEKIT_API_SECRET="$LIVEKIT_SECRET"
railway add redis
railway deploy
LIVEKIT_URL=$(railway domain)

# Deploy frontend
cd ..
railway create combat-mirror-frontend
railway env set VITE_API_URL="$BACKEND_URL"
railway env set VITE_LIVEKIT_URL="$LIVEKIT_URL"
railway env set VITE_LIVEKIT_API_KEY="APIfightmaster"
railway env set VITE_LIVEKIT_API_SECRET="$LIVEKIT_SECRET"
railway deploy

echo "✅ Deployment complete!"
echo "Frontend: $(railway domain)"
echo "Backend: $BACKEND_URL"
echo "LiveKit: $LIVEKIT_URL"
```

Run with:
```bash
chmod +x quick-deploy.sh
GEMINI_API_KEY="your_key" LIVEKIT_SECRET="your_secret" ./quick-deploy.sh
```
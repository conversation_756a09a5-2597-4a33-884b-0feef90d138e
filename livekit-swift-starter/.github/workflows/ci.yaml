name: CI

on:
  workflow_dispatch:
  push:
    branches: [main]
  pull_request:
    branches: [main]

concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number || github.ref }}
  cancel-in-progress: true

jobs:
  build-and-test:
    name: Build & Test
    strategy:
      fail-fast: false
      matrix:
        include:
          # https://github.com/actions/runner-images/blob/main/images/macos/macos-15-Readme.md
          - os: macos-15
            xcode: 16.4
            platform: "iOS Simulator,OS=18.5,name=iPhone 16 Pro"
          # - os: macos-15
          #   xcode: 16.4
          #   platform: "macOS"

    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    defaults:
      run:
        shell: bash
    steps:
      - uses: actions/checkout@v4

      - uses: maxim-lobanov/setup-xcode@v1
        with:
          xcode-version: ${{ matrix.xcode }}

      - name: Xcode Version
        run: xcodebuild -version

      - name: Swift Version
        run: xcrun swift --version

      - name: Setup .env
        run: cat VoiceAgent/.env.example.xcconfig > VoiceAgent/.env.xcconfig

      - name: Run Tests
        run: |
          set -o pipefail && xcodebuild test \
            -scheme VoiceAgent \
            -destination 'platform=${{ matrix.platform }}' | xcbeautify --renderer github-actions

  lint:
    name: Lint
    runs-on: macos-latest
    steps:
      - uses: actions/checkout@v4
      - name: SwiftFormat Lint
        run: swiftformat --lint . --reporter github-actions-log
        # Comes pre-installed on macOS runners

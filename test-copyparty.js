#!/usr/bin/env node

// Test script for CopyParty integration
import axios from 'axios';

const COPYPARTY_URL = 'http://localhost:3923';

async function testCopyPartyHealth() {
  console.log('\n🧪 Testing CopyParty File Server Integration\n');
  
  try {
    console.log('1. Testing health check...');
    const response = await axios.get(`${COPYPARTY_URL}/`, { timeout: 5000 });
    console.log(`✅ CopyParty is running! Status: ${response.status}`);
    
    console.log('\n2. Testing upload directory...');
    const uploadResponse = await axios.get(`${COPYPARTY_URL}/uploads/`, { timeout: 5000 });
    console.log(`✅ Upload directory accessible! Status: ${uploadResponse.status}`);
    
    return true;
  } catch (error) {
    console.log(`❌ CopyParty test failed: ${error.message}`);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n💡 Make sure to start CopyParty first:');
      console.log('   docker-compose up copyparty');
    }
    
    return false;
  }
}

testCopyPartyHealth();
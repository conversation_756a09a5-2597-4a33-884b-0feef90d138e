# 🎯 Combat Mirror System - Quick Calibration Guide

## Overview
This guide provides step-by-step instructions for calibrating your iPad Pro, iPhone 15 Pro Max, and MacBook Pro devices for the Combat Mirror System.

## 🚀 Quick Start (5 minutes)

### 1. Install Dependencies
```bash
npm install
```

### 2. Run Complete Calibration Setup
```bash
npm run calibration:full
```

### 3. Open Web Interface
```bash
open output/qr-codes/index.html
```

## 📋 Manual Steps

### Step 1: Device Detection
```bash
npm run calibration:detect
```

**Expected Output:**
```
✅ Found 3 devices:
  1. iPad Pro (ipad-pro-001)
  2. iPhone 15 Pro Max (iphone-15-pro-max-001)
  3. MacBook Pro (macbook-pro-001)
```

### Step 2: Generate QR Codes
```bash
npm run calibration:qr
```

**Generated Files:**
- `output/qr-codes/` - All QR codes for device positioning
- `output/qr-codes/index.html` - Web interface for easy access
- `output/qr-codes/summary.json` - Device mapping summary

### Step 3: Position Devices
Use the following positions for optimal calibration:

| Position | Device | Distance | Height | Angle |
|----------|--------|----------|---------|--------|
| FRONT | iPad Pro | 2m | Chest level | 0° |
| LEFT | iPhone 15 Pro Max | 1.5m left, 1m front | Shoulder level | 45° |
| RIGHT | MacBook Pro | 1.5m right, 1m front | Shoulder level | 45° |

### Step 4: USB Connection Setup
Connect all devices via USB for initial calibration:

```bash
# Check device recognition
system_profiler SPUSBDataType | grep -A 5 -B 5 "iPad\|iPhone\|MacBook"

# For iOS devices, ensure trust is established
# Look for "Trust This Computer" prompt on device
```

### Step 5: QR Code Scanning
1. Open `output/qr-codes/index.html` in your browser
2. Print or display QR codes at designated positions
3. Scan QR codes with mobile devices for wireless setup

## 🔧 Calibration Commands

| Command | Description |
|---------|-------------|
| `npm run calibration:detect` | Detect connected devices |
| `npm run calibration:qr` | Generate QR codes |
| `npm run calibration:web` | Generate web interface |
| `npm run calibration:full` | Complete setup (recommended) |

## 📱 Device URLs

Access each device directly:
- **iPad Pro**: `https://localhost:5173/camera-stream.html?device=ipad-pro-001&position=FRONT`
- **iPhone 15 Pro Max**: `https://localhost:5173/camera-stream.html?device=iphone-15-pro-max-001&position=LEFT`
- **MacBook Pro**: `https://localhost:5173/camera-stream.html?device=macbook-pro-001&position=RIGHT`

## 🎯 Calibration Protocol Summary

### Phase 1: Device Detection
- Detects iPad Pro, iPhone 15 Pro Max, MacBook Pro
- Validates USB connections
- Creates device registry

### Phase 2: Intrinsic Calibration
- Camera matrix calculation
- Distortion correction
- Focal length optimization

### Phase 3: Spatial Calibration
- Multi-device coordinate system
- Position mapping
- Spatial relationship calculation

### Phase 4: QR Code Matching
- Generate device-specific QR codes
- Position-based QR codes
- Wireless device pairing

### Phase 5: Motion Tracking
- Gyroscope calibration
- Accelerometer bias correction
- Tracking parameter optimization

## 🚨 Troubleshooting

### Device Not Detected
```bash
# Check USB connection
lsusb | grep -i "apple\|ipad\|iphone"

# Install ios-deploy
npm install -g ios-deploy

# Reset device trust settings
# Settings > General > Reset > Reset Location & Privacy
```

### QR Code Not Scanning
- Ensure adequate lighting
- Check camera focus
- Verify QR code size (minimum 2x2 inches)
- Try different scanning apps

### Calibration Accuracy Issues
- Ensure consistent lighting
- Check device positioning
- Verify distance measurements
- Recalibrate if needed

## 📊 Validation

After setup, validate calibration:
```bash
# Check calibration accuracy
npm run calibration:validate

# Expected accuracy: >95% spatial alignment
# Expected reprojection error: <2 pixels
```

## 🔄 Switching from USB to QR

1. **Initial Setup**: Use USB for precise calibration
2. **Daily Use**: Use QR codes for wireless operation
3. **Re-calibration**: Run full setup if devices move significantly

## 📞 Support

For issues:
1. Check device connections
2. Verify QR code positioning
3. Review calibration protocol: `docs/CALIBRATION_PROTOCOL.md`
4. Run setup script again: `npm run calibration:full`

## 🎉 Success Indicators
- ✅ All devices detected
- ✅ QR codes generated successfully
- ✅ Web interface loads correctly
- ✅ Devices connect via QR codes
- ✅ Motion tracking is accurate
- ✅ Spatial alignment is consistent

Your Combat Mirror System is now ready for multi-device calibration!

#!/bin/bash

# Start backend server
cd server && npm start &
SERVER_PID=$!

# Wait for server to be ready
sleep 3

# Start frontend preview
cd .. && npm run preview &
FRONTEND_PID=$!

# Function to handle shutdown
cleanup() {
    echo "Shutting down services..."
    kill $SERVER_PID $FRONTEND_PID 2>/dev/null
    exit 0
}

# Set up signal handlers
trap cleanup SIGINT SIGTERM

# Keep script running
echo "Services started:"
echo "  Backend API: http://localhost:3001"
echo "  Frontend: http://localhost:4173"
echo "Press Ctrl+C to stop"

# Wait for processes
wait $SERVER_PID $FRONTEND_PID
import SwiftUI
import LiveKit
import AVFoundation
import CodeScanner

// MARK: - Main App
@main
struct CombatMirrorClientApp: App {
    var body: some Scene {
        WindowGroup {
            ContentView()
        }
    }
}

// MARK: - Content View
struct ContentView: View {
    @StateObject private var roomManager = RoomManager()
    @State private var scannedURL: String = ""
    @State private var showingScanner = false
    
    var body: some View {
        NavigationView {
            VStack {
                if roomManager.isConnected {
                    VideoStreamView(roomManager: roomManager)
                } else {
                    ConnectionView(
                        roomManager: roomManager,
                        scannedURL: $scannedURL,
                        showingScanner: $showingScanner
                    )
                }
            }
            .navigationTitle("Combat Mirror Camera")
            .navigationBarTitleDisplayMode(.inline)
        }
    }
}

// MARK: - Connection View
struct ConnectionView: View {
    @ObservedObject var roomManager: RoomManager
    @Binding var scannedURL: String
    @Binding var showingScanner: Bool
    
    var body: some View {
        VStack(spacing: 20) {
            Text("Connect to Combat Mirror")
                .font(.title2)
                .fontWeight(.bold)
            
            Text("Scan QR code or enter connection details")
                .foregroundColor(.secondary)
            
            Button(action: {
                showingScanner = true
            }) {
                Label("Scan QR Code", systemImage: "qrcode.viewfinder")
                    .frame(maxWidth: .infinity)
                    .padding()
                    .background(Color.blue)
                    .foregroundColor(.white)
                    .cornerRadius(10)
            }
            .padding(.horizontal)
            
            if !scannedURL.isEmpty {
                VStack(alignment: .leading) {
                    Text("Scanned URL:")
                        .font(.caption)
                        .foregroundColor(.secondary)
                    Text(scannedURL)
                        .font(.caption)
                        .padding(8)
                        .background(Color.gray.opacity(0.1))
                        .cornerRadius(8)
                }
                .padding(.horizontal)
                
                Button(action: {
                    Task {
                        await roomManager.connect(from: scannedURL)
                    }
                }) {
                    if roomManager.isConnecting {
                        ProgressView()
                            .progressViewStyle(CircularProgressViewStyle(tint: .white))
                    } else {
                        Text("Connect")
                    }
                }
                .frame(maxWidth: .infinity)
                .padding()
                .background(Color.green)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.horizontal)
                .disabled(roomManager.isConnecting)
            }
            
            Spacer()
        }
        .padding(.top, 40)
        .sheet(isPresented: $showingScanner) {
            QRScannerView(scannedURL: $scannedURL)
        }
    }
}

// MARK: - Video Stream View
struct VideoStreamView: View {
    @ObservedObject var roomManager: RoomManager
    @State private var isFrontCamera = false
    
    var body: some View {
        ZStack {
            // Local video preview
            VideoView(track: roomManager.localVideoTrack)
                .background(Color.black)
            
            // Controls overlay
            VStack {
                HStack {
                    // Connection status
                    HStack {
                        Circle()
                            .fill(Color.green)
                            .frame(width: 12, height: 12)
                        Text("Connected")
                            .font(.caption)
                            .foregroundColor(.white)
                    }
                    .padding(8)
                    .background(Color.black.opacity(0.6))
                    .cornerRadius(20)
                    
                    Spacer()
                    
                    // Camera position
                    Text(roomManager.cameraPosition)
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(8)
                        .background(Color.black.opacity(0.6))
                        .cornerRadius(20)
                }
                .padding()
                
                Spacer()
                
                // Bottom controls
                HStack(spacing: 30) {
                    // Switch camera
                    Button(action: {
                        isFrontCamera.toggle()
                        Task {
                            await roomManager.switchCamera(position: isFrontCamera ? .front : .back)
                        }
                    }) {
                        Image(systemName: "camera.rotate")
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                            .background(Color.blue)
                            .clipShape(Circle())
                    }
                    
                    // Disconnect
                    Button(action: {
                        Task {
                            await roomManager.disconnect()
                        }
                    }) {
                        Image(systemName: "phone.down.fill")
                            .font(.title2)
                            .foregroundColor(.white)
                            .frame(width: 60, height: 60)
                            .background(Color.red)
                            .clipShape(Circle())
                    }
                }
                .padding(.bottom, 40)
            }
        }
        .ignoresSafeArea()
    }
}

// MARK: - Room Manager
class RoomManager: ObservableObject {
    @Published var isConnected = false
    @Published var isConnecting = false
    @Published var localVideoTrack: LocalVideoTrack?
    @Published var cameraPosition = "Unknown"
    
    private var room: Room?
    private let serverURL = "ws://************:7880" // Update with your server IP
    
    func connect(from urlString: String) async {
        guard let url = URL(string: urlString),
              let components = URLComponents(url: url, resolvingAgainstBaseURL: false),
              let queryItems = components.queryItems else {
            print("Invalid URL")
            return
        }
        
        // Extract parameters from URL
        guard let roomName = queryItems.first(where: { $0.name == "room" })?.value,
              let token = queryItems.first(where: { $0.name == "token" })?.value,
              let position = queryItems.first(where: { $0.name == "position" })?.value else {
            print("Missing required parameters")
            return
        }
        
        await MainActor.run {
            self.isConnecting = true
            self.cameraPosition = position
        }
        
        do {
            // Create room
            room = Room()
            
            // Setup local video track
            let localVideoTrack = LocalVideoTrack.createCameraTrack(options: CameraCaptureOptions(
                position: .back,
                dimensions: .h720_169
            ))
            self.localVideoTrack = localVideoTrack
            
            // Connect to room
            try await room?.connect(serverURL, token)
            
            // Publish video track
            try await room?.localParticipant.publishVideoTrack(localVideoTrack)
            
            await MainActor.run {
                self.isConnected = true
                self.isConnecting = false
            }
            
        } catch {
            print("Connection error: \(error)")
            await MainActor.run {
                self.isConnecting = false
            }
        }
    }
    
    func disconnect() async {
        await room?.disconnect()
        await MainActor.run {
            self.isConnected = false
            self.localVideoTrack = nil
        }
    }
    
    func switchCamera(position: AVCaptureDevice.Position) async {
        guard let track = localVideoTrack as? CameraCaptureTrack else { return }
        try? await track.switchCameraPosition(to: position)
    }
}

// MARK: - Video View (SwiftUI wrapper for video rendering)
struct VideoView: UIViewRepresentable {
    let track: VideoTrack?
    
    func makeUIView(context: Context) -> UIView {
        let view = UIView()
        view.backgroundColor = .black
        return view
    }
    
    func updateUIView(_ uiView: UIView, context: Context) {
        // Remove existing video views
        uiView.subviews.forEach { $0.removeFromSuperview() }
        
        // Add new video view if track exists
        if let track = track {
            let videoView = VideoView(frame: uiView.bounds)
            videoView.autoresizingMask = [.flexibleWidth, .flexibleHeight]
            track.add(videoRenderer: videoView)
            uiView.addSubview(videoView)
        }
    }
}

// MARK: - QR Scanner View (with real QR scanning)
struct QRScannerView: View {
    @Binding var scannedURL: String
    @Environment(\.presentationMode) var presentationMode
    @State private var isPresentingScanner = false
    @State private var scannedCode = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Scan QR Code")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Position the QR code from Combat Mirror within the camera frame")
                    .multilineTextAlignment(.center)
                    .foregroundColor(.secondary)
                    .padding(.horizontal)
                
                // QR Scanner using CodeScanner
                CodeScannerView(
                    codeTypes: [.qr],
                    simulatedData: "http://192.168.1.14:5175/camera-stream.html?room=combat-mirror-front-123&token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9&position=FRONT",
                    completion: handleScan
                )
                .frame(height: 300)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.blue, lineWidth: 2)
                )
                
                if !scannedCode.isEmpty {
                    VStack(alignment: .leading, spacing: 8) {
                        Text("Scanned URL:")
                            .font(.caption)
                            .foregroundColor(.secondary)
                        
                        Text(scannedCode)
                            .font(.caption)
                            .padding(12)
                            .background(Color.gray.opacity(0.1))
                            .cornerRadius(8)
                            .lineLimit(3)
                    }
                    .padding(.horizontal)
                }
                
                Button("Use Scanned Code") {
                    scannedURL = scannedCode
                    presentationMode.wrappedValue.dismiss()
                }
                .disabled(scannedCode.isEmpty)
                .frame(maxWidth: .infinity)
                .padding()
                .background(scannedCode.isEmpty ? Color.gray : Color.blue)
                .foregroundColor(.white)
                .cornerRadius(10)
                .padding(.horizontal)
                
                Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
                .foregroundColor(.red)
                
                Spacer()
            }
            .padding()
            .navigationBarHidden(true)
        }
    }
    
    private func handleScan(result: Result<ScanResult, ScanError>) {
        switch result {
        case .success(let result):
            let code = result.string
            if isValidCombatMirrorURL(code) {
                scannedCode = code
                // Auto-dismiss after successful scan
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                    scannedURL = code
                    presentationMode.wrappedValue.dismiss()
                }
            }
        case .failure(let error):
            print("QR Scanning failed: \(error.localizedDescription)")
        }
    }
    
    private func isValidCombatMirrorURL(_ url: String) -> Bool {
        return url.contains("camera-stream.html") && 
               url.contains("room=") && 
               url.contains("token=") && 
               url.contains("position=")
    }
}
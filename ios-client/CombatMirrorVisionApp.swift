import SwiftUI
import LiveKit

@main
struct CombatMirrorVisionApp: App {
    var body: some Scene {
        WindowGroup {
            CombatContextProvider {
                ConnectionView()
            }
        }
    }
}

// MARK: - Combat Context Provider
class CombatContext: ObservableObject {
    @Published var agentParticipant: RemoteParticipant?
    @Published var isConnected = false
    @Published var currentMetrics: CombatMetrics?
    @Published var coachingFeedback: [CoachingMessage] = []
    
    func addCoachingMessage(_ message: String, type: CoachingType = .technique) {
        let coaching = CoachingMessage(
            id: UUID(),
            message: message,
            type: type,
            timestamp: Date()
        )
        coachingFeedback.append(coaching)
        
        // Keep only last 20 messages
        if coachingFeedback.count > 20 {
            coachingFeedback.removeFirst()
        }
    }
}

struct CombatContextProvider<Content: View>: View {
    @StateObject private var combatContext = CombatContext()
    let content: Content
    
    init(@ViewBuilder content: () -> Content) {
        self.content = content()
    }
    
    var body: some View {
        content
            .environmentObject(combatContext)
    }
}

// MARK: - Combat Data Models
struct CombatMetrics {
    let stanceQuality: Double
    let guardPosition: String
    let balanceScore: Double
    let postureScore: Double
    let defensiveRating: Double
    let techniqueFocus: String
    let improvementArea: String
    let sessionProgress: String
    let timestamp: Date
}

struct CoachingMessage: Identifiable {
    let id: UUID
    let message: String
    let type: CoachingType
    let timestamp: Date
}

enum CoachingType {
    case technique
    case encouragement
    case correction
    case drill
    
    var color: Color {
        switch self {
        case .technique:
            return .blue
        case .encouragement:
            return .green
        case .correction:
            return .orange
        case .drill:
            return .purple
        }
    }
    
    var icon: String {
        switch self {
        case .technique:
            return "target"
        case .encouragement:
            return "hand.thumbsup"
        case .correction:
            return "exclamationmark.triangle"
        case .drill:
            return "figure.boxing"
        }
    }
}

// MARK: - Enhanced Connection View
struct ConnectionView: View {
    @StateObject private var tokenService = TokenService()
    @EnvironmentObject var combatContext: CombatContext
    @State private var participantName = "Fighter"
    @State private var roomName = "combat-training"
    @State private var isConnecting = false
    @State private var showingScanner = false
    @State private var scannedURL = ""
    
    var body: some View {
        NavigationView {
            VStack(spacing: 30) {
                // App Header
                VStack(spacing: 10) {
                    Image(systemName: "figure.boxing")
                        .font(.system(size: 80))
                        .foregroundColor(.red)
                    
                    Text("Combat Mirror AI Coach")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("Real-time technique analysis and coaching")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                // Connection Options
                VStack(spacing: 20) {
                    // QR Code Scanner
                    Button(action: {
                        showingScanner = true
                    }) {
                        Label("Scan QR Code", systemImage: "qrcode.viewfinder")
                            .frame(maxWidth: .infinity)
                            .padding()
                            .background(Color.blue)
                            .foregroundColor(.white)
                            .cornerRadius(12)
                    }
                    
                    // Manual Connection
                    VStack(spacing: 15) {
                        TextField("Fighter Name", text: $participantName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        TextField("Training Room", text: $roomName)
                            .textFieldStyle(RoundedBorderTextFieldStyle())
                        
                        Button(action: connectManually) {
                            if isConnecting {
                                ProgressView()
                                    .progressViewStyle(CircularProgressViewStyle(tint: .white))
                            } else {
                                Text("Start Training Session")
                            }
                        }
                        .frame(maxWidth: .infinity)
                        .padding()
                        .background(Color.red)
                        .foregroundColor(.white)
                        .cornerRadius(12)
                        .disabled(isConnecting)
                    }
                }
                
                Spacer()
                
                // Status Information
                VStack(spacing: 10) {
                    if combatContext.isConnected {
                        Label("Connected to AI Coach", systemImage: "checkmark.circle.fill")
                            .foregroundColor(.green)
                    } else {
                        Label("Ready to connect", systemImage: "wifi.slash")
                            .foregroundColor(.secondary)
                    }
                    
                    Text("Ensure camera permissions are enabled")
                        .font(.caption)
                        .foregroundColor(.secondary)
                }
            }
            .padding()
            .navigationBarHidden(true)
        }
        .sheet(isPresented: $showingScanner) {
            QRScannerView(scannedURL: $scannedURL)
        }
        .onChange(of: scannedURL) { url in
            if !url.isEmpty {
                connectFromURL(url)
            }
        }
        .navigationViewStyle(StackNavigationViewStyle())
    }
    
    private func connectManually() {
        isConnecting = true
        
        Task {
            do {
                // Generate token for self-hosted LiveKit
                let token = generateLocalToken(roomName: roomName, participantName: participantName)
                await connectToRoom(token: token, serverUrl: "ws://************:7880")
            } catch {
                print("Connection error: \(error)")
                isConnecting = false
            }
        }
    }
    
    private func connectFromURL(_ url: String) {
        // Parse QR code URL and extract connection details
        guard let urlComponents = URLComponents(string: url),
              let queryItems = urlComponents.queryItems,
              let room = queryItems.first(where: { $0.name == "room" })?.value,
              let token = queryItems.first(where: { $0.name == "token" })?.value else {
            return
        }
        
        roomName = room
        
        Task {
            await connectToRoom(token: token, serverUrl: "ws://************:7880")
        }
    }
    
    private func connectToRoom(token: String, serverUrl: String) async {
        // Navigate to the main training view
        // This would typically use NavigationLink or programmatic navigation
        combatContext.isConnected = true
        isConnecting = false
    }
    
    private func generateLocalToken(roomName: String, participantName: String) -> String {
        // For local development - in production, get token from server
        return "local_dev_token_\(roomName)_\(participantName)"
    }
}

// MARK: - Token Service (Simplified for self-hosted)
class TokenService: ObservableObject {
    func getToken(roomName: String, participantName: String) async throws -> String {
        // In a real implementation, this would call your token server
        // For now, return a placeholder token
        return "dev_token_\(roomName)_\(participantName)"
    }
}

// MARK: - QR Scanner View
struct QRScannerView: View {
    @Binding var scannedURL: String
    @Environment(\.presentationMode) var presentationMode
    
    var body: some View {
        NavigationView {
            VStack(spacing: 20) {
                Text("Scan Combat Mirror QR Code")
                    .font(.title2)
                    .fontWeight(.bold)
                
                Text("Position the QR code from your Combat Mirror web interface within the camera frame")
                    .multilineTextAlignment(.center)
                    .padding(.horizontal)
                
                // QR Scanner implementation would go here
                // For now, show a placeholder
                RoundedRectangle(cornerRadius: 16)
                    .stroke(Color.blue, lineWidth: 3)
                    .frame(height: 300)
                    .overlay(
                        VStack {
                            Image(systemName: "qrcode.viewfinder")
                                .font(.system(size: 60))
                                .foregroundColor(.blue)
                            Text("QR Scanner")
                                .font(.headline)
                                .foregroundColor(.blue)
                        }
                    )
                
                Button("Simulate Scan (Development)") {
                    scannedURL = "https://************:5173/camera-stream.html?room=combat-training&token=dev_token&position=FRONT"
                    presentationMode.wrappedValue.dismiss()
                }
                .padding()
                
                Spacer()
            }
            .padding()
            .navigationBarTitleDisplayMode(.inline)
            .navigationBarItems(
                trailing: Button("Cancel") {
                    presentationMode.wrappedValue.dismiss()
                }
            )
        }
    }
}
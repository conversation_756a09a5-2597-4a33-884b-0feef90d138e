// swift-tools-version:5.9
import PackageDescription

let package = Package(
    name: "CombatMirrorClient",
    platforms: [
        .iOS(.v14)
    ],
    products: [
        .executable(name: "CombatMirrorClient", targets: ["CombatMirrorClient"])
    ],
    dependencies: [
        .package(url: "https://github.com/livekit/client-sdk-swift", from: "2.0.0"),
        .package(url: "https://github.com/twostraws/CodeScanner", from: "2.0.0")
    ],
    targets: [
        .executableTarget(
            name: "CombatMirrorClient",
            dependencies: [
                .product(name: "LiveKit", package: "client-sdk-swift"),
                .product(name: "CodeScanner", package: "CodeScanner")
            ]
        )
    ]
)
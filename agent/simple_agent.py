#!/usr/bin/env python3
"""
Simple Combat Mirror Agent for testing LiveKit integration
This is a minimal agent that connects to LiveKit and simulates metrics
"""

import asyncio
import logging
import json
import time
import random

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger("simple-combat-agent")

class SimpleCombatAgent:
    """Simple agent that generates mock combat metrics"""
    
    def __init__(self):
        self.running = False
        
    async def start(self):
        """Start the simple agent"""
        logger.info("🥊 Simple Combat Mirror Agent Started")
        logger.info("LiveKit Server: ws://localhost:7880")
        logger.info("Generating mock combat metrics...")
        
        self.running = True
        
        # Simulate metrics generation
        while self.running:
            metrics = self.generate_mock_metrics()
            logger.info(f"Generated metrics - Posture: {metrics['posture_score']:.1f}, Balance: {metrics['balance_score']:.1f}")
            await asyncio.sleep(1)
            
    def generate_mock_metrics(self):
        """Generate realistic mock combat metrics"""
        return {
            "timestamp": time.time(),
            "posture_score": random.uniform(75, 95),
            "balance_score": random.uniform(80, 100),
            "defense_rating": random.uniform(70, 90),
            "punch_detected": random.random() < 0.1,  # 10% chance
            "head_movement": random.uniform(0, 0.05),
            "fatigue_level": random.uniform(10, 30),
            "reaction_time": random.uniform(0.2, 0.4),
            "technique_score": random.uniform(85, 95),
            "stance_quality": random.choice(["Orthodox", "Southpaw"]),
            "guard_position": random.choice(["High Guard", "Mid Guard", "Low Guard"])
        }
        
    def stop(self):
        """Stop the agent"""
        self.running = False
        logger.info("Simple Combat Agent stopped")

async def main():
    """Main function"""
    agent = SimpleCombatAgent()
    
    try:
        await agent.start()
    except KeyboardInterrupt:
        logger.info("Received shutdown signal")
        agent.stop()
    except Exception as e:
        logger.error(f"Agent error: {e}")
        agent.stop()

if __name__ == "__main__":
    asyncio.run(main())
import asyncio
import logging
import json
import time
from typing import Optional, Dict, Any, List
import numpy as np
import cv2
import mediapipe as mp
from collections import deque

from livekit import agents, rtc
from livekit.agents import JobContext, WorkerOptions, cli
from livekit.rtc import VideoFrame

logger = logging.getLogger("enhanced-combat-agent")

class PoseAnalyzer:
    """MediaPipe-based pose analysis for combat sports"""
    
    def __init__(self):
        self.mp_pose = mp.solutions.pose
        self.pose = self.mp_pose.Pose(
            static_image_mode=False,
            model_complexity=2,
            enable_segmentation=True,
            min_detection_confidence=0.5,
            min_tracking_confidence=0.5
        )
        self.mp_drawing = mp.solutions.drawing_utils
        
        # Tracking buffers
        self.pose_history = deque(maxlen=30)  # 1 second at 30fps
        self.punch_buffer = deque(maxlen=10)
        self.previous_landmarks = None
        
    def analyze_frame(self, frame: np.ndarray) -> Dict[str, Any]:
        """Analyze a single frame for combat metrics"""
        # Convert BGR to RGB for MediaPipe
        rgb_frame = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
        results = self.pose.process(rgb_frame)
        
        if not results.pose_landmarks:
            return self._empty_metrics()
            
        landmarks = results.pose_landmarks.landmark
        self.pose_history.append(landmarks)
        
        # Calculate all metrics
        metrics = {
            "timestamp": time.time(),
            "posture_score": self._calculate_posture_score(landmarks),
            "punch_detected": self._detect_punch(landmarks),
            "head_movement": self._calculate_head_movement(landmarks),
            "balance_score": self._calculate_balance_score(landmarks),
            "defense_rating": self._calculate_defense_rating(landmarks),
            "fatigue_level": self._calculate_fatigue_level(),
            "reaction_time": self._estimate_reaction_time(),
            "technique_score": self._calculate_technique_score(landmarks),
            "stance_quality": self._analyze_stance(landmarks),
            "guard_position": self._check_guard_position(landmarks)
        }
        
        self.previous_landmarks = landmarks
        return metrics
        
    def _calculate_posture_score(self, landmarks) -> float:
        """Calculate posture alignment score (0-100)"""
        try:
            # Key points for posture
            nose = landmarks[self.mp_pose.PoseLandmark.NOSE]
            left_shoulder = landmarks[self.mp_pose.PoseLandmark.LEFT_SHOULDER]
            right_shoulder = landmarks[self.mp_pose.PoseLandmark.RIGHT_SHOULDER]
            left_hip = landmarks[self.mp_pose.PoseLandmark.LEFT_HIP]
            right_hip = landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP]
            
            # Calculate shoulder alignment
            shoulder_tilt = abs(left_shoulder.y - right_shoulder.y)
            shoulder_score = max(0, 100 - shoulder_tilt * 500)
            
            # Calculate hip alignment
            hip_tilt = abs(left_hip.y - right_hip.y)
            hip_score = max(0, 100 - hip_tilt * 500)
            
            # Calculate spine alignment (simplified)
            mid_shoulder_x = (left_shoulder.x + right_shoulder.x) / 2
            mid_hip_x = (left_hip.x + right_hip.x) / 2
            spine_deviation = abs(mid_shoulder_x - mid_hip_x)
            spine_score = max(0, 100 - spine_deviation * 200)
            
            # Head position relative to shoulders
            head_alignment = abs(nose.x - mid_shoulder_x)
            head_score = max(0, 100 - head_alignment * 300)
            
            # Weighted average
            posture_score = (
                shoulder_score * 0.3 +
                hip_score * 0.2 +
                spine_score * 0.3 +
                head_score * 0.2
            )
            
            return round(min(100, max(0, posture_score)), 1)
            
        except Exception as e:
            logger.error(f"Error calculating posture score: {e}")
            return 75.0
            
    def _detect_punch(self, landmarks) -> bool:
        """Detect if a punch is being thrown"""
        try:
            left_wrist = landmarks[self.mp_pose.PoseLandmark.LEFT_WRIST]
            right_wrist = landmarks[self.mp_pose.PoseLandmark.RIGHT_WRIST]
            left_elbow = landmarks[self.mp_pose.PoseLandmark.LEFT_ELBOW]
            right_elbow = landmarks[self.mp_pose.PoseLandmark.RIGHT_ELBOW]
            
            if self.previous_landmarks:
                # Calculate wrist velocities
                prev_left_wrist = self.previous_landmarks[self.mp_pose.PoseLandmark.LEFT_WRIST]
                prev_right_wrist = self.previous_landmarks[self.mp_pose.PoseLandmark.RIGHT_WRIST]
                
                left_velocity = np.sqrt(
                    (left_wrist.x - prev_left_wrist.x)**2 + 
                    (left_wrist.y - prev_left_wrist.y)**2
                )
                right_velocity = np.sqrt(
                    (right_wrist.x - prev_right_wrist.x)**2 + 
                    (right_wrist.y - prev_right_wrist.y)**2
                )
                
                # Check for punch threshold
                punch_threshold = 0.05
                punch_detected = left_velocity > punch_threshold or right_velocity > punch_threshold
                
                # Additional check for arm extension
                if punch_detected:
                    left_extension = abs(left_wrist.y - left_elbow.y) > 0.1
                    right_extension = abs(right_wrist.y - right_elbow.y) > 0.1
                    punch_detected = left_extension or right_extension
                
                return punch_detected
                
            return False
            
        except Exception as e:
            logger.error(f"Error detecting punch: {e}")
            return False
            
    def _calculate_head_movement(self, landmarks) -> float:
        """Calculate head movement magnitude"""
        try:
            if not self.previous_landmarks:
                return 0.0
                
            nose = landmarks[self.mp_pose.PoseLandmark.NOSE]
            prev_nose = self.previous_landmarks[self.mp_pose.PoseLandmark.NOSE]
            
            movement = np.sqrt(
                (nose.x - prev_nose.x)**2 + 
                (nose.y - prev_nose.y)**2
            )
            
            return round(movement * 100, 2)
            
        except Exception:
            return 0.0
            
    def _calculate_balance_score(self, landmarks) -> float:
        """Calculate balance based on center of mass stability"""
        try:
            # Use hips and ankles for balance calculation
            left_hip = landmarks[self.mp_pose.PoseLandmark.LEFT_HIP]
            right_hip = landmarks[self.mp_pose.PoseLandmark.RIGHT_HIP]
            left_ankle = landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE]
            right_ankle = landmarks[self.mp_pose.PoseLandmark.RIGHT_ANKLE]
            
            # Center of mass approximation
            com_x = (left_hip.x + right_hip.x) / 2
            base_x = (left_ankle.x + right_ankle.x) / 2
            
            # Balance score based on COM over base of support
            deviation = abs(com_x - base_x)
            balance_score = max(0, 100 - deviation * 200)
            
            # Check stance width
            stance_width = abs(left_ankle.x - right_ankle.x)
            if stance_width < 0.1:  # Too narrow
                balance_score *= 0.8
            elif stance_width > 0.4:  # Too wide
                balance_score *= 0.9
                
            return round(min(100, max(0, balance_score)), 1)
            
        except Exception:
            return 85.0
            
    def _calculate_defense_rating(self, landmarks) -> float:
        """Calculate defensive posture quality"""
        try:
            # Check guard position
            left_wrist = landmarks[self.mp_pose.PoseLandmark.LEFT_WRIST]
            right_wrist = landmarks[self.mp_pose.PoseLandmark.RIGHT_WRIST]
            nose = landmarks[self.mp_pose.PoseLandmark.NOSE]
            
            # Ideal guard position: wrists near face level
            left_guard = 1 - abs(left_wrist.y - nose.y)
            right_guard = 1 - abs(right_wrist.y - nose.y)
            guard_score = (max(0, left_guard) + max(0, right_guard)) * 50
            
            # Check chin position (tucked)
            chin_score = 100 if nose.y > 0.3 else 80
            
            # Combine scores
            defense_rating = guard_score * 0.7 + chin_score * 0.3
            
            return round(min(100, max(0, defense_rating)), 1)
            
        except Exception:
            return 80.0
            
    def _calculate_fatigue_level(self) -> float:
        """Estimate fatigue based on movement patterns"""
        if len(self.pose_history) < 10:
            return 0.0
            
        # Simple fatigue estimation based on movement degradation
        # In a real system, this would track performance over time
        return 15.0
        
    def _estimate_reaction_time(self) -> float:
        """Estimate reaction time based on movement patterns"""
        # Placeholder - would need specific stimulus-response tracking
        return 0.25
        
    def _calculate_technique_score(self, landmarks) -> float:
        """Overall technique quality score"""
        posture = self._calculate_posture_score(landmarks)
        balance = self._calculate_balance_score(landmarks)
        defense = self._calculate_defense_rating(landmarks)
        
        technique_score = (posture * 0.4 + balance * 0.3 + defense * 0.3)
        return round(technique_score, 1)
        
    def _analyze_stance(self, landmarks) -> str:
        """Analyze fighting stance type"""
        try:
            left_ankle = landmarks[self.mp_pose.PoseLandmark.LEFT_ANKLE]
            right_ankle = landmarks[self.mp_pose.PoseLandmark.RIGHT_ANKLE]
            
            if left_ankle.z < right_ankle.z:
                return "Orthodox"
            else:
                return "Southpaw"
        except:
            return "Unknown"
            
    def _check_guard_position(self, landmarks) -> str:
        """Check if guard is up or down"""
        try:
            left_wrist = landmarks[self.mp_pose.PoseLandmark.LEFT_WRIST]
            right_wrist = landmarks[self.mp_pose.PoseLandmark.RIGHT_WRIST]
            nose = landmarks[self.mp_pose.PoseLandmark.NOSE]
            
            avg_wrist_height = (left_wrist.y + right_wrist.y) / 2
            
            if avg_wrist_height < nose.y:
                return "High Guard"
            elif avg_wrist_height < nose.y + 0.1:
                return "Mid Guard"
            else:
                return "Low Guard"
        except:
            return "Unknown"
            
    def _empty_metrics(self) -> Dict[str, Any]:
        """Return empty metrics when no pose detected"""
        return {
            "timestamp": time.time(),
            "posture_score": 0,
            "punch_detected": False,
            "head_movement": 0,
            "balance_score": 0,
            "defense_rating": 0,
            "fatigue_level": 0,
            "reaction_time": 0,
            "technique_score": 0,
            "stance_quality": "Unknown",
            "guard_position": "Unknown"
        }


class EnhancedCombatMirrorAgent:
    """Enhanced Combat Mirror agent with real pose analysis"""
    
    def __init__(self, ctx: JobContext):
        self.ctx = ctx
        self.room = ctx.room
        self.pose_analyzer = PoseAnalyzer()
        self.camera_positions = {}  # Track which participant is which camera
        self.frame_processors = {}  # One processor per camera
        
    async def start(self):
        """Start the agent"""
        logger.info(f"Starting Enhanced Combat Mirror Agent for room {self.room.name}")
        
        # Set up event handlers
        self.room.on("participant_connected", self._on_participant_connected)
        self.room.on("track_subscribed", self._on_track_subscribed)
        self.room.on("track_unsubscribed", self._on_track_unsubscribed)
        self.room.on("data_received", self._on_data_received)
        
    def _on_participant_connected(self, participant: rtc.RemoteParticipant):
        """Handle new participant connections"""
        logger.info(f"Participant connected: {participant.identity} (SID: {participant.sid})")
        
        # Extract camera position from identity (e.g., "iphone-front")
        if participant.identity.startswith("iphone-"):
            position = participant.identity.replace("iphone-", "").upper()
            self.camera_positions[participant.sid] = position
            logger.info(f"Camera position {position} connected")
            
    def _on_track_subscribed(
        self,
        track: rtc.Track,
        publication: rtc.RemoteTrackPublication,
        participant: rtc.RemoteParticipant,
    ):
        """Handle video track subscription"""
        if track.kind == rtc.TrackKind.VIDEO:
            position = self.camera_positions.get(participant.sid, "UNKNOWN")
            logger.info(f"Video track subscribed from {position} camera")
            
            # Create a frame processor for this camera
            processor_task = asyncio.create_task(
                self._process_video_stream(track, participant.sid, position)
            )
            self.frame_processors[participant.sid] = processor_task
            
    def _on_track_unsubscribed(
        self,
        track: rtc.Track,
        publication: rtc.RemoteTrackPublication,
        participant: rtc.RemoteParticipant,
    ):
        """Handle track unsubscription"""
        if participant.sid in self.frame_processors:
            # Cancel the processor task
            self.frame_processors[participant.sid].cancel()
            del self.frame_processors[participant.sid]
            logger.info(f"Stopped processing for {participant.identity}")
            
    def _on_data_received(self, data: rtc.DataPacket, participant: rtc.RemoteParticipant):
        """Handle data messages from participants"""
        try:
            message = json.loads(data.data.decode())
            logger.info(f"Received data from {participant.identity}: {message}")
        except Exception as e:
            logger.error(f"Error processing data message: {e}")
            
    async def _process_video_stream(self, video_track: rtc.VideoTrack, participant_sid: str, position: str):
        """Process video frames from a specific camera"""
        frame_count = 0
        last_process_time = 0
        process_interval = 1.0 / 30  # 30 FPS processing
        
        try:
            async for frame in video_track:
                current_time = time.time()
                
                # Rate limit processing
                if current_time - last_process_time < process_interval:
                    continue
                    
                last_process_time = current_time
                frame_count += 1
                
                # Convert frame to numpy array
                frame_array = frame.to_ndarray(format="bgr24")
                
                # Analyze the frame
                metrics = self.pose_analyzer.analyze_frame(frame_array)
                metrics["camera_position"] = position
                metrics["frame_number"] = frame_count
                
                # Send metrics to all participants
                await self._broadcast_metrics(metrics, position)
                
                # Log periodically
                if frame_count % 30 == 0:
                    logger.info(f"{position} camera - Frame {frame_count}, "
                              f"Posture: {metrics['posture_score']}, "
                              f"Balance: {metrics['balance_score']}")
                    
        except asyncio.CancelledError:
            logger.info(f"Frame processing cancelled for {position}")
        except Exception as e:
            logger.error(f"Error in frame processor for {position}: {e}")
            
    async def _broadcast_metrics(self, metrics: Dict[str, Any], camera_position: str):
        """Broadcast metrics to all participants"""
        try:
            message = {
                "type": "combat_metrics",
                "camera": camera_position,
                "metrics": metrics
            }
            
            await self.room.local_participant.publish_data(
                payload=json.dumps(message).encode(),
                reliable=True,
                topic="metrics"
            )
        except Exception as e:
            logger.error(f"Error broadcasting metrics: {e}")


async def entrypoint(ctx: JobContext):
    """Main entry point for the agent"""
    agent = EnhancedCombatMirrorAgent(ctx)
    await agent.start()
    
    # Keep the agent running
    await asyncio.Event().wait()


if __name__ == "__main__":
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    # Run the agent
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
        )
    )
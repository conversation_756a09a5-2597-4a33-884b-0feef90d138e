# Combat Mirror Vision Agent Requirements
# Based on LiveKit Vision Demo architecture

# Core LiveKit Agents framework
livekit-agents[all]>=0.9.0
livekit-plugins-google>=0.9.0

# Additional dependencies for combat analysis
python-dotenv>=1.0.0
Pillow>=10.0.0

# Optional: MediaPipe for advanced pose analysis (requires compatible Python version)
# mediapipe>=0.10.0  # Uncomment when Python 3.12 support is available

# Development tools
ruff>=0.1.0
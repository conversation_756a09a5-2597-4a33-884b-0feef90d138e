#!/bin/bash

# Combat Mirror LiveKit Agent Runner

echo "🥊 Combat Mirror LiveKit Agent Setup"
echo "===================================="

# Check if virtual environment exists
if [ ! -d "venv" ]; then
    echo "Creating Python virtual environment..."
    python3 -m venv venv
fi

# Activate virtual environment
source venv/bin/activate

# Install/upgrade dependencies
echo "Installing dependencies..."
pip install -r requirements.txt

# Set environment variables
export LIVEKIT_API_KEY="APIfightmaster"
export LIVEKIT_API_SECRET="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ"
export LIVEKIT_WS_URL="ws://localhost:7880"

# Run the agent
echo ""
echo "Starting Combat Mirror Vision Agent..."
echo "LiveKit Server: $LIVEKIT_WS_URL"
echo ""

python enhanced_combat_agent.py
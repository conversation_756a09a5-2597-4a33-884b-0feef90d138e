import logging
import asyncio
import base64
import json
import time
from typing import Optional, Dict, Any
from dotenv import load_dotenv

from livekit.agents import (
    Agent,
    AgentSession,
    JobContext,
    RoomInputOptions,
    WorkerOptions,
    cli,
    get_job_context,
)
from livekit.agents.llm import ImageContent
from livekit.plugins import google
from livekit import rtc

logger = logging.getLogger("combat-vision-agent")

load_dotenv()


class CombatVisionAgent(Agent):
    """Combat Mirror AI Coach - Real-time video analysis for combat sports training"""
    
    def __init__(self) -> None:
        self._tasks = []
        self._metrics_history = []
        self._frame_count = 0
        self._last_analysis_time = 0
        
        super().__init__(
            instructions="""
You are a professional combat sports coach and trainer with expertise in boxing, MMA, and martial arts. 
You analyze video feeds to provide real-time feedback on:
- Fighting stance and posture
- Defensive positioning and guard
- Punch technique and timing
- Footwork and balance
- Head movement and defensive patterns
- Overall technique improvement

Provide encouraging but constructive feedback focused on safety and technique improvement.
Keep responses concise and actionable. Use boxing/MMA terminology appropriately.
Always prioritize fighter safety and proper form over aggression.
            """,
            llm=google.beta.realtime.RealtimeModel(
                voice="Echo", # More suitable for a coach
                temperature=0.7,
            ),
        )

    async def on_enter(self):
        """Initialize the combat training session"""
        logger.info("🥊 Combat Mirror Coach entering session")
        
        # Register for video frame processing
        def _video_frame_handler(reader, participant_identity):
            task = asyncio.create_task(
                self._process_video_frame(reader, participant_identity)
            )
            self._tasks.append(task)
            task.add_done_callback(lambda t: self._tasks.remove(t))
            
        get_job_context().room.register_byte_stream_handler("video_analysis", _video_frame_handler)

        # Welcome the fighter
        self.session.generate_reply(
            instructions="""
Greet the fighter and introduce yourself as their AI combat coach. 
Briefly explain that you'll be analyzing their video feed to provide real-time 
technique feedback and training guidance. Ask them to show their fighting stance.
Keep it encouraging and professional.
            """
        )
    
    async def _process_video_frame(self, reader, participant_identity):
        """Process incoming video frames for combat analysis"""
        current_time = time.time()
        
        # Rate limit analysis - analyze every 2 seconds for detailed feedback
        if current_time - self._last_analysis_time < 2.0:
            return
            
        self._last_analysis_time = current_time
        self._frame_count += 1
        
        logger.info("Processing combat video frame %d from %s", self._frame_count, participant_identity)
        
        try:
            image_bytes = bytes()
            async for chunk in reader:
                image_bytes += chunk

            # Generate combat-specific analysis prompt
            analysis_prompt = self._generate_analysis_prompt()
            
            chat_ctx = self.chat_ctx.copy()
            chat_ctx.add_message(
                role="user",
                content=[
                    f"Frame {self._frame_count}: {analysis_prompt}",
                    ImageContent(
                        image=f"data:image/jpeg;base64,{base64.b64encode(image_bytes).decode('utf-8')}"
                    )
                ],
            )
            
            await self.update_chat_ctx(chat_ctx)
            
            # Generate coaching feedback
            await self._generate_coaching_feedback()
            
        except Exception as e:
            logger.error("Error processing combat video frame: %s", e)

    def _generate_analysis_prompt(self) -> str:
        """Generate analysis prompt based on frame count and training focus"""
        prompts = [
            "Analyze the fighter's stance and posture. Are their feet positioned correctly? Is their guard up?",
            "Evaluate their defensive positioning. How is their head movement and guard placement?",
            "Assess their balance and weight distribution. Are they centered and ready to move?",
            "Look at their hand positioning and guard. Are they protecting their centerline?",
            "Analyze any punch technique visible. Comment on form, rotation, and follow-through.",
            "Evaluate their overall fighting posture and readiness position.",
            "Check for common technical errors - dropped hands, poor stance, off-balance positioning.",
            "Assess their defensive awareness - chin position, shoulder positioning, eye focus.",
        ]
        
        # Cycle through different analysis focuses
        return prompts[self._frame_count % len(prompts)]

    async def _generate_coaching_feedback(self):
        """Generate real-time coaching feedback based on visual analysis"""
        try:
            # Generate immediate coaching response
            await self.session.generate_reply(
                instructions="""
Based on what you just observed in the video frame, provide specific, 
actionable coaching feedback. Focus on:
1. One key technique point to improve
2. One thing they're doing well
3. A specific drill or adjustment to try

Keep it under 15 seconds of speech. Be encouraging but specific.
Use proper boxing/MMA terminology.
                """
            )
            
        except Exception as e:
            logger.error("Error generating coaching feedback: %s", e)

    async def _broadcast_metrics(self, metrics: Dict[str, Any]):
        """Broadcast combat metrics to the room"""
        try:
            ctx = get_job_context()
            if ctx and ctx.room:
                message = {
                    "type": "combat_metrics",
                    "timestamp": time.time(),
                    "frame_count": self._frame_count,
                    "metrics": metrics
                }
                
                await ctx.room.local_participant.publish_data(
                    payload=json.dumps(message).encode(),
                    reliable=True,
                    topic="combat_coaching"
                )
                
        except Exception as e:
            logger.error("Error broadcasting metrics: %s", e)

    def _extract_mock_metrics(self) -> Dict[str, Any]:
        """Extract mock combat metrics (placeholder for actual analysis)"""
        return {
            "stance_quality": 85.5,
            "guard_position": "High Guard",
            "balance_score": 92.0,
            "posture_score": 88.5,
            "defensive_rating": 90.0,
            "technique_focus": "Stance and Guard",
            "improvement_area": "Head Movement",
            "session_progress": f"Frame {self._frame_count}"
        }


async def entrypoint(ctx: JobContext):
    """Main entry point for the Combat Vision Agent"""
    logger.info("🥊 Starting Combat Mirror Vision Agent")
    await ctx.connect()
    
    session = AgentSession()
    await session.start(
        agent=CombatVisionAgent(),
        room=ctx.room,
        room_input_options=RoomInputOptions(
            video_enabled=True,
            audio_enabled=True,
            # Note: noise_cancellation requires LiveKit Cloud
            # noise_cancellation=noise_cancellation.BVC(),
        ),
    )


if __name__ == "__main__":
    # Configure logging for combat training
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    )
    
    cli.run_app(
        WorkerOptions(
            entrypoint_fnc=entrypoint,
            # Self-hosted configuration
            api_key="APIfightmaster",
            api_secret="xKhTcmPB8n3WQqzYgNpR7jLFvEaVbDuA4MXSe6Ct9fZ",
            ws_url="ws://localhost:7880",
        )
    )
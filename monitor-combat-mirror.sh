#!/bin/bash

# Combat Mirror System Monitor
# Updates every 2 seconds with service status

while true; do
    clear
    echo "╔══════════════════════════════════════════════════════════════╗"
    echo "║           COMBAT MIRROR SYSTEM MONITOR                        ║"
    echo "╠══════════════════════════════════════════════════════════════╣"
    echo "║  $(date '+%Y-%m-%d %H:%M:%S')                                         ║"
    echo "╚══════════════════════════════════════════════════════════════╝"
    echo ""
    
    # Backend Status
    echo "┌─ BACKEND API (Port 5557) ────────────────────────────────────┐"
    if curl -s http://localhost:5557/api/health > /dev/null 2>&1; then
        echo "│ Status: ✅ RUNNING                                           │"
        HEALTH=$(curl -s http://localhost:5557/api/health | jq -r '.message' 2>/dev/null || echo "Unknown")
        echo "│ Health: ${HEALTH:0:50}           │"
    else
        echo "│ Status: ❌ OFFLINE                                           │"
    fi
    echo "└───────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Copyparty Status
    echo "┌─ COPYPARTY SERVER (Port 5923) ───────────────────────────────┐"
    if curl -sI http://localhost:5923 | grep -q "200"; then
        echo "│ Status: ✅ RUNNING                                           │"
        echo "│ Type: WebDAV File Server                                     │"
    else
        echo "│ Status: ❌ OFFLINE                                           │"
    fi
    echo "└───────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Frontend Status
    echo "┌─ FRONTEND (Port 3006) ────────────────────────────────────────┐"
    if lsof -i :3006 | grep -q LISTEN; then
        echo "│ Status: ✅ RUNNING                                           │"
        echo "│ URL: http://localhost:3006                                   │"
    else
        echo "│ Status: ❌ OFFLINE                                           │"
    fi
    echo "└───────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Process Info
    echo "┌─ PROCESS INFORMATION ─────────────────────────────────────────┐"
    echo "│ Active Node Processes:                                       │"
    ps aux | grep -E "node.*index.js|npm run dev" | grep -v grep | head -2 | while read line; do
        PID=$(echo $line | awk '{print $2}')
        CPU=$(echo $line | awk '{print $3}')
        MEM=$(echo $line | awk '{print $4}')
        CMD=$(echo $line | awk '{for(i=11;i<=NF;i++) printf "%s ", $i; print ""}' | cut -c1-40)
        printf "│ PID: %-6s CPU: %5s%% MEM: %5s%% %-25s│\n" "$PID" "$CPU" "$MEM" "$CMD"
    done
    echo "└───────────────────────────────────────────────────────────────┘"
    echo ""
    
    # Network Connections
    echo "┌─ NETWORK ACTIVITY ────────────────────────────────────────────┐"
    CONNECTIONS=$(lsof -i :5557,:5923,:3006 2>/dev/null | grep -c ESTABLISHED)
    echo "│ Active Connections: $CONNECTIONS                                     │"
    echo "└───────────────────────────────────────────────────────────────┘"
    echo ""
    
    echo "Press Ctrl+C to exit | Refreshing every 2 seconds..."
    
    sleep 2
done
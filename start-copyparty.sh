#!/bin/bash

# Start Copyparty File Server for Combat Mirror System
set -e

echo "🗂️  Starting Copyparty File Server for Combat Mirror"
echo "=================================================="

# Colors for output
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

# Create required directories
echo -e "${YELLOW}📁 Creating storage directories...${NC}"
mkdir -p uploads recordings exports copyparty-db

# Check if Copyparty is installed
if ! command -v copyparty &> /dev/null && ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 is required but not installed.${NC}"
    echo "   Install with: brew install python3"
    exit 1
fi

# Download Copyparty if not present
if [ ! -f "copyparty.py" ]; then
    echo -e "${YELLOW}📥 Downloading Copyparty...${NC}"
    curl -L https://github.com/9001/copyparty/releases/latest/download/copyparty-sfx.py -o copyparty.py
    chmod +x copyparty.py
    echo -e "${GREEN}✅ Copyparty downloaded${NC}"
fi

# Configuration file
CONFIG_FILE="copyparty-config/copyparty.conf"

# Create basic config if it doesn't exist
if [ ! -f "$CONFIG_FILE" ]; then
    echo -e "${YELLOW}📝 Creating configuration...${NC}"
    mkdir -p copyparty-config
    cat > "$CONFIG_FILE" << 'EOF'
[global]
  e2dsa    # enable file deduplication
  e2ts     # enable file indexing
  ed       # enable markdown editor
  em       # enable media indexing
  et       # enable thumbnails
  j        # enable uploads
  cors     # enable CORS for API access
  s        # searchable
  
[/uploads]
  accs: rw
  flags: dupe move

[/recordings]
  accs: rw
  flags: dupe ts

[/exports]
  accs: rw
  flags: dupe move
EOF
    echo -e "${GREEN}✅ Configuration created${NC}"
fi

# Start Copyparty
echo -e "${YELLOW}🚀 Starting Copyparty server...${NC}"
echo ""
echo "📍 Server URLs:"
echo "   Web Interface: http://localhost:3923"
echo "   WebDAV:        http://localhost:3923"
echo "   FTP:           ftp://localhost:3990"
echo ""
echo "📂 Storage Paths:"
echo "   Uploads:       http://localhost:3923/uploads/"
echo "   Recordings:    http://localhost:3923/recordings/"
echo "   Exports:       http://localhost:3923/exports/"
echo ""
echo -e "${GREEN}✅ Copyparty is starting...${NC}"
echo "   Press Ctrl+C to stop"
echo ""

# Run Copyparty with our configuration
if [ -f "copyparty.py" ]; then
    python3 copyparty.py \
        -i :: -p 3923 \
        --rw ./uploads,./recordings,./exports \
        --dbpath ./copyparty-db \
        --no-robots \
        --cors \
        --js-browser \
        --theme 2 \
        --name "Combat Mirror File Server"
else
    # Use system-installed copyparty if available
    copyparty \
        -i :: -p 3923 \
        --rw ./uploads,./recordings,./exports \
        --db ./copyparty-db \
        --no-robots \
        --cors \
        --js-browser \
        --theme 2 \
        --name "Combat Mirror File Server"
fi
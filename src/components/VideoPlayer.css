/* Video Player Modal Styles */
.video-player-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.95);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2000;
  outline: none;
}

.video-player-modal.fullscreen {
  background: #000;
}

.video-player-container {
  position: relative;
  width: 90vw;
  height: 90vh;
  max-width: 1200px;
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: none;
}

.video-player-modal.fullscreen .video-player-container {
  width: 100vw;
  height: 100vh;
  max-width: none;
}

.video-element {
  width: 100%;
  height: 100%;
  object-fit: contain;
  background: #000;
  cursor: pointer;
}

/* Loading */
.video-loading {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.8);
  color: white;
}

.loading-spinner {
  text-align: center;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(255, 255, 255, 0.3);
  border-top: 4px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

/* Controls Overlay */
.video-controls-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.7) 0%,
    transparent 20%,
    transparent 80%,
    rgba(0, 0, 0, 0.8) 100%
  );
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  opacity: 0;
  transition: opacity 0.3s ease;
  pointer-events: none;
}

.video-controls-overlay.visible {
  opacity: 1;
  pointer-events: all;
}

/* Top Bar */
.video-top-bar {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 20px;
  color: white;
}

.video-title h3 {
  margin: 0 0 5px 0;
  font-size: 18px;
  font-weight: 600;
  max-width: 600px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-meta {
  display: flex;
  gap: 15px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.video-meta .category {
  background: #007AFF;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.video-actions {
  display: flex;
  gap: 10px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: all 0.2s;
  color: white;
}

.analyze-btn {
  background: #007AFF;
}

.analyze-btn:hover {
  background: #0051D0;
}

.download-btn {
  background: #34C759;
}

.download-btn:hover {
  background: #28A745;
}

.close-btn {
  background: rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

/* Center Play Button */
.center-play-button {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  cursor: pointer;
  transition: all 0.3s ease;
}

.center-play-button:hover {
  transform: translate(-50%, -50%) scale(1.1);
}

.play-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: #007AFF;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  backdrop-filter: blur(10px);
}

/* Bottom Controls */
.video-bottom-controls {
  padding: 20px;
  color: white;
}

.progress-container {
  margin-bottom: 15px;
}

.progress-bar {
  width: 100%;
  height: 6px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 3px;
  appearance: none;
  cursor: pointer;
  margin-bottom: 8px;
}

.progress-bar::-webkit-slider-thumb {
  appearance: none;
  width: 16px;
  height: 16px;
  background: #007AFF;
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.progress-bar::-moz-range-thumb {
  width: 16px;
  height: 16px;
  background: #007AFF;
  border-radius: 50%;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.time-display {
  display: flex;
  justify-content: center;
  gap: 5px;
  font-size: 14px;
  color: rgba(255, 255, 255, 0.8);
}

.control-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-controls,
.right-controls {
  display: flex;
  align-items: center;
  gap: 15px;
}

.control-btn {
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  padding: 10px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.control-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.05);
}

.play-pause-btn {
  background: #007AFF;
  padding: 12px 16px;
  border-radius: 8px;
}

.play-pause-btn:hover {
  background: #0051D0;
}

.volume-control {
  display: flex;
  align-items: center;
  gap: 8px;
}

.volume-slider {
  width: 80px;
  height: 4px;
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
  appearance: none;
  cursor: pointer;
}

.volume-slider::-webkit-slider-thumb {
  appearance: none;
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
}

.volume-slider::-moz-range-thumb {
  width: 12px;
  height: 12px;
  background: white;
  border-radius: 50%;
  cursor: pointer;
  border: none;
}

.rate-select {
  background: rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 6px 10px;
  border-radius: 4px;
  font-size: 14px;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.rate-select option {
  background: #333;
  color: white;
}

/* Keyboard Shortcuts */
.keyboard-shortcuts {
  position: absolute;
  bottom: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 12px;
  opacity: 0;
  transition: opacity 0.3s;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.video-player-container:hover .keyboard-shortcuts {
  opacity: 1;
}

.shortcuts-content {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.shortcuts-content strong {
  margin-bottom: 5px;
  color: #007AFF;
}

.shortcuts-content span {
  font-family: monospace;
  font-size: 11px;
  color: rgba(255, 255, 255, 0.8);
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-player-container {
    width: 100vw;
    height: 100vh;
  }

  .video-top-bar {
    padding: 15px;
    flex-direction: column;
    gap: 15px;
  }

  .video-title h3 {
    font-size: 16px;
  }

  .video-actions {
    align-self: stretch;
    justify-content: space-between;
  }

  .action-btn {
    flex: 1;
    justify-content: center;
    padding: 10px;
  }

  .video-bottom-controls {
    padding: 15px;
  }

  .control-buttons {
    flex-direction: column;
    gap: 15px;
  }

  .left-controls,
  .right-controls {
    justify-content: center;
    flex-wrap: wrap;
  }

  .volume-control {
    order: 1;
  }

  .keyboard-shortcuts {
    display: none;
  }

  .center-play-button .play-icon {
    width: 60px;
    height: 60px;
    font-size: 24px;
  }
}

@media (max-width: 480px) {
  .video-top-bar {
    padding: 10px;
  }

  .video-bottom-controls {
    padding: 10px;
  }

  .control-btn {
    padding: 8px;
    font-size: 14px;
  }

  .volume-slider {
    width: 60px;
  }
}

/* Fullscreen styles */
.video-player-modal.fullscreen .video-controls-overlay {
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.6) 0%,
    transparent 15%,
    transparent 85%,
    rgba(0, 0, 0, 0.8) 100%
  );
}

.video-player-modal.fullscreen .center-play-button .play-icon {
  width: 100px;
  height: 100px;
  font-size: 36px;
}

/* Custom scrollbar for controls */
.video-controls-overlay::-webkit-scrollbar {
  display: none;
}

.video-controls-overlay {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
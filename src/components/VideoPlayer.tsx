import React, { useState, useRef, useEffect } from 'react';
import './VideoPlayer.css';

interface VideoFile {
  name: string;
  size: number;
  modified: string;
  url: string;
  category: string;
}

interface VideoPlayerProps {
  video: VideoFile | null;
  onClose: () => void;
  onAnalyze?: (video: VideoFile) => void;
}

export default function VideoPlayer({ video, onClose, onAnalyze }: VideoPlayerProps) {
  const [isPlaying, setIsPlaying] = useState(false);
  const [currentTime, setCurrentTime] = useState(0);
  const [duration, setDuration] = useState(0);
  const [volume, setVolume] = useState(1);
  const [playbackRate, setPlaybackRate] = useState(1);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [showControls, setShowControls] = useState(true);
  
  const videoRef = useRef<HTMLVideoElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const controlsTimeoutRef = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!video) return;

    const videoElement = videoRef.current;
    if (!videoElement) return;

    // Reset video state when video changes
    setIsPlaying(false);
    setCurrentTime(0);
    setPlaybackRate(1);
    
    videoElement.load();
  }, [video]);

  useEffect(() => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const handleTimeUpdate = () => setCurrentTime(videoElement.currentTime);
    const handleDurationChange = () => setDuration(videoElement.duration);
    const handlePlay = () => setIsPlaying(true);
    const handlePause = () => setIsPlaying(false);
    const handleEnded = () => setIsPlaying(false);

    videoElement.addEventListener('timeupdate', handleTimeUpdate);
    videoElement.addEventListener('durationchange', handleDurationChange);
    videoElement.addEventListener('play', handlePlay);
    videoElement.addEventListener('pause', handlePause);
    videoElement.addEventListener('ended', handleEnded);

    return () => {
      videoElement.removeEventListener('timeupdate', handleTimeUpdate);
      videoElement.removeEventListener('durationchange', handleDurationChange);
      videoElement.removeEventListener('play', handlePlay);
      videoElement.removeEventListener('pause', handlePause);
      videoElement.removeEventListener('ended', handleEnded);
    };
  }, [video]);

  const togglePlay = () => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    if (isPlaying) {
      videoElement.pause();
    } else {
      videoElement.play();
    }
  };

  const handleSeek = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const newTime = parseFloat(e.target.value);
    videoElement.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleVolumeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const newVolume = parseFloat(e.target.value);
    videoElement.volume = newVolume;
    setVolume(newVolume);
  };

  const handlePlaybackRateChange = (rate: number) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    videoElement.playbackRate = rate;
    setPlaybackRate(rate);
  };

  const toggleFullscreen = () => {
    const container = containerRef.current;
    if (!container) return;

    if (!isFullscreen) {
      if (container.requestFullscreen) {
        container.requestFullscreen();
      }
    } else {
      if (document.exitFullscreen) {
        document.exitFullscreen();
      }
    }
  };

  const formatTime = (seconds: number): string => {
    if (isNaN(seconds)) return '0:00';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);

    if (hours > 0) {
      return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
    }
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  };

  const handleMouseMove = () => {
    setShowControls(true);
    
    if (controlsTimeoutRef.current) {
      clearTimeout(controlsTimeoutRef.current);
    }
    
    controlsTimeoutRef.current = setTimeout(() => {
      if (isPlaying) {
        setShowControls(false);
      }
    }, 3000);
  };

  const skipTime = (seconds: number) => {
    const videoElement = videoRef.current;
    if (!videoElement) return;

    const newTime = Math.max(0, Math.min(duration, currentTime + seconds));
    videoElement.currentTime = newTime;
    setCurrentTime(newTime);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    switch (e.key) {
      case ' ':
        e.preventDefault();
        togglePlay();
        break;
      case 'ArrowLeft':
        e.preventDefault();
        skipTime(-10);
        break;
      case 'ArrowRight':
        e.preventDefault();
        skipTime(10);
        break;
      case 'f':
        e.preventDefault();
        toggleFullscreen();
        break;
      case 'Escape':
        onClose();
        break;
    }
  };

  if (!video) return null;

  return (
    <div 
      className={`video-player-modal ${isFullscreen ? 'fullscreen' : ''}`}
      onKeyDown={handleKeyPress}
      tabIndex={0}
    >
      <div 
        ref={containerRef}
        className="video-player-container"
        onMouseMove={handleMouseMove}
        onMouseLeave={() => isPlaying && setShowControls(false)}
      >
        {/* Video Element */}
        <video
          ref={videoRef}
          className="video-element"
          src={video.url}
          poster={video.url + '#t=1'}
          onClick={togglePlay}
          onDoubleClick={toggleFullscreen}
        />

        {/* Loading Overlay */}
        {!duration && (
          <div className="video-loading">
            <div className="loading-spinner">
              <div className="spinner"></div>
              <p>Loading video...</p>
            </div>
          </div>
        )}

        {/* Controls Overlay */}
        <div className={`video-controls-overlay ${showControls ? 'visible' : ''}`}>
          {/* Top Bar */}
          <div className="video-top-bar">
            <div className="video-title">
              <h3>{video.name}</h3>
              <div className="video-meta">
                <span className="category">{video.category}</span>
                <span className="date">{new Date(video.modified).toLocaleDateString()}</span>
              </div>
            </div>
            
            <div className="video-actions">
              {onAnalyze && (
                <button 
                  className="action-btn analyze-btn"
                  onClick={() => onAnalyze(video)}
                  title="Analyze Video"
                >
                  🔍 Analyze
                </button>
              )}
              
              <a 
                href={video.url} 
                download={video.name}
                className="action-btn download-btn"
                title="Download Video"
              >
                ⬇ Download
              </a>
              
              <button 
                className="action-btn close-btn"
                onClick={onClose}
                title="Close (Esc)"
              >
                ✕
              </button>
            </div>
          </div>

          {/* Center Play Button */}
          {!isPlaying && (
            <div className="center-play-button" onClick={togglePlay}>
              <div className="play-icon">▶</div>
            </div>
          )}

          {/* Bottom Controls */}
          <div className="video-bottom-controls">
            {/* Progress Bar */}
            <div className="progress-container">
              <input
                type="range"
                className="progress-bar"
                min="0"
                max={duration || 0}
                value={currentTime}
                onChange={handleSeek}
              />
              <div className="time-display">
                <span>{formatTime(currentTime)}</span>
                <span>/</span>
                <span>{formatTime(duration)}</span>
              </div>
            </div>

            {/* Control Buttons */}
            <div className="control-buttons">
              <div className="left-controls">
                <button 
                  className="control-btn play-pause-btn"
                  onClick={togglePlay}
                  title={isPlaying ? 'Pause (Space)' : 'Play (Space)'}
                >
                  {isPlaying ? '⏸' : '▶'}
                </button>

                <button 
                  className="control-btn skip-btn"
                  onClick={() => skipTime(-10)}
                  title="Rewind 10s (←)"
                >
                  ⏪
                </button>

                <button 
                  className="control-btn skip-btn"
                  onClick={() => skipTime(10)}
                  title="Forward 10s (→)"
                >
                  ⏩
                </button>

                <div className="volume-control">
                  <button className="control-btn volume-btn">
                    {volume === 0 ? '🔇' : volume < 0.5 ? '🔉' : '🔊'}
                  </button>
                  <input
                    type="range"
                    className="volume-slider"
                    min="0"
                    max="1"
                    step="0.1"
                    value={volume}
                    onChange={handleVolumeChange}
                  />
                </div>
              </div>

              <div className="right-controls">
                <div className="playback-rate">
                  <select 
                    value={playbackRate}
                    onChange={(e) => handlePlaybackRateChange(parseFloat(e.target.value))}
                    className="rate-select"
                  >
                    <option value={0.25}>0.25x</option>
                    <option value={0.5}>0.5x</option>
                    <option value={0.75}>0.75x</option>
                    <option value={1}>1x</option>
                    <option value={1.25}>1.25x</option>
                    <option value={1.5}>1.5x</option>
                    <option value={2}>2x</option>
                  </select>
                </div>

                <button 
                  className="control-btn fullscreen-btn"
                  onClick={toggleFullscreen}
                  title="Fullscreen (f)"
                >
                  {isFullscreen ? '⛶' : '⛶'}
                </button>
              </div>
            </div>
          </div>
        </div>

        {/* Keyboard Shortcuts Help */}
        <div className="keyboard-shortcuts">
          <div className="shortcuts-content">
            <strong>Keyboard Shortcuts:</strong>
            <span>Space: Play/Pause</span>
            <span>← →: Skip 10s</span>
            <span>F: Fullscreen</span>
            <span>Esc: Close</span>
          </div>
        </div>
      </div>
    </div>
  );
}
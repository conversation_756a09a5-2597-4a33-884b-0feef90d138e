import React, { useState, useEffect } from 'react';
import './VideoLibrary.css';

interface VideoFile {
  name: string;
  size: number;
  modified: string;
  url: string;
  category: string;
}

interface VideoLibraryProps {
  onVideoSelect?: (video: VideoFile) => void;
  onAnalyzeVideo?: (video: VideoFile) => void;
}

export default function VideoLibrary({ onVideoSelect, onAnalyzeVideo }: VideoLibraryProps) {
  const [videos, setVideos] = useState<VideoFile[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCategory, setSelectedCategory] = useState<'all' | 'uploads' | 'recordings' | 'exports'>('all');
  const [selectedVideo, setSelectedVideo] = useState<VideoFile | null>(null);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [sortBy, setSortBy] = useState<'name' | 'date' | 'size'>('date');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');

  useEffect(() => {
    loadVideoLibrary();
  }, [selectedCategory]);

  const loadVideoLibrary = async () => {
    setLoading(true);
    setError(null);

    try {
      const categories = selectedCategory === 'all' ? ['uploads', 'recordings', 'exports'] : [selectedCategory];
      const allVideos: VideoFile[] = [];

      for (const category of categories) {
        const response = await fetch(`/api/files/list/${category}`);
        if (!response.ok) {
          throw new Error(`Failed to fetch ${category} videos`);
        }
        const data = await response.json();
        const categoryVideos = data.files
          .filter((file: any) => isVideoFile(file.name))
          .map((file: any) => ({
            ...file,
            category: category
          }));
        allVideos.push(...categoryVideos);
      }

      setVideos(sortVideos(allVideos, sortBy, sortOrder));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load video library');
    } finally {
      setLoading(false);
    }
  };

  const isVideoFile = (filename: string): boolean => {
    const videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    return videoExtensions.some(ext => filename.toLowerCase().endsWith(ext));
  };

  const sortVideos = (videos: VideoFile[], sortBy: string, order: string): VideoFile[] => {
    return [...videos].sort((a, b) => {
      let comparison = 0;
      
      switch (sortBy) {
        case 'name':
          comparison = a.name.localeCompare(b.name);
          break;
        case 'date':
          comparison = new Date(a.modified).getTime() - new Date(b.modified).getTime();
          break;
        case 'size':
          comparison = a.size - b.size;
          break;
      }
      
      return order === 'asc' ? comparison : -comparison;
    });
  };

  const handleSort = (newSortBy: 'name' | 'date' | 'size') => {
    if (sortBy === newSortBy) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortOrder('desc');
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string): string => {
    return new Date(dateString).toLocaleString();
  };

  const getCategoryColor = (category: string): string => {
    switch (category) {
      case 'uploads': return '#007AFF';
      case 'recordings': return '#FF3B30';
      case 'exports': return '#34C759';
      default: return '#8E8E93';
    }
  };

  const handleVideoClick = (video: VideoFile) => {
    setSelectedVideo(video);
    onVideoSelect?.(video);
  };

  const handleAnalyze = (video: VideoFile) => {
    onAnalyzeVideo?.(video);
  };

  const handleDelete = async (video: VideoFile) => {
    try {
      const response = await fetch(`/api/files/${video.category}/${video.name}`, {
        method: 'DELETE'
      });

      if (response.ok) {
        setVideos(videos.filter(v => v.name !== video.name || v.category !== video.category));
        setShowDeleteModal(false);
        setSelectedVideo(null);
      } else {
        setError('Failed to delete video');
      }
    } catch (err) {
      setError('Failed to delete video');
    }
  };

  const generateThumbnail = (videoUrl: string): string => {
    // In a real implementation, you'd generate actual thumbnails
    // For now, return a placeholder or video poster
    return videoUrl + '#t=1'; // Shows frame at 1 second
  };

  if (loading) {
    return (
      <div className="video-library loading">
        <div className="loading-spinner">
          <div className="spinner"></div>
          <p>Loading video library...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="video-library error">
        <div className="error-message">
          <h3>Error Loading Videos</h3>
          <p>{error}</p>
          <button onClick={loadVideoLibrary} className="retry-button">
            Retry
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="video-library">
      {/* Header */}
      <div className="library-header">
        <h2>Video Library</h2>
        <div className="library-stats">
          <span>{videos.length} videos</span>
          <span>•</span>
          <span>{formatFileSize(videos.reduce((sum, video) => sum + video.size, 0))} total</span>
        </div>
      </div>

      {/* Filters and Controls */}
      <div className="library-controls">
        <div className="category-filters">
          <button 
            className={selectedCategory === 'all' ? 'active' : ''}
            onClick={() => setSelectedCategory('all')}
          >
            All Videos
          </button>
          <button 
            className={selectedCategory === 'uploads' ? 'active' : ''}
            onClick={() => setSelectedCategory('uploads')}
          >
            Uploads
          </button>
          <button 
            className={selectedCategory === 'recordings' ? 'active' : ''}
            onClick={() => setSelectedCategory('recordings')}
          >
            Recordings
          </button>
          <button 
            className={selectedCategory === 'exports' ? 'active' : ''}
            onClick={() => setSelectedCategory('exports')}
          >
            Exports
          </button>
        </div>

        <div className="sort-controls">
          <label>Sort by:</label>
          <button 
            className={sortBy === 'date' ? 'active' : ''}
            onClick={() => handleSort('date')}
          >
            Date {sortBy === 'date' && (sortOrder === 'desc' ? '↓' : '↑')}
          </button>
          <button 
            className={sortBy === 'name' ? 'active' : ''}
            onClick={() => handleSort('name')}
          >
            Name {sortBy === 'name' && (sortOrder === 'desc' ? '↓' : '↑')}
          </button>
          <button 
            className={sortBy === 'size' ? 'active' : ''}
            onClick={() => handleSort('size')}
          >
            Size {sortBy === 'size' && (sortOrder === 'desc' ? '↓' : '↑')}
          </button>
        </div>
      </div>

      {/* Video Grid */}
      {videos.length === 0 ? (
        <div className="empty-state">
          <div className="empty-icon">📹</div>
          <h3>No Videos Found</h3>
          <p>Upload videos from the mobile app to see them here.</p>
        </div>
      ) : (
        <div className="video-grid">
          {videos.map((video, index) => (
            <div 
              key={`${video.category}-${video.name}-${index}`}
              className={`video-card ${selectedVideo?.name === video.name ? 'selected' : ''}`}
              onClick={() => handleVideoClick(video)}
            >
              <div className="video-thumbnail">
                <video 
                  poster={generateThumbnail(video.url)}
                  preload="metadata"
                >
                  <source src={video.url} type="video/mp4" />
                </video>
                <div className="video-overlay">
                  <button 
                    className="play-button"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleVideoClick(video);
                    }}
                  >
                    ▶
                  </button>
                </div>
              </div>

              <div className="video-info">
                <div className="video-title" title={video.name}>
                  {video.name}
                </div>
                
                <div className="video-meta">
                  <span 
                    className="category-badge"
                    style={{ backgroundColor: getCategoryColor(video.category) }}
                  >
                    {video.category}
                  </span>
                  <span className="file-size">{formatFileSize(video.size)}</span>
                </div>

                <div className="video-date">
                  {formatDate(video.modified)}
                </div>

                <div className="video-actions">
                  <button 
                    className="action-button analyze"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleAnalyze(video);
                    }}
                    title="Analyze Video"
                  >
                    🔍 Analyze
                  </button>
                  
                  <a 
                    href={video.url} 
                    download={video.name}
                    className="action-button download"
                    onClick={(e) => e.stopPropagation()}
                    title="Download Video"
                  >
                    ⬇ Download
                  </a>
                  
                  <button 
                    className="action-button delete"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedVideo(video);
                      setShowDeleteModal(true);
                    }}
                    title="Delete Video"
                  >
                    🗑 Delete
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Delete Confirmation Modal */}
      {showDeleteModal && selectedVideo && (
        <div className="modal-overlay">
          <div className="modal">
            <h3>Delete Video</h3>
            <p>Are you sure you want to delete "{selectedVideo.name}"?</p>
            <p>This action cannot be undone.</p>
            <div className="modal-actions">
              <button 
                className="cancel-button"
                onClick={() => {
                  setShowDeleteModal(false);
                  setSelectedVideo(null);
                }}
              >
                Cancel
              </button>
              <button 
                className="delete-button"
                onClick={() => handleDelete(selectedVideo)}
              >
                Delete
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
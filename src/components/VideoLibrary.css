/* Video Library Styles */
.video-library {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-spinner {
  text-align: center;
  color: #666;
}

.spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.error-message {
  text-align: center;
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-width: 400px;
}

.error-message h3 {
  color: #FF3B30;
  margin-bottom: 10px;
}

.retry-button {
  background: #007AFF;
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  margin-top: 20px;
  transition: background 0.2s;
}

.retry-button:hover {
  background: #0051D0;
}

/* Header */
.library-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 2px solid #e9ecef;
}

.library-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 28px;
  font-weight: 700;
}

.library-stats {
  color: #6c757d;
  font-size: 16px;
  font-weight: 500;
}

.library-stats span {
  margin: 0 5px;
}

/* Controls */
.library-controls {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  flex-wrap: wrap;
  gap: 20px;
}

.category-filters {
  display: flex;
  gap: 8px;
}

.category-filters button {
  padding: 10px 20px;
  border: 2px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 25px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.category-filters button:hover {
  border-color: #007AFF;
  color: #007AFF;
}

.category-filters button.active {
  background: #007AFF;
  border-color: #007AFF;
  color: white;
}

.sort-controls {
  display: flex;
  align-items: center;
  gap: 10px;
}

.sort-controls label {
  font-weight: 600;
  color: #495057;
}

.sort-controls button {
  padding: 8px 16px;
  border: 1px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.sort-controls button:hover {
  border-color: #007AFF;
  color: #007AFF;
}

.sort-controls button.active {
  background: #007AFF;
  border-color: #007AFF;
  color: white;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 80px 20px;
  color: #6c757d;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 20px;
  opacity: 0.5;
}

.empty-state h3 {
  margin-bottom: 10px;
  color: #495057;
}

/* Video Grid */
.video-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  margin-top: 20px;
}

.video-card {
  background: white;
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s;
  cursor: pointer;
  border: 2px solid transparent;
}

.video-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.video-card.selected {
  border-color: #007AFF;
  box-shadow: 0 8px 25px rgba(0, 122, 255, 0.2);
}

.video-thumbnail {
  position: relative;
  width: 100%;
  height: 180px;
  background: #000;
  overflow: hidden;
}

.video-thumbnail video {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.video-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
  display: flex;
  justify-content: center;
  align-items: center;
  opacity: 0;
  transition: opacity 0.3s;
}

.video-card:hover .video-overlay {
  opacity: 1;
}

.play-button {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.9);
  border: none;
  font-size: 20px;
  color: #007AFF;
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button:hover {
  background: white;
  transform: scale(1.1);
}

.video-info {
  padding: 16px;
}

.video-title {
  font-weight: 600;
  font-size: 16px;
  color: #2c3e50;
  margin-bottom: 8px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.video-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.category-badge {
  padding: 4px 8px;
  border-radius: 12px;
  color: white;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.file-size {
  color: #6c757d;
  font-size: 14px;
}

.video-date {
  color: #6c757d;
  font-size: 14px;
  margin-bottom: 12px;
}

.video-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.action-button {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  cursor: pointer;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 4px;
  transition: all 0.2s;
}

.action-button.analyze {
  background: #007AFF;
  color: white;
}

.action-button.analyze:hover {
  background: #0051D0;
}

.action-button.download {
  background: #34C759;
  color: white;
}

.action-button.download:hover {
  background: #28A745;
}

.action-button.delete {
  background: #FF3B30;
  color: white;
}

.action-button.delete:hover {
  background: #DC2626;
}

/* Modal */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.modal {
  background: white;
  padding: 30px;
  border-radius: 12px;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.2);
}

.modal h3 {
  margin-bottom: 15px;
  color: #2c3e50;
}

.modal p {
  margin-bottom: 10px;
  color: #6c757d;
}

.modal-actions {
  display: flex;
  gap: 12px;
  justify-content: center;
  margin-top: 20px;
}

.cancel-button {
  padding: 12px 24px;
  border: 2px solid #dee2e6;
  background: white;
  color: #495057;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: all 0.2s;
}

.cancel-button:hover {
  border-color: #6c757d;
  color: #6c757d;
}

.delete-button {
  padding: 12px 24px;
  border: none;
  background: #FF3B30;
  color: white;
  border-radius: 8px;
  cursor: pointer;
  font-weight: 600;
  transition: background 0.2s;
}

.delete-button:hover {
  background: #DC2626;
}

/* Responsive Design */
@media (max-width: 768px) {
  .video-library {
    padding: 15px;
  }
  
  .library-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }
  
  .library-controls {
    flex-direction: column;
    align-items: stretch;
  }
  
  .video-grid {
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 15px;
  }
  
  .category-filters {
    justify-content: center;
    flex-wrap: wrap;
  }
  
  .sort-controls {
    justify-content: center;
    flex-wrap: wrap;
  }
}
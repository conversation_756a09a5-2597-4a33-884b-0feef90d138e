# Combat Mirror System - Multi-Device Calibration Protocol

## Overview
This protocol establishes standardized calibration procedures for iPad Pro, iPhone 15 Pro Max, and MacBook Pro devices to ensure consistent motion tracking and spatial alignment across all connected devices.

## Device Specifications & Capabilities

### iPad Pro (12.9-inch, 6th Gen)
- **Camera**: 12MP Ultra Wide (122° FOV), 12MP Wide
- **Resolution**: 2732 x 2048 pixels
- **Frame Rate**: 60 FPS max
- **Aspect Ratio**: 4:3
- **Calibration Focus**: Wide-angle distortion correction, gyroscope integration

### iPhone 15 Pro Max
- **Camera**: 48MP Main, 12MP Ultra Wide (120° FOV), 12MP Telephoto
- **Resolution**: 2796 x 1290 pixels
- **Frame Rate**: 60 FPS max, 120 FPS with ProMotion
- **Aspect Ratio**: 19.5:9
- **Calibration Focus**: Telephoto zoom levels, stabilization compensation

### MacBook Pro (14-inch, M3)
- **Camera**: 1080p FaceTime HD
- **Resolution**: 1920 x 1080 pixels
- **Frame Rate**: 30 FPS max
- **Aspect Ratio**: 16:9
- **Calibration Focus**: Fixed position calibration, lighting compensation

## Phase 1: USB Connection & Device Detection

### 1.1 Device Connection Setup
```bash
# Check connected devices
system_profiler SPUSBDataType | grep -A 10 -B 5 "iPad\|iPhone\|MacBook"

# Verify device recognition
ios-deploy -c
```

### 1.2 Device Registration Protocol
```typescript
interface DeviceRegistration {
  deviceId: string;
  deviceType: 'ipad-pro' | 'iphone-15-pro-max' | 'macbook-pro';
  serialNumber: string;
  cameraSpecs: CameraSpecs;
  calibrationStatus: 'pending' | 'in-progress' | 'completed';
  connectionMethod: 'usb' | 'wifi' | 'qr-code';
  position: CameraPosition;
}

class DeviceRegistry {
  async registerDevice(device: DeviceRegistration): Promise<string> {
    const deviceId = this.generateDeviceId(device);
    await this.storeCalibrationData(deviceId, {
      intrinsicMatrix: null,
      distortionCoefficients: null,
      positionMatrix: null,
      timestamp: Date.now()
    });
    return deviceId;
  }
}
```

## Phase 2: Intrinsic Camera Calibration

### 2.1 Calibration Pattern Setup
```typescript
interface CalibrationPattern {
  type: 'checkerboard' | 'charuco' | 'circular';
  size: { width: number; height: number };
  squareSize: number; // in mm
  pattern: number[][];
}

const CALIBRATION_PATTERNS = {
  checkerboard: {
    size: { width: 9, height: 6 },
    squareSize: 25, // 25mm squares
    pattern: generateCheckerboardPattern(9, 6)
  },
  charuco: {
    size: { width: 5, height: 7 },
    squareSize: 30,
    pattern: generateCharucoPattern(5, 7)
  }
};
```

### 2.2 Camera Matrix Calculation
```typescript
class CameraCalibrator {
  async calibrateIntrinsic(deviceId: string): Promise<CalibrationResult> {
    const images = await this.captureCalibrationImages(deviceId);
    const corners = await this.findCorners(images);
    
    const result = await this.solvePnP(corners, CALIBRATION_PATTERNS.checkerboard);
    
    return {
      cameraMatrix: result.cameraMatrix,
      distortionCoefficients: result.distCoeffs,
      reprojectionError: result.rms,
      calibrationDate: new Date().toISOString()
    };
  }
  
  private async captureCalibrationImages(deviceId: string): Promise<ImageData[]> {
    const positions = [
      'front', 'left', 'right', 'top', 'bottom', 'tilted-left', 'tilted-right'
    ];
    
    const images: ImageData[] = [];
    for (const position of positions) {
      const image = await this.captureImage(deviceId, position);
      images.push(image);
    }
    
    return images;
  }
}
```

## Phase 3: Multi-Device Spatial Calibration

### 3.1 Coordinate System Definition
```typescript
interface CoordinateSystem {
  origin: { x: number; y: number; z: number };
  axes: {
    x: { x: number; y: number; z: number };
    y: { x: number; y: number; z: number };
    z: { x: number; y: number; z: number };
  };
  scale: number; // mm per unit
}

class SpatialCalibrator {
  private worldOrigin: CoordinateSystem = {
    origin: { x: 0, y: 0, z: 0 },
    axes: {
      x: { x: 1, y: 0, z: 0 },
      y: { x: 0, y: 1, z: 0 },
      z: { x: 0, y: 0, z: 1 }
    },
    scale: 1.0
  };
  
  async calibrateSpatialRelationship(
    devices: DeviceRegistration[]
  ): Promise<SpatialCalibration> {
    const calibrationPoints = this.generateCalibrationPoints();
    const transformations = new Map<string, TransformationMatrix>();
    
    for (const device of devices) {
      const devicePoints = await this.captureCalibrationPoints(device.deviceId);
      const transform = await this.calculateTransformation(
        calibrationPoints,
        devicePoints
      );
      transformations.set(device.deviceId, transform);
    }
    
    return {
      worldOrigin: this.worldOrigin,
      deviceTransforms: transformations,
      calibrationAccuracy: await this.validateCalibration(transformations)
    };
  }
}
```

### 3.2 Device Position Mapping
```typescript
interface DevicePosition {
  deviceId: string;
  position: CameraPosition;
  transform: TransformationMatrix;
  coverageArea: {
    min: { x: number; y: number; z: number };
    max: { x: number; y: number; z: number };
  };
}

const POSITION_MAPPING = {
  FRONT: { x: 0, y: 0, z: 2000 }, // 2m in front
  LEFT: { x: -1500, y: 0, z: 1000 }, // 1.5m left, 1m front
  RIGHT: { x: 1500, y: 0, z: 1000 }, // 1.5m right, 1m front
  BACK: { x: 0, y: 0, z: -1000 } // 1m behind
};
```

## Phase 4: QR Code Matching System

### 4.1 QR Code Generation
```typescript
interface QRCodeData {
  deviceId: string;
  position: CameraPosition;
  calibrationData: {
    intrinsic: CalibrationResult;
    extrinsic: TransformationMatrix;
  };
  timestamp: number;
  sessionId: string;
}

class QRCodeGenerator {
  generateCalibrationQR(device: DeviceRegistration): string {
    const qrData: QRCodeData = {
      deviceId: device.deviceId,
      position: device.position,
      calibrationData: {
        intrinsic: this.getIntrinsicCalibration(device.deviceId),
        extrinsic: this.getExtrinsicCalibration(device.deviceId)
      },
      timestamp: Date.now(),
      sessionId: this.generateSessionId()
    };
    
    return this.encodeQRData(qrData);
  }
  
  private encodeQRData(data: QRCodeData): string {
    return btoa(JSON.stringify(data));
  }
}
```

### 4.2 QR Code Detection & Matching
```typescript
class QRCodeMatcher {
  async matchQRCode(imageData: ImageData): Promise<QRMatchResult> {
    const qrData = await this.decodeQRCode(imageData);
    const device = await this.validateDevice(qrData.deviceId);
    
    if (!device) {
      throw new Error('Invalid device ID in QR code');
    }
    
    const match = await this.calculateMatchConfidence(qrData, device);
    
    return {
      deviceId: device.deviceId,
      position: device.position,
      confidence: match.confidence,
      calibrationData: match.calibrationData,
      timestamp: Date.now()
    };
  }
  
  private async calculateMatchConfidence(
    qrData: QRCodeData,
    device: DeviceRegistration
  ): Promise<MatchConfidence> {
    const timeDiff = Math.abs(qrData.timestamp - Date.now());
    const calibrationDiff = this.compareCalibrationData(
      qrData.calibrationData,
      device.calibrationData
    );
    
    return {
      confidence: Math.max(0, 1 - (timeDiff / 300000)), // 5 minute window
      calibrationData: calibrationDiff
    };
  }
}
```

## Phase 5: Motion Tracking Calibration

### 5.1 Motion Model Calibration
```typescript
interface MotionCalibration {
  deviceId: string;
  motionModel: {
    accelerationBias: { x: number; y: number; z: number };
    gyroscopeBias: { x: number; y: number; z: number };
    magnetometerBias: { x: number; y: number; z: number };
  };
  trackingParameters: {
    minDisplacement: number;
    maxDisplacement: number;
    smoothingFactor: number;
    predictionWindow: number;
  };
}

class MotionCalibrator {
  async calibrateMotion(deviceId: string): Promise<MotionCalibration> {
    const motionData = await this.collectMotionData(deviceId);
    const biases = await this.calculateBiases(motionData);
    const trackingParams = await this.optimizeTrackingParameters(motionData);
    
    return {
      deviceId,
      motionModel: biases,
      trackingParameters: trackingParams
    };
  }
  
  private async collectMotionData(deviceId: string): Promise<MotionData> {
    const duration = 30000; // 30 seconds
    const samples = await this.recordMotion(deviceId, duration);
    
    return {
      accelerometer: samples.map(s => s.accelerometer),
      gyroscope: samples.map(s => s.gyroscope),
      magnetometer: samples.map(s => s.magnetometer),
      timestamps: samples.map(s => s.timestamp)
    };
  }
}
```

## Phase 6: Validation & Testing

### 6.1 Calibration Validation
```typescript
interface ValidationResult {
  deviceId: string;
  intrinsicAccuracy: number;
  spatialAccuracy: number;
  motionAccuracy: number;
  overallScore: number;
  recommendations: string[];
}

class CalibrationValidator {
  async validateCalibration(deviceId: string): Promise<ValidationResult> {
    const intrinsicTest = await this.testIntrinsicCalibration(deviceId);
    const spatialTest = await this.testSpatialCalibration(deviceId);
    const motionTest = await this.testMotionCalibration(deviceId);
    
    const overallScore = (intrinsicTest.accuracy + spatialTest.accuracy + motionTest.accuracy) / 3;
    
    return {
      deviceId,
      intrinsicAccuracy: intrinsicTest.accuracy,
      spatialAccuracy: spatialTest.accuracy,
      motionAccuracy: motionTest.accuracy,
      overallScore,
      recommendations: this.generateRecommendations(intrinsicTest, spatialTest, motionTest)
    };
  }
  
  private async testIntrinsicCalibration(deviceId: string): Promise<TestResult> {
    const testImages = await this.captureTestImages(deviceId);
    const reprojectionErrors = await this.calculateReprojectionErrors(testImages);
    
    return {
      accuracy: 1 - (Math.mean(reprojectionErrors) / 2.0), // Normalize to 0-1
      details: reprojectionErrors
    };
  }
}
```

## Phase 7: Automated Calibration Pipeline

### 7.1 Calibration Workflow
```typescript
class CalibrationOrchestrator {
  async runFullCalibration(devices: DeviceRegistration[]): Promise<CalibrationReport> {
    const report: CalibrationReport = {
      devices: [],
      timestamp: new Date().toISOString(),
      status: 'in-progress'
    };
    
    // Phase 1: Device Detection
    const detectedDevices = await this.detectDevices();
    
    // Phase 2: Intrinsic Calibration
    for (const device of detectedDevices) {
      const intrinsic = await this.calibrateIntrinsic(device.deviceId);
      report.devices.push({
        deviceId: device.deviceId,
        intrinsicCalibration: intrinsic,
        status: 'intrinsic-completed'
      });
    }
    
    // Phase 3: Spatial Calibration
    const spatial = await this.calibrateSpatial(detectedDevices);
    
    // Phase 4: Motion Calibration
    for (const device of detectedDevices) {
      const motion = await this.calibrateMotion(device.deviceId);
      report.devices.find(d => d.deviceId === device.deviceId)!.motionCalibration = motion;
    }
    
    // Phase 5: Validation
    for (const device of detectedDevices) {
      const validation = await this.validateCalibration(device.deviceId);
      report.devices.find(d => d.deviceId === device.deviceId)!.validation = validation;
    }
    
    report.status = 'completed';
    return report;
  }
}
```

## Usage Instructions

### Initial Setup
```bash
# 1. Connect devices via USB
npm run calibration:detect-devices

# 2. Run intrinsic calibration
npm run calibration:intrinsic --device=ipad-pro-001

# 3. Run spatial calibration
npm run calibration:spatial --devices=ipad-pro-001,iphone-15-pro-max-001,macbook-pro-001

# 4. Generate QR codes
npm run calibration:generate-qr --device=ipad-pro-001 --position=FRONT

# 5. Validate calibration
npm run calibration:validate --device=ipad-pro-001
```

### QR Code Workflow
1. **Generate QR codes** for each device/position combination
2. **Print or display** QR codes at designated positions
3. **Scan QR codes** with mobile devices for automatic calibration
4. **Verify calibration** through validation tests

### Troubleshooting
- **Device not detected**: Check USB connection and permissions
- **Calibration failed**: Ensure adequate lighting and pattern visibility
- **QR code not scanning**: Check camera focus and lighting conditions
- **Spatial accuracy low**: Verify device positions and distances

## Calibration Data Storage
```typescript
interface CalibrationDatabase {
  devices: Map<string, DeviceCalibration>;
  sessions: Map<string, CalibrationSession>;
  qrCodes: Map<string, QRCodeData>;
}

// Stored in IndexedDB for persistence
const calibrationDB = new CalibrationDatabase();
```

This protocol provides a complete framework for calibrating your multi-device setup, ensuring accurate motion tracking and spatial alignment across all devices.

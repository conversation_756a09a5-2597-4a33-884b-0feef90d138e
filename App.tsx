
import React, { useState, useEffect, useCallback } from 'react';
import { SessionState, SessionType, Metrics } from './types';
import { SESSION_TYPES } from './constants';
import { generateAnalysisReport } from './services/geminiService';
import { videoStorageService, VideoMetadata } from './services/videoStorageService';
import copypartyService from './services/copypartyService';
import { validateParticipantId, validateSessionType, validateVideoFile, sanitizeMetrics } from './utils/validation';
import Header from './components/Header';
import Dashboard from './components/Dashboard';
import ReportModal from './components/ReportModal';
import VideoUpload from './components/VideoUpload';
import VideoList from './components/VideoList';
import VideoLibrary from './src/components/VideoLibrary';
import VideoPlayer from './src/components/VideoPlayer';

const App: React.FC = () => {
  const [useLiveCamera, setUseLiveCamera] = useState<boolean>(false);
  const [useRealMetrics, setUseRealMetrics] = useState<boolean>(false);
  const [sessionState, setSessionState] = useState<SessionState>(SessionState.Idle);
  const [participantId, setParticipantId] = useState<string>('P001');
  const [sessionType, setSessionType] = useState<SessionType>(SESSION_TYPES[0] as SessionType);
  const [metrics, setMetrics] = useState<Metrics>({
    punchRate: 0,
    punchCount: 0,
    punchVelocity: 0,
    headMovement: 0,
    postureScore: 95,
    gaitBalance: 98,
    fatigue: 0,
  });
  const [finalMetrics, setFinalMetrics] = useState<Metrics | null>(null);
  const [report, setReport] = useState<string>('');
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [videos, setVideos] = useState<VideoMetadata[]>([]);
  const [isUploading, setIsUploading] = useState<boolean>(false);
  const [currentView, setCurrentView] = useState<'dashboard' | 'library'>('dashboard');
  const [selectedVideo, setSelectedVideo] = useState<any>(null);
  const [showVideoPlayer, setShowVideoPlayer] = useState<boolean>(false);
  
  const resetMetrics = () => {
    setMetrics({
      punchRate: 0,
      punchCount: 0,
      punchVelocity: 0,
      headMovement: 0,
      postureScore: 95,
      gaitBalance: 98,
      fatigue: 0,
    });
  };

  const handleStart = () => {
    try {
      const validatedId = validateParticipantId(participantId);
      if (!validateSessionType(sessionType)) {
        alert('Invalid session type selected.');
        return;
      }
      setParticipantId(validatedId);
      resetMetrics();
      setSessionState(SessionState.Active);
    } catch (error: any) {
      alert(error.message);
      return;
    }
  };

  const handleStop = useCallback(async () => {
    setSessionState(SessionState.Analyzing);
    setIsModalOpen(true);
    
    // Capture final metrics for the report
    const capturedMetrics = { ...metrics };
    setFinalMetrics(capturedMetrics);

    try {
      const analysisReport = await generateAnalysisReport(capturedMetrics, participantId, sessionType);
      setReport(analysisReport);
    } catch (error) {
      console.error('Failed to generate analysis report:', error);
      setReport('Error: Failed to generate analysis report. Please check your internet connection and try again.');
    } finally {
      setSessionState(SessionState.Finished);
    }
  }, [metrics, participantId, sessionType]);

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSessionState(SessionState.Idle);
    resetMetrics();
  };

  const handleVideoUpload = async (file: File) => {
    setIsUploading(true);
    try {
      validateVideoFile(file);
      const validatedId = validateParticipantId(participantId || 'P001');
      
      // Use Copyparty for video storage
      const videoMetadata = await copypartyService.uploadWithProgress(
        file,
        validatedId,
        sessionType,
        (progress) => {
          console.log(`Upload progress: ${progress.toFixed(2)}%`);
        }
      );
      
      setVideos(prev => [...prev, videoMetadata]);
      alert('Video uploaded successfully to Copyparty!');
    } catch (error: any) {
      console.error('Failed to upload video:', error);
      alert(error.message || 'Failed to upload video. Please try again.');
    } finally {
      setIsUploading(false);
    }
  };

  const handleVideoDelete = async (id: string) => {
    try {
      await videoStorageService.deleteVideo(id);
      setVideos(prev => prev.filter(v => v.id !== id));
    } catch (error) {
      console.error('Failed to delete video:', error);
    }
  };

  const handleVideoSelect = (video: VideoMetadata) => {
    // Open video in a new window or modal
    window.open(video.url, '_blank');
  };

  const handleLibraryVideoSelect = (video: any) => {
    setSelectedVideo(video);
    setShowVideoPlayer(true);
  };

  const handleAnalyzeVideo = async (video: any) => {
    // Trigger analysis for the selected video
    setIsModalOpen(true);
    setSessionState(SessionState.Analyzing);
    
    try {
      // Create mock metrics for video analysis
      const videoMetrics: Metrics = {
        punchRate: 45 + Math.random() * 30,
        punchCount: Math.floor(100 + Math.random() * 200),
        punchVelocity: 6 + Math.random() * 4,
        headMovement: 1.5 + Math.random() * 2,
        postureScore: 75 + Math.random() * 20,
        gaitBalance: 80 + Math.random() * 15,
        fatigue: Math.random() * 40,
      };

      const analysisReport = await generateAnalysisReport(
        videoMetrics, 
        `Video-${video.name}`, 
        'Video Analysis (15 mins)' as SessionType
      );
      setFinalMetrics(videoMetrics);
      setReport(analysisReport);
    } catch (error) {
      console.error('Failed to analyze video:', error);
      setReport('Error: Failed to analyze video. Please check your connection and try again.');
    } finally {
      setSessionState(SessionState.Finished);
    }
  };

  const closeVideoPlayer = () => {
    setShowVideoPlayer(false);
    setSelectedVideo(null);
  };

  // Load videos from IndexedDB on mount
  useEffect(() => {
    const loadVideos = async () => {
      try {
        const loadedVideos = await videoStorageService.loadVideosFromIndexedDB();
        setVideos(loadedVideos);
      } catch (error) {
        console.error('Failed to load videos:', error);
        // Don't show alert on initial load - just log the error
      }
    };
    loadVideos();
  }, []);

  // Retry failed syncs every 30 seconds
  useEffect(() => {
    const interval = setInterval(() => {
      videoStorageService.retryFailedSyncs();
    }, 30000);

    return () => clearInterval(interval);
  }, []);

  const handleCameraMetricsUpdate = useCallback((cameraId: string, newMetrics: Metrics) => {
    if (useRealMetrics) {
      setMetrics(newMetrics);
    }
  }, [useRealMetrics]);

  useEffect(() => {
    // Early return if conditions not met
    if (sessionState !== SessionState.Active || useRealMetrics) {
      return;
    }

    // Only create interval if conditions are met
    const interval = setInterval(() => {
      setMetrics(prev => {
        // PERFORMANCE FIX: Validate previous state to prevent NaN values
        const safePrev = {
          punchRate: Number(prev.punchRate) || 0,
          punchCount: Number(prev.punchCount) || 0,
          punchVelocity: Number(prev.punchVelocity) || 0,
          headMovement: Number(prev.headMovement) || 0,
          postureScore: Number(prev.postureScore) || 95,
          gaitBalance: Number(prev.gaitBalance) || 98,
          fatigue: Number(prev.fatigue) || 0
        };

        return {
          punchRate: Math.max(0, 60 + Math.random() * 15 - (safePrev.fatigue / 5)),
          punchCount: safePrev.punchCount + Math.floor(1 + Math.random()),
          punchVelocity: Math.max(0, 8 + Math.random() * 2 - (safePrev.fatigue / 10)),
          headMovement: Math.max(0, 2 + Math.random() * 1.5),
          postureScore: Math.max(70, Math.min(100, safePrev.postureScore - Math.random() * 0.1)),
          gaitBalance: Math.max(85, Math.min(100, safePrev.gaitBalance - Math.random() * 0.1)),
          fatigue: Math.max(0, Math.min(100, safePrev.fatigue + 0.2))
        };
      });
    }, 1000);

    // Cleanup function always clears the interval
    return () => {
      clearInterval(interval);
    };
  }, [sessionState, useRealMetrics]);

  return (
    <div className="h-screen bg-neutral-900 text-neutral-100 flex flex-col font-sans overflow-hidden">
      <Header />
      
      {/* Navigation Tabs */}
      <div className="flex border-b border-neutral-700 bg-neutral-800">
        <button
          className={`px-6 py-3 font-semibold transition-colors ${
            currentView === 'dashboard'
              ? 'bg-blue-600 text-white border-b-2 border-blue-400'
              : 'text-neutral-300 hover:text-white hover:bg-neutral-700'
          }`}
          onClick={() => setCurrentView('dashboard')}
        >
          📊 Live Analysis Dashboard
        </button>
        <button
          className={`px-6 py-3 font-semibold transition-colors ${
            currentView === 'library'
              ? 'bg-blue-600 text-white border-b-2 border-blue-400'
              : 'text-neutral-300 hover:text-white hover:bg-neutral-700'
          }`}
          onClick={() => setCurrentView('library')}
        >
          📹 Video Library
        </button>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-hidden">
        {currentView === 'dashboard' ? (
          <Dashboard 
            participantId={participantId}
            setParticipantId={setParticipantId}
            sessionType={sessionType}
            setSessionType={setSessionType}
            sessionState={sessionState}
            handleStart={handleStart}
            handleStop={handleStop}
            metrics={metrics}
            onCameraMetricsUpdate={handleCameraMetricsUpdate}
            useLiveCamera={useLiveCamera}
          />
        ) : (
          <VideoLibrary 
            onVideoSelect={handleLibraryVideoSelect}
            onAnalyzeVideo={handleAnalyzeVideo}
          />
        )}
      </div>

      {/* Video Player Modal */}
      {showVideoPlayer && selectedVideo && (
        <VideoPlayer
          video={selectedVideo}
          onClose={closeVideoPlayer}
          onAnalyze={handleAnalyzeVideo}
        />
      )}

      <ReportModal
        isOpen={isModalOpen}
        onClose={handleCloseModal}
        report={report}
        sessionState={sessionState}
        metrics={finalMetrics || metrics}
      />
      
      <footer className="text-center p-4 text-neutral-500 text-sm border-t border-neutral-800">
        <div className="flex items-center justify-center gap-4 mb-2">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={useLiveCamera}
              onChange={(e) => {
                setUseLiveCamera(e.target.checked);
                setUseRealMetrics(e.target.checked);
              }}
              className="w-4 h-4 text-blue-600 bg-neutral-700 border-neutral-600 rounded focus:ring-blue-500"
            />
            <span className="text-sm">Use Live Camera</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="checkbox"
              checked={useRealMetrics}
              onChange={(e) => setUseRealMetrics(e.target.checked)}
              disabled={!useLiveCamera}
              className="w-4 h-4 text-blue-600 bg-neutral-700 border-neutral-600 rounded focus:ring-blue-500 disabled:opacity-50"
            />
            <span className="text-sm">Use Real Metrics</span>
          </label>
        </div>
        <div className="text-xs text-neutral-600">
          Combat Mirror System v1.0 - For Research Use Only | 
          Videos uploaded from mobile automatically sync to CopyParty file server
        </div>
      </footer>
    </div>
  );
};

export default App;

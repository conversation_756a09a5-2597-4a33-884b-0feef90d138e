# Mobile Camera Setup Guide

## 🎯 Quick Fix for Camera Access Issues

The iPhone camera access issue has been **RESOLVED** by enabling HTTPS support. Here's what was fixed:

### ❌ **Previous Issue:**
- Error: `undefined is not an object evaluating navigator.mediaDevices.getUserMedia`
- Caused by HTTP access - mobile browsers require HTTPS for camera permissions

### ✅ **Solution Applied:**
1. **Enabled HTTPS on Vite server** with self-signed certificates
2. **Updated camera-stream.html** with better error handling
3. **Modified LiveKit service** to generate HTTPS URLs
4. **Enhanced startup script** for HTTPS support

## 📱 **How to Connect Your iPhone:**

### **Method 1: QR Code Scanning (Recommended)**
1. Open the web interface: **https://************:5173**
2. Toggle "Use Live Camera" mode
3. You'll see QR codes for each camera position
4. **Open iPhone Camera app** and scan the QR code
5. **Tap the notification** to open the camera stream page
6. **Accept HTTPS certificate warning** (self-signed certificate)
7. **Allow camera permissions** when prompted

### **Method 2: Direct URL**
1. On your iPhone, go to: **https://************:5173/camera-stream.html**
2. Add URL parameters: `?room=test&token=test&position=FRONT`
3. Accept certificate warning and camera permissions

## 🔧 **Current System URLs:**

- **Desktop Interface**: https://localhost:5173
- **Mobile Access**: https://************:5173
- **LiveKit Server**: ws://localhost:7880

## ⚠️ **Important Notes:**

### **HTTPS Certificate Warning**
- You'll see a "Not Secure" or certificate warning
- This is normal for self-signed certificates
- **Tap "Advanced" → "Proceed to site"** or similar option
- This is safe for local development

### **Camera Permissions**
- **Always allow camera access** when prompted
- If denied, go to iPhone Settings → Safari → Camera → Allow
- Or try refreshing the page and allow again

### **Troubleshooting Steps:**

1. **If QR code scanning doesn't work:**
   - Manually type the URL in Safari
   - Make sure you're using **https://** not **http://**

2. **If camera access is denied:**
   - Check iPhone Settings → Safari → Camera
   - Try refreshing the page
   - Close and reopen Safari

3. **If connection fails:**
   - Ensure you're on the same WiFi network
   - Check that IP address ************ is correct
   - Try accessing the desktop interface first

4. **If video doesn't appear:**
   - Check LiveKit server is running: `docker ps`
   - Look for errors in browser console (Safari → Develop → Console)

## 🎥 **Expected Behavior:**

When working correctly, you should see:
1. ✅ Camera permission granted
2. ✅ Video feed appears in iPhone browser
3. ✅ "Connected: FRONT Camera" status (or your camera position)
4. ✅ Video stream appears in the desktop interface
5. ✅ Controls for switching cameras and disconnecting

## 🚀 **System Status Check:**

```bash
# Check if system is running
curl -k https://localhost:5173
docker ps | grep combat-mirror
tmux list-sessions | grep combat-mirror-livekit
```

## 📞 **Still Having Issues?**

Check the tmux session for real-time feedback:
```bash
tmux attach -t combat-mirror-livekit
```

Or check the logs:
```bash
tail -f web.log
docker logs combat-mirror-livekit
```
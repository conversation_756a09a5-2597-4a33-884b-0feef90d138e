# Combat Mirror System - Architecture Enhancement Plan

## Executive Summary

Based on the analysis of the Combat Mirror System, PunchTracker-AI-Boxing-Assistant, and Dance-Recognition-AI-App, this document outlines strategic enhancements to improve the Combat Mirror System's punch detection, analysis capabilities, and overall architecture.

## Current State Analysis

### Combat Mirror System Strengths
- Multi-camera support (4 positions)
- LiveKit WebRTC integration for real-time streaming
- Local-first architecture with IndexedDB
- Comprehensive metrics tracking
- Session management system
- MediaPipe integration

### Areas for Enhancement
1. **Punch Detection Algorithm** - Currently uses simple velocity threshold (3.0 m/s)
2. **Punch Classification** - Limited punch type detection
3. **AI Analysis** - Basic metric reporting to Gemini
4. **Visualization** - Static skeleton rendering
5. **Performance Tracking** - Limited combo detection and fatigue analysis

## Proposed Enhancements

### 1. Advanced Punch Detection & Classification System

#### Implementation from PunchTracker
Integrate PunchTracker's sophisticated punch detection algorithm that includes:

```typescript
// services/enhancedPunchDetectionService.ts
interface PunchDetectionConfig {
  velocityThreshold: number;        // Dynamic threshold (default: 50 px/frame)
  directionThreshold: number;       // Cosine similarity (default: 0.7)
  punchCooldown: number;            // Prevent rapid detections (default: 0.5s)
  positionHistoryLength: number;    // Tracking history (default: 10 frames)
}

interface PunchType {
  JAB: 'jab';
  CROSS: 'cross';
  HOOK: 'hook';
  UPPERCUT: 'uppercut';
}

class EnhancedPunchDetectionService {
  // Position history tracking for velocity calculation
  private positionHistory: Map<string, Array<Position>>;
  private timestampHistory: Map<string, Array<number>>;
  
  // Punch classification based on:
  // - Arm extension ratio (>0.8 for straight punches)
  // - Wrist position relative to elbow/shoulder
  // - Trajectory analysis
  classifyPunchType(keypoints: Keypoints, hand: 'left' | 'right'): PunchType {
    const armExtensionRatio = this.calculateArmExtension(keypoints, hand);
    const wristTrajectory = this.analyzeTrajectory(hand);
    
    if (this.isUppercut(keypoints, hand)) return 'uppercut';
    if (this.isHook(keypoints, hand, armExtensionRatio)) return 'hook';
    if (hand === 'right' && armExtensionRatio > 0.8) return 'cross';
    return 'jab';
  }
  
  // Velocity calculation with calibration support
  calculateVelocity(positions: Position[], timestamps: number[]): VelocityVector {
    // Multi-frame averaging for stability
    // Direction vector calculation
    // Calibration multiplier application
  }
}
```

### 2. Combo Detection System

#### New Feature: Real-time Combo Recognition
Based on PunchTracker's FEATURE_IDEAS.md:

```typescript
// services/comboDetectionService.ts
interface ComboPattern {
  id: string;
  name: string;
  sequence: PunchType[];
  maxTimeBetweenPunches: number; // milliseconds
  minTimeBetweenPunches: number; // milliseconds
}

class ComboDetectionService {
  private punchHistory: Array<{type: PunchType, timestamp: number}>;
  private predefinedCombos: ComboPattern[] = [
    { id: 'jab-cross', name: 'One-Two', sequence: ['jab', 'cross'], maxTime: 800, minTime: 200 },
    { id: 'jab-cross-hook', name: 'Classic Combo', sequence: ['jab', 'cross', 'hook'], maxTime: 1200, minTime: 200 },
    { id: 'double-jab-cross', name: 'Double Jab Cross', sequence: ['jab', 'jab', 'cross'], maxTime: 1000, minTime: 150 }
  ];
  
  detectCombo(): DetectedCombo | null {
    // Pattern matching against recent punch history
    // Timing validation
    // Accuracy scoring
  }
}
```

### 3. Enhanced Motion Summarization

#### Integration from Dance-Recognition-AI-App
Adapt the motion summarization approach for combat-specific insights:

```typescript
// services/motionSummarizationService.ts
class MotionSummarizationService {
  summarizeSession(metrics: SessionMetrics, punchData: PunchData[]): string {
    const summary = [];
    
    // Movement analysis (from Dance app)
    const leftHandMovement = this.calculateMovementMetric(punchData, 'left');
    const rightHandMovement = this.calculateMovementMetric(punchData, 'right');
    
    // Combat-specific insights
    if (leftHandMovement > 2 || rightHandMovement > 2) {
      summary.push("High activity detected with significant arm movement");
    }
    
    // Punch pattern analysis
    const dominantHand = this.identifyDominantHand(punchData);
    summary.push(`${dominantHand} hand dominant with ${punchData.length} total punches`);
    
    // Fatigue indicators
    if (this.detectFatiguePattern(metrics)) {
      summary.push("Performance decline detected in final third of session");
    }
    
    return summary.join('. ');
  }
  
  private calculateMovementMetric(data: PunchData[], hand: string): number {
    // Adapted from Dance app's movement_metric function
    return data.reduce((sum, curr, i) => {
      if (i === 0) return 0;
      const prev = data[i-1];
      return sum + Math.sqrt(
        Math.pow(curr.x - prev.x, 2) + 
        Math.pow(curr.y - prev.y, 2)
      );
    }, 0);
  }
}
```

### 4. Advanced Visualization Features

#### Skeleton Customization (from Dance app)
```typescript
// components/SkeletonCustomization.tsx
interface SkeletonColors {
  lineColor: string;
  dotColor: string;
  punchTrailColor: string;
  comboHighlightColor: string;
}

const SkeletonCustomization: React.FC = () => {
  const [colors, setColors] = useState<SkeletonColors>({
    lineColor: '#00FF00',
    dotColor: '#FF0000',
    punchTrailColor: '#FFD700',
    comboHighlightColor: '#FF00FF'
  });
  
  return (
    <div className="skeleton-customization">
      <ColorPicker label="Skeleton Lines" value={colors.lineColor} 
                   onChange={(color) => setColors({...colors, lineColor: color})} />
      <ColorPicker label="Joint Points" value={colors.dotColor} 
                   onChange={(color) => setColors({...colors, dotColor: color})} />
      {/* Additional customization options */}
    </div>
  );
};
```

#### Animation Replay System
```typescript
// components/SessionReplay.tsx
interface ReplayFrame {
  timestamp: number;
  keypoints: Keypoints;
  detectedPunches: Punch[];
  metrics: FrameMetrics;
}

class SessionReplayService {
  private frames: ReplayFrame[] = [];
  private currentIndex: number = 0;
  
  playback(speed: number = 1.0) {
    // Animated skeleton replay
    // Punch visualization with trails
    // Metrics overlay
  }
  
  exportToVideo(): Blob {
    // Generate video file of session with overlays
  }
}
```

### 5. Target Mode / Gamification

#### Interactive Training System
```typescript
// services/targetModeService.ts
interface Target {
  id: string;
  position: { x: number, y: number };
  type: PunchType | 'any';
  size: number;
  appearanceTime: number;
  expirationTime: number;
  points: number;
}

class TargetModeService {
  private targets: Target[] = [];
  private score: number = 0;
  private difficulty: 'beginner' | 'intermediate' | 'advanced' = 'beginner';
  
  generateTarget(): Target {
    // Position based on camera view and user stance
    // Type based on training mode
    // Timing based on difficulty
  }
  
  checkHit(punchPosition: Position, punchType: PunchType): HitResult {
    // Proximity detection
    // Type matching
    // Score calculation
  }
  
  getTrainingModes() {
    return [
      { id: 'reaction', name: 'Reaction Training', description: 'Hit targets as quickly as possible' },
      { id: 'combo', name: 'Combo Practice', description: 'Execute specific combinations' },
      { id: 'accuracy', name: 'Accuracy Focus', description: 'Hit center of targets for max points' },
      { id: 'endurance', name: 'Endurance Mode', description: 'Continuous targets for stamina' }
    ];
  }
}
```

### 6. Enhanced AI Analysis Pipeline

#### Improved Gemini Integration
```typescript
// services/enhancedGeminiService.ts
class EnhancedGeminiService extends GeminiService {
  async generateAdvancedReport(
    sessionData: SessionData,
    motionSummary: string,
    comboStats: ComboStatistics,
    comparisonData?: HistoricalData
  ): Promise<DetailedReport> {
    const prompt = this.buildEnhancedPrompt({
      // Contextual motion summary (from Dance app approach)
      motionNarrative: motionSummary,
      
      // Detailed punch breakdown
      punchAnalysis: {
        total: sessionData.totalPunches,
        byType: sessionData.punchCounts,
        velocity: sessionData.avgVelocity,
        accuracy: sessionData.accuracy
      },
      
      // Combo performance
      comboPerformance: {
        attempted: comboStats.attempted,
        successful: comboStats.successful,
        accuracy: comboStats.accuracy,
        favoriteCombo: comboStats.mostUsed
      },
      
      // Comparative analysis
      improvement: comparisonData ? {
        punchRateChange: this.calculateChange(comparisonData.punchRate, sessionData.punchRate),
        accuracyChange: this.calculateChange(comparisonData.accuracy, sessionData.accuracy),
        fatigueResistance: this.calculateFatigueImprovement(comparisonData, sessionData)
      } : null
    });
    
    return await this.callGeminiAPI(prompt);
  }
}
```

### 7. Performance Optimization

#### TensorFlow Model Management (from PunchTracker)
```typescript
// services/modelManager.ts
class ModelManager {
  private static instance: ModelManager;
  private poseModel: any;
  private modelType: 'lightning' | 'thunder' = 'lightning';
  
  // Singleton pattern to prevent multiple model loads
  static getInstance(): ModelManager {
    if (!ModelManager.instance) {
      ModelManager.instance = new ModelManager();
    }
    return ModelManager.instance;
  }
  
  async loadModel() {
    if (!this.poseModel) {
      // Load once and reuse
      this.poseModel = await this.loadMoveNet(this.modelType);
    }
    return this.poseModel;
  }
}
```

## Implementation Roadmap

### Phase 1: Core Enhancements (Week 1-2)
1. Integrate enhanced punch detection algorithm
2. Implement punch classification system
3. Add combo detection service
4. Update metrics calculation

### Phase 2: Visualization & UX (Week 3-4)
1. Add skeleton customization options
2. Implement session replay system
3. Create punch trail visualizations
4. Add real-time combo feedback

### Phase 3: Gamification (Week 5-6)
1. Develop target mode system
2. Create training modes
3. Implement scoring system
4. Add achievement tracking

### Phase 4: AI & Analytics (Week 7-8)
1. Enhance motion summarization
2. Improve Gemini prompts
3. Add comparative analysis
4. Implement predictive insights

### Phase 5: Optimization & Testing (Week 9-10)
1. Optimize model loading
2. Performance profiling
3. User testing
4. Bug fixes and refinements

## Technical Considerations

### Performance
- Implement model singleton pattern to prevent reloading
- Use Web Workers for heavy computations
- Optimize frame processing pipeline
- Implement adaptive quality based on device capabilities

### Scalability
- Design services as independent modules
- Use dependency injection for flexibility
- Implement feature flags for gradual rollout
- Create abstraction layers for AI providers

### Testing Strategy
- Unit tests for punch detection algorithms
- Integration tests for combo detection
- E2E tests for user workflows
- Performance benchmarks for frame processing

## Success Metrics

1. **Punch Detection Accuracy**: >95% accuracy for standard punches
2. **Combo Recognition Rate**: >90% for predefined combinations
3. **Frame Processing Speed**: Maintain 30 FPS on mid-range devices
4. **User Engagement**: 50% increase in session duration
5. **Training Effectiveness**: Measurable improvement in user metrics over time

## Conclusion

These enhancements will transform the Combat Mirror System into a comprehensive training platform that combines the best aspects of computer vision, gamification, and AI analysis. The integration of features from PunchTracker and Dance-Recognition projects will provide users with professional-grade training tools while maintaining the system's ease of use and accessibility.

The modular architecture ensures that features can be implemented incrementally, allowing for continuous improvement and user feedback integration throughout the development process.

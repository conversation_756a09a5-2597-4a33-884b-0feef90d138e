#!/bin/bash

# Combat Mirror iOS App - Production Build Script
# This script guides you through building the iOS app for production

echo "🚀 Combat Mirror iOS Production Build"
echo "====================================="

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

# iOS app directory
IOS_APP_DIR="/Users/<USER>/Documents/CombatMirrorProjects/combatmirroranalysis/combat-mirror-mobile"

echo -e "\n${YELLOW}Prerequisites:${NC}"
echo "1. Node.js 18+ installed"
echo "2. EAS CLI installed (npm install -g eas-cli)"
echo "3. Apple Developer account"
echo "4. Expo account (free)"
echo ""

# Step 1: Navigate to iOS app directory
echo -e "\n${YELLOW}Step 1: Navigate to iOS app directory${NC}"
echo "cd $IOS_APP_DIR"

# Step 2: Install dependencies
echo -e "\n${YELLOW}Step 2: Install dependencies${NC}"
echo "npm install"

# Step 3: Login to EAS
echo -e "\n${YELLOW}Step 3: Login to EAS${NC}"
echo "eas login"
echo "(Use your Expo account credentials)"

# Step 4: Configure EAS build
echo -e "\n${YELLOW}Step 4: Ensure EAS is configured${NC}"
echo "The app already has an EAS project ID: b6df7343-053a-492b-869d-45a0d9a3e3dd"

# Step 5: Create/Update production environment file
echo -e "\n${YELLOW}Step 5: Create production environment file${NC}"
cat << 'EOF' > $IOS_APP_DIR/.env.production
# Production Environment Variables
EXPO_PUBLIC_API_URL=https://combat-mirror-system.up.railway.app/api
EXPO_PUBLIC_LIVEKIT_URL=wss://combat-mirror-livekit.up.railway.app
EXPO_PUBLIC_GEMINI_API_KEY=your_production_gemini_key_here
EOF
echo "Created .env.production file"
echo -e "${RED}IMPORTANT: Update EXPO_PUBLIC_GEMINI_API_KEY with your production key${NC}"

# Step 6: Update eas.json for production
echo -e "\n${YELLOW}Step 6: Create/Update eas.json for production${NC}"
cat << 'EOF' > $IOS_APP_DIR/eas.json
{
  "cli": {
    "version": ">= 3.0.0"
  },
  "build": {
    "development": {
      "developmentClient": true,
      "distribution": "internal",
      "ios": {
        "resourceClass": "m-medium"
      }
    },
    "preview": {
      "distribution": "internal",
      "ios": {
        "resourceClass": "m-medium"
      }
    },
    "production": {
      "ios": {
        "resourceClass": "m-large",
        "buildConfiguration": "Release",
        "env": {
          "EXPO_PUBLIC_ENV": "production"
        }
      },
      "env": {
        "NODE_ENV": "production"
      }
    }
  },
  "submit": {
    "production": {
      "ios": {
        "appleId": "<EMAIL>",
        "ascAppId": "your-app-store-connect-app-id",
        "appleTeamId": "your-apple-team-id"
      }
    }
  }
}
EOF
echo "Created eas.json file"
echo -e "${RED}IMPORTANT: Update the submit section with your Apple credentials${NC}"

# Step 7: Build commands
echo -e "\n${YELLOW}Step 7: Production Build Commands${NC}"
echo -e "\n${GREEN}Option 1: Build for TestFlight/App Store${NC}"
echo "eas build --platform ios --profile production"

echo -e "\n${GREEN}Option 2: Build for internal testing${NC}"
echo "eas build --platform ios --profile preview"

echo -e "\n${GREEN}Option 3: Build locally (requires Xcode)${NC}"
echo "npx expo prebuild"
echo "cd ios && pod install"
echo "open ios/CombatMirrorCamera.xcworkspace"
echo "# Then build in Xcode"

echo -e "\n${YELLOW}Step 8: Submit to App Store (after successful build)${NC}"
echo "eas submit --platform ios --latest"

echo -e "\n${YELLOW}Additional Configuration:${NC}"
echo "1. Update app.json version and buildNumber before each submission"
echo "2. Ensure all app icons and splash screens are present in assets/"
echo "3. Update bundle identifier if needed (currently: com.aliaslabs.combatmirrorsystem)"
echo "4. Configure push notifications if needed"

echo -e "\n${YELLOW}Build Process:${NC}"
echo "1. Run this script to see all commands"
echo "2. Navigate to: cd $IOS_APP_DIR"
echo "3. Update environment variables in .env.production"
echo "4. Update Apple credentials in eas.json"
echo "5. Run: eas build --platform ios --profile production"
echo "6. Wait for build to complete (usually 20-30 minutes)"
echo "7. Download the .ipa file or submit directly to App Store"

echo -e "\n${GREEN}✅ Build script complete!${NC}"
echo "Follow the steps above to build your iOS app for production."
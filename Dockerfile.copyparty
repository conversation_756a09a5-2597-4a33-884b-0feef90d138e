FROM python:3.11-slim

RUN apt-get update && apt-get install -y curl && rm -rf /var/lib/apt/lists/*

RUN pip install --no-cache-dir copyparty

WORKDIR /srv

RUN mkdir -p /srv/uploads /srv/recordings /srv/exports /srv/.hist /srv/config /srv/.th

RUN chmod -R 755 /srv

EXPOSE 3923

HEALTHCHECK --interval=30s --timeout=10s --start-period=20s --retries=3 CMD curl -f http://localhost:3923/ || exit 1

ENTRYPOINT ["copyparty"]
CMD ["-i", "::", "-p", "3923", "--rw", "/srv/uploads,/srv/recordings,/srv/exports", "--cors", "--theme", "2", "--name", "Combat Mirror File Server"]

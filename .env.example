# Combat Mirror System - Environment Configuration Template
# Copy this file to .env and fill in your actual values
# NEVER commit .env files to version control

# =============================================================================
# CRITICAL SECURITY SETTINGS
# =============================================================================

# Gemini AI API Key (Required for analysis reports)
GEMINI_API_KEY=your_gemini_api_key_here

# LiveKit Configuration (Server-side only - NEVER expose in client)
LIVEKIT_API_KEY=your_livekit_api_key
LIVEKIT_API_SECRET=your_livekit_api_secret
LIVEKIT_URL=ws://localhost:7880

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================

# Environment
NODE_ENV=development
LOG_LEVEL=info

# Server Configuration
PORT=3001
API_URL=http://localhost:3001

# Frontend Configuration (Safe for client-side)
FRONTEND_URL=http://localhost:5173
VITE_API_URL=http://localhost:3001
VITE_LIVEKIT_URL=ws://localhost:7880

# =============================================================================
# SECURITY SETTINGS
# =============================================================================

# CORS Configuration
CORS_ORIGIN=http://localhost:5173,http://localhost:3000
CORS_DEBUG=false

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload Limits
MAX_VIDEO_SIZE_MB=500
VITE_MAX_VIDEO_SIZE_MB=50

# Session Security
SESSION_SECRET=your_session_secret_here
JWT_SECRET=your_jwt_secret_here
JWT_EXPIRES_IN=24h
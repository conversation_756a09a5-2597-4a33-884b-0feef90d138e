#!/bin/bash

# Combat Mirror System - Development Start Script

echo "🥊 Starting Combat Mirror System Development Environment..."

# Check if .env files exist
if [ ! -f ".env.local" ]; then
    echo "⚠️  Creating .env.local from template..."
    cp .env.example .env.local
    echo "📝 Please edit .env.local with your configuration"
fi

if [ ! -f "server/.env" ]; then
    echo "⚠️  Creating server/.env from template..."
    cp server/.env.example server/.env
    echo "📝 Please edit server/.env and add your GEMINI_API_KEY"
fi

# Function to check if port is in use
check_port() {
    if lsof -Pi :$1 -sTCP:LISTEN -t >/dev/null ; then
        echo "❌ Port $1 is already in use"
        return 1
    else
        echo "✅ Port $1 is available"
        return 0
    fi
}

# Check required ports
echo -e "\n🔍 Checking port availability..."
FRONTEND_PORT=5176
BACKEND_PORT=3001

if ! check_port $BACKEND_PORT; then
    echo "Please stop the process using port $BACKEND_PORT"
    exit 1
fi

# Start backend server
echo -e "\n🚀 Starting backend server on port $BACKEND_PORT..."
cd server
npm install
node index.js &
BACKEND_PID=$!
cd ..

# Wait for backend to start
echo "⏳ Waiting for backend to start..."
sleep 3

# Check if backend is running
if curl -s http://localhost:$BACKEND_PORT/api/health > /dev/null; then
    echo "✅ Backend server is running"
else
    echo "❌ Backend server failed to start"
    kill $BACKEND_PID 2>/dev/null
    exit 1
fi

# Start frontend
echo -e "\n🎨 Starting frontend development server..."
npm install
npm run dev &
FRONTEND_PID=$!

# Function to cleanup on exit
cleanup() {
    echo -e "\n🛑 Shutting down servers..."
    kill $BACKEND_PID 2>/dev/null
    kill $FRONTEND_PID 2>/dev/null
    echo "👋 Goodbye!"
    exit 0
}

# Set up cleanup on script exit
trap cleanup EXIT INT TERM

# Wait and show status
echo -e "\n✨ Combat Mirror System is starting up!"
echo "📍 Frontend: https://localhost:$FRONTEND_PORT"
echo "📍 Backend API: http://localhost:$BACKEND_PORT"
echo "📍 API Health: http://localhost:$BACKEND_PORT/api/health"
echo -e "\n💡 Press Ctrl+C to stop all servers\n"

# Keep script running
wait